"""
API v3路由 - 使用动态字段管理器
实施迁移的第一步，提供新的API接口
"""

from flask import Blueprint, request, jsonify, render_template
import logging
from app.services.enhanced_data_source_manager import get_enhanced_manager
from app.services.dynamic_field_manager import get_field_manager
import os

logger = logging.getLogger(__name__)

# 创建API v3蓝图
api_v3 = Blueprint('api_v3', __name__, url_prefix='/api/v3')

@api_v3.route('/tables', methods=['GET'])
def get_supported_tables():
    """获取支持的表列表 - 动态发现"""
    try:
        manager = get_enhanced_manager()
        result = manager.get_supported_tables()
        
        logger.info(f"✅ API v3: 获取支持表列表成功")
        return jsonify(result)
        
    except Exception as e:
        logger.error(f"❌ API v3: 获取支持表列表失败: {e}")
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500

@api_v3.route('/tables/<table_name>/columns', methods=['GET'])
def get_table_columns_v3(table_name):
    """获取表字段信息 - 动态发现"""
    try:
        manager = get_enhanced_manager()
        result = manager.get_table_columns(table_name)
        
        if result['success']:
            logger.info(f"✅ API v3: 获取表字段成功 - {table_name}: {len(result['columns'])}个字段")
        else:
            logger.warning(f"⚠️ API v3: 获取表字段失败 - {table_name}: {result.get('error')}")
            
        return jsonify(result)
        
    except Exception as e:
        logger.error(f"❌ API v3: 获取表字段异常 - {table_name}: {e}")
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500

@api_v3.route('/tables/<table_name>/info', methods=['GET'])
def get_table_info_v3(table_name):
    """获取表完整信息 - 包含字段、类型、配置等"""
    try:
        manager = get_enhanced_manager()
        table_info = manager.field_manager.get_table_info(table_name)

        if table_info:
            logger.info(f"✅ API v3: 获取表信息成功 - {table_name}")
            return jsonify({
                'success': True,
                'table_name': table_name,
                'table_info': table_info
            })
        else:
            return jsonify({
                'success': False,
                'error': f'表不存在: {table_name}'
            }), 404

    except Exception as e:
        logger.error(f"❌ API v3: 获取表信息失败 - {table_name}: {e}")
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500

@api_v3.route('/tables/<table_name>/data', methods=['GET'])
def get_table_data_v3(table_name):
    """获取表数据 - 动态字段支持"""
    try:
        # 获取请求参数
        page = request.args.get('page', 1, type=int)
        per_page = request.args.get('per_page', 50, type=int)

        # 处理筛选条件
        filters = []
        filters_param = request.args.get('filters')
        if filters_param:
            import json
            filters = json.loads(filters_param)

        # 处理排序条件
        sort_by = request.args.get('sort_by', '')
        sort_order = request.args.get('sort_order', 'asc')

        # 处理搜索条件
        search = request.args.get('search', '')
        if search:
            # 添加全文搜索到筛选条件
            filters.append({
                'field': '_global_search',
                'operator': 'contains',
                'value': search
            })

        manager = get_enhanced_manager()
        result = manager.get_table_data(table_name, page, per_page, filters, sort_by, sort_order)

        if result['success']:
            logger.info(f"✅ API v3: 获取表数据成功 - {table_name}: {result['total']}条记录")
        else:
            logger.warning(f"⚠️ API v3: 获取表数据失败 - {table_name}: {result.get('error')}")

        return jsonify(result)

    except Exception as e:
        logger.error(f"❌ API v3: 获取表数据异常 - {table_name}: {e}")
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500

@api_v3.route('/tables/<table_name>/data', methods=['POST'])
def create_record_v3(table_name):
    """创建记录 - 动态字段支持"""
    try:
        data = request.get_json()
        if not data:
            return jsonify({
                'success': False,
                'error': '缺少数据'
            }), 400
        
        manager = get_enhanced_manager()
        result = manager.create_record(table_name, data)
        
        if result['success']:
            logger.info(f"✅ API v3: 创建记录成功 - {table_name}: ID {result.get('record_id')}")
        else:
            logger.warning(f"⚠️ API v3: 创建记录失败 - {table_name}: {result.get('error')}")
            
        return jsonify(result)
        
    except Exception as e:
        logger.error(f"❌ API v3: 创建记录异常 - {table_name}: {e}")
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500

@api_v3.route('/tables/<table_name>/data/<record_id>', methods=['PUT'])
def update_record_v3(table_name, record_id):
    """更新记录 - 动态字段支持"""
    try:
        data = request.get_json()
        if not data:
            return jsonify({
                'success': False,
                'error': '缺少数据'
            }), 400

        # 获取表信息以确定正确的主键字段
        manager = get_enhanced_manager()
        table_info = manager.field_manager.get_table_info(table_name)

        if table_info:
            primary_key = table_info.get('primary_key', 'id')
        else:
            primary_key = 'id'  # 默认主键

        # 添加记录ID到数据中，使用正确的主键字段名
        data[primary_key] = record_id

        result = manager.update_record(table_name, data)

        if result['success']:
            logger.info(f"✅ API v3: 更新记录成功 - {table_name}: {result.get('affected_rows')}行")
        else:
            logger.warning(f"⚠️ API v3: 更新记录失败 - {table_name}: {result.get('error')}")

        return jsonify(result)

    except Exception as e:
        logger.error(f"❌ API v3: 更新记录异常 - {table_name}: {e}")
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500

@api_v3.route('/tables/<table_name>/data/<record_id>', methods=['DELETE'])
def delete_record_v3(table_name, record_id):
    """删除记录"""
    try:
        manager = get_enhanced_manager()
        result = manager.delete_record(table_name, record_id)

        if result['success']:
            logger.info(f"✅ API v3: 删除记录成功 - {table_name}: ID {record_id}")
        else:
            logger.warning(f"⚠️ API v3: 删除记录失败 - {table_name}: {result.get('error')}")

        return jsonify(result)

    except Exception as e:
        logger.error(f"❌ API v3: 删除记录异常 - {table_name}: {e}")
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500

@api_v3.route('/tables/<table_name>/data/batch', methods=['DELETE'])
def batch_delete_records_v3(table_name):
    """批量删除记录 - 动态字段支持"""
    try:
        data = request.get_json()
        if not data or 'ids' not in data:
            return jsonify({
                'success': False,
                'error': '缺少要删除的记录ID列表'
            }), 400

        ids = data['ids']
        if not isinstance(ids, list) or len(ids) == 0:
            return jsonify({
                'success': False,
                'error': 'ID列表不能为空'
            }), 400

        manager = get_enhanced_manager()
        result = manager.batch_delete_records(table_name, ids)

        if result['success']:
            logger.info(f"✅ API v3: 批量删除成功 - {table_name}: {result.get('deleted_count')}条记录")
        else:
            logger.warning(f"⚠️ API v3: 批量删除失败 - {table_name}: {result.get('error')}")

        return jsonify(result)

    except Exception as e:
        logger.error(f"❌ API v3: 批量删除异常 - {table_name}: {e}")
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500

@api_v3.route('/tables/<table_name>/validate', methods=['GET'])
def validate_table_mapping_v3(table_name):
    """验证表字段映射"""
    try:
        field_manager = get_field_manager()
        result = field_manager.validate_field_mapping(table_name)
        
        logger.info(f"✅ API v3: 验证字段映射 - {table_name}: {result.get('match_rate', 0)}%匹配")
        return jsonify(result)
        
    except Exception as e:
        logger.error(f"❌ API v3: 验证字段映射异常 - {table_name}: {e}")
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500

@api_v3.route('/config/tables/<table_name>', methods=['POST'])
def override_table_config_v3(table_name):
    """覆盖表配置"""
    try:
        config = request.get_json()
        if not config:
            return jsonify({
                'success': False,
                'error': '缺少配置数据'
            }), 400
        
        manager = get_enhanced_manager()
        result = manager.override_table_config(table_name, config)
        
        logger.info(f"✅ API v3: 覆盖表配置 - {table_name}")
        return jsonify(result)
        
    except Exception as e:
        logger.error(f"❌ API v3: 覆盖表配置异常 - {table_name}: {e}")
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500

@api_v3.route('/cache/clear', methods=['POST'])
def clear_cache_v3():
    """清理缓存"""
    try:
        manager = get_enhanced_manager()
        manager.clear_cache()
        
        logger.info("✅ API v3: 清理缓存成功")
        return jsonify({
            'success': True,
            'message': '缓存已清理'
        })
        
    except Exception as e:
        logger.error(f"❌ API v3: 清理缓存失败: {e}")
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500

@api_v3.route('/migration/status', methods=['GET'])
def get_migration_status():
    """获取迁移状态"""
    try:
        field_manager = get_field_manager()
        
        # 检查配置文件状态
        config_exists = os.path.exists(field_manager.config_path)
        
        # 获取支持的表
        supported_tables = field_manager.get_supported_tables()
        
        status = {
            'config_file_exists': config_exists,
            'config_path': field_manager.config_path,
            'total_tables': len(supported_tables),
            'v3_api_active': True,
            'cache_size': len(field_manager.cache),
            'migration_ready': config_exists and len(supported_tables) > 0
        }
        
        logger.info(f"✅ API v3: 迁移状态检查 - 配置存在: {config_exists}, 表数: {status['total_tables']}")
        return jsonify({
            'success': True,
            'status': status
        })
        
    except Exception as e:
        logger.error(f"❌ API v3: 获取迁移状态失败: {e}")
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500

@api_v3.route('/migration/test-page')
def migration_test_page():
    """迁移测试页面"""
    try:
        logger.info("✅ API v3: 迁移测试页面访问")
        return render_template('resources/migration_test.html')
        
    except Exception as e:
        logger.error(f"❌ API v3: 迁移测试页面访问失败: {e}")
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500

@api_v3.route('/page/<table_name>')
def universal_resource_page(table_name):
    """通用资源管理页面 - API v3版本"""
    try:
        # 验证表是否存在
        manager = get_enhanced_manager()
        supported_tables_result = manager.get_supported_tables()
        
        if not supported_tables_result['success']:
            logger.warning(f"❌ API v3: 无法获取支持表列表")
            return f"""
            <html><head><title>系统错误</title></head>
            <body style="font-family: Arial; padding: 50px; text-align: center;">
                <h1>❌ 系统错误</h1>
                <p>无法获取支持的表列表</p>
                <a href="/api/v3/migration/test-page">返回测试页面</a>
            </body></html>
            """, 500
        
        # 获取表名列表
        supported_table_names = [t['table_name'] for t in supported_tables_result['tables']]
        
        if table_name not in supported_table_names:
            logger.warning(f"❌ API v3: 表 {table_name} 不存在或不支持")
            return f"""
            <html><head><title>表不存在</title></head>
            <body style="font-family: Arial; padding: 50px; text-align: center;">
                <h1>❌ 表不存在</h1>
                <p>表 '{table_name}' 不存在或不支持</p>
                                 <p>支持的表: {', '.join(supported_table_names)}</p>
                <a href="/api/v3/migration/test-page">返回测试页面</a>
            </body></html>
            """, 404
        
        # 获取表配置信息
        table_info = manager.field_manager.get_table_info(table_name)
        
        # 创建显示配置
        table_config = {
            'display_name': _get_display_name(table_name),
            'description': f"基于API v3动态字段管理的 {table_name} 表管理页面"
        }
        
        logger.info(f"✅ API v3: 访问通用资源页面 - {table_name}")
        return render_template('resources/simple_test_v3.html',
                             table_name=table_name,
                             page_title=table_config['display_name'],
                             page_description=table_config['description'],
                             table_title=table_config['display_name'],
                             table_info=table_info)
        
    except Exception as e:
        logger.error(f"❌ API v3: 通用资源页面访问失败 - {table_name}: {e}")
        return f"""
        <html><head><title>页面加载失败</title></head>
        <body style="font-family: Arial; padding: 50px; text-align: center;">
            <h1>⚠️ 页面加载失败</h1>
            <p>错误信息: {str(e)}</p>
            <a href="/api/v3/migration/test-page">返回测试页面</a>
        </body></html>
        """, 500

@api_v3.route('/test/crud')
def crud_test_page():
    """CRUD功能测试页面"""
    try:
        logger.info("✅ API v3: 访问CRUD测试页面")
        return render_template('resources/crud_test_v3.html')

    except Exception as e:
        logger.error(f"❌ API v3: CRUD测试页面失败: {e}")
        return f"页面加载失败: {e}", 500

@api_v3.route('/test/advanced')
def advanced_features_test_page():
    """高级功能测试页面"""
    try:
        logger.info("✅ API v3: 访问高级功能测试页面")
        return render_template('resources/advanced_features_test_v3.html')

    except Exception as e:
        logger.error(f"❌ API v3: 高级功能测试页面失败: {e}")
        return f"页面加载失败: {e}", 500

@api_v3.route('/pages/eqp_status')
def eqp_status_v3_page():
    """设备状态管理v3页面"""
    try:
        logger.info("✅ API v3: 访问设备状态管理v3页面")
        return render_template('resources/eqp_status_v3.html')

    except Exception as e:
        logger.error(f"❌ API v3: 设备状态管理v3页面失败: {e}")
        return f"页面加载失败: {e}", 500

@api_v3.route('/pages/et_uph_eqp')
def et_uph_eqp_v3_page():
    """UPH设备管理v3页面"""
    try:
        logger.info("✅ API v3: 访问UPH设备管理v3页面")
        return render_template('resources/et_uph_eqp_v3.html')

    except Exception as e:
        logger.error(f"❌ API v3: UPH设备管理v3页面失败: {e}")
        return f"页面加载失败: {e}", 500

@api_v3.route('/pages/et_ft_test_spec')
def et_ft_test_spec_v3_page():
    """测试规格管理v3页面"""
    try:
        logger.info("✅ API v3: 访问测试规格管理v3页面")
        return render_template('resources/et_ft_test_spec_v3.html')

    except Exception as e:
        logger.error(f"❌ API v3: 测试规格管理v3页面失败: {e}")
        return f"页面加载失败: {e}", 500

@api_v3.route('/pages/ct')
def ct_v3_page():
    """产品周期管理v3页面"""
    try:
        logger.info("✅ API v3: 访问产品周期管理v3页面")
        return render_template('resources/ct_v3.html')

    except Exception as e:
        logger.error(f"❌ API v3: 产品周期管理v3页面失败: {e}")
        return f"页面加载失败: {e}", 500

@api_v3.route('/navigation')
def navigation_center():
    """API v3导航中心"""
    try:
        logger.info("✅ API v3: 访问导航中心")
        return render_template('resources/api_v3_navigation.html')

    except Exception as e:
        logger.error(f"❌ API v3: 导航中心失败: {e}")
        return f"页面加载失败: {e}", 500

@api_v3.route('/tables/<table_name>/export', methods=['GET'])
def export_table_data_v3(table_name):
    """导出表数据 - 支持Excel和CSV格式"""
    try:
        # 获取请求参数
        export_format = request.args.get('format', 'excel').lower()

        # 处理筛选条件
        filters = []
        filters_param = request.args.get('filters')
        if filters_param:
            import json
            filters = json.loads(filters_param)

        # 处理排序条件
        sort_by = request.args.get('sort_by', '')
        sort_order = request.args.get('sort_order', 'asc')

        # 处理搜索条件
        search = request.args.get('search', '')
        if search:
            filters.append({
                'field': '_global_search',
                'operator': 'contains',
                'value': search
            })

        manager = get_enhanced_manager()

        # 获取所有数据（不分页）
        result = manager.get_table_data(table_name, 1, 10000, filters, sort_by, sort_order)

        if not result['success']:
            return jsonify({
                'success': False,
                'error': result.get('error', '获取数据失败')
            }), 500

        # 根据格式导出
        if export_format == 'csv':
            return _export_csv(table_name, result['columns'], result['data'])
        else:
            return _export_excel(table_name, result['columns'], result['data'])

    except Exception as e:
        logger.error(f"❌ API v3: 导出数据异常 - {table_name}: {e}")
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500

def _export_csv(table_name, columns, data):
    """导出CSV格式"""
    import csv
    import io
    from flask import Response

    output = io.StringIO()
    writer = csv.writer(output)

    # 写入表头
    writer.writerow(columns)

    # 写入数据
    for row in data:
        writer.writerow([row.get(col, '') for col in columns])

    output.seek(0)

    return Response(
        output.getvalue(),
        mimetype='text/csv',
        headers={
            'Content-Disposition': f'attachment; filename={table_name}_export.csv'
        }
    )

def _export_excel(table_name, columns, data):
    """导出Excel格式"""
    try:
        import pandas as pd
        import io
        from flask import Response

        # 创建DataFrame
        df_data = []
        for row in data:
            df_data.append([row.get(col, '') for col in columns])

        df = pd.DataFrame(df_data, columns=columns)

        # 创建Excel文件
        output = io.BytesIO()
        with pd.ExcelWriter(output, engine='openpyxl') as writer:
            df.to_excel(writer, sheet_name=table_name, index=False)

        output.seek(0)

        return Response(
            output.getvalue(),
            mimetype='application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
            headers={
                'Content-Disposition': f'attachment; filename={table_name}_export.xlsx'
            }
        )

    except ImportError:
        # 如果没有pandas，回退到CSV
        logger.warning("pandas未安装，回退到CSV导出")
        return _export_csv(table_name, columns, data)

def _get_display_name(table_name):
    """获取表的显示名称"""
    display_names = {
        'eqp_status': '设备状态管理',
        'et_uph_eqp': 'UPH设备管理',
        'et_ft_test_spec': '测试规格管理',
        'ct': '产品周期管理'
    }
    return display_names.get(table_name, table_name.upper())