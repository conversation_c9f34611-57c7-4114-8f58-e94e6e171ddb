<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>CRUD测试 - API v3</title>
    <link rel="stylesheet" href="{{ url_for('static', filename='vendor/bootstrap/bootstrap.min.css') }}">
    <link rel="stylesheet" href="{{ url_for('static', filename='vendor/fontawesome/all.min.css') }}">
    <style>
        body { font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, sans-serif; }
        .test-section { border: 1px solid #dee2e6; border-radius: 8px; padding: 20px; margin: 20px 0; }
        .test-success { border-color: #28a745; background-color: #f8fff9; }
        .test-error { border-color: #dc3545; background-color: #fff8f8; }
        .log-area { background-color: #f8f9fa; border: 1px solid #dee2e6; border-radius: 4px; padding: 15px; max-height: 300px; overflow-y: auto; font-family: monospace; }
    </style>
</head>
<body>
    <nav class="navbar navbar-expand-lg" style="background-color: #b72424;">
        <div class="container">
            <a class="navbar-brand text-white" href="/">
                <i class="fas fa-vial me-2"></i>CRUD测试 - API v3
            </a>
        </div>
    </nav>

    <div class="container mt-4">
        <div class="row">
            <div class="col-md-8">
                <!-- 测试步骤1：新增记录 -->
                <div class="test-section" id="test-create">
                    <h5><i class="fas fa-plus-circle text-success me-2"></i>步骤1：新增记录测试</h5>
                    <p class="text-muted">测试API v3的POST /tables/{table}/data接口</p>
                    
                    <div class="mb-3">
                        <label for="testTable" class="form-label">选择测试表：</label>
                        <select class="form-select" id="testTable">
                            <option value="">请选择...</option>
                            <option value="eqp_status">设备状态管理</option>
                            <option value="lotpriorityconfig">批次优先级配置</option>
                            <option value="devicepriorityconfig">设备优先级配置</option>
                        </select>
                    </div>
                    
                    <div id="createForm" style="display: none;">
                        <div class="row">
                            <div class="col-md-6">
                                <label class="form-label">设备名称：</label>
                                <input type="text" class="form-control" id="deviceName" placeholder="TEST_DEVICE_001">
                            </div>
                            <div class="col-md-6">
                                <label class="form-label">状态：</label>
                                <select class="form-select" id="deviceStatus">
                                    <option value="IDLE">IDLE</option>
                                    <option value="RUN">RUN</option>
                                    <option value="DOWN">DOWN</option>
                                </select>
                            </div>
                        </div>
                        <div class="mt-3">
                            <button class="btn btn-success" onclick="testCreate()">
                                <i class="fas fa-plus me-1"></i>测试新增
                            </button>
                            <button class="btn btn-outline-secondary ms-2" onclick="clearForm()">清空</button>
                        </div>
                    </div>
                </div>
                
                <!-- 测试步骤2：查看结果 -->
                <div class="test-section" id="test-read">
                    <h5><i class="fas fa-list text-primary me-2"></i>步骤2：查看数据</h5>
                    <button class="btn btn-primary" onclick="testRead()">
                        <i class="fas fa-sync me-1"></i>刷新数据
                    </button>
                    <div id="dataTable" class="mt-3"></div>
                </div>

                <!-- 测试步骤3：删除记录 -->
                <div class="test-section" id="test-delete">
                    <h5><i class="fas fa-trash text-danger me-2"></i>步骤3：删除记录测试</h5>
                    <p class="text-muted">测试API v3的DELETE /tables/{table}/data/{id}接口</p>

                    <div class="mb-3">
                        <label for="deleteId" class="form-label">要删除的记录ID：</label>
                        <input type="text" class="form-control" id="deleteId" placeholder="输入记录ID">
                    </div>

                    <button class="btn btn-danger" onclick="testDelete()">
                        <i class="fas fa-trash me-1"></i>删除记录
                    </button>
                </div>

                <!-- 测试步骤4：更新记录 -->
                <div class="test-section" id="test-update">
                    <h5><i class="fas fa-edit text-warning me-2"></i>步骤4：更新记录测试</h5>
                    <p class="text-muted">测试API v3的PUT /tables/{table}/data/{id}接口</p>

                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="updateId" class="form-label">要更新的记录ID：</label>
                                <input type="text" class="form-control" id="updateId" placeholder="输入记录ID">
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="updateStatus" class="form-label">新状态：</label>
                                <select class="form-select" id="updateStatus">
                                    <option value="IDLE">IDLE</option>
                                    <option value="RUN">RUN</option>
                                    <option value="DOWN">DOWN</option>
                                    <option value="MAINT">MAINT</option>
                                </select>
                            </div>
                        </div>
                    </div>

                    <button class="btn btn-warning" onclick="testUpdate()">
                        <i class="fas fa-edit me-1"></i>更新记录
                    </button>
                </div>

                <!-- 测试步骤5：高级筛选 -->
                <div class="test-section" id="test-filter">
                    <h5><i class="fas fa-filter text-info me-2"></i>步骤5：高级筛选测试</h5>
                    <p class="text-muted">测试API v3的筛选功能</p>

                    <div class="row">
                        <div class="col-md-4">
                            <div class="mb-3">
                                <label for="filterField" class="form-label">筛选字段：</label>
                                <select class="form-select" id="filterField">
                                    <option value="">请选择字段...</option>
                                    <option value="STATUS">状态</option>
                                    <option value="DEVICE">设备</option>
                                    <option value="STAGE">阶段</option>
                                    <option value="LOT_ID">批次ID</option>
                                </select>
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="mb-3">
                                <label for="filterOperator" class="form-label">操作符：</label>
                                <select class="form-select" id="filterOperator">
                                    <option value="contains">包含</option>
                                    <option value="equals">等于</option>
                                    <option value="starts_with">开始于</option>
                                    <option value="ends_with">结束于</option>
                                </select>
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="mb-3">
                                <label for="filterValue" class="form-label">筛选值：</label>
                                <input type="text" class="form-control" id="filterValue" placeholder="输入筛选值">
                            </div>
                        </div>
                    </div>

                    <div class="mb-3">
                        <button class="btn btn-info me-2" onclick="testFilter()">
                            <i class="fas fa-filter me-1"></i>应用筛选
                        </button>
                        <button class="btn btn-outline-secondary" onclick="clearFilter()">
                            <i class="fas fa-times me-1"></i>清除筛选
                        </button>
                    </div>

                    <div id="filterStatus" class="alert alert-info" style="display: none;">
                        <i class="fas fa-info-circle me-2"></i>
                        <span id="filterStatusText">无筛选条件</span>
                    </div>
                </div>
            </div>
            
            <div class="col-md-4">
                <!-- 日志区域 -->
                <div class="test-section">
                    <h6><i class="fas fa-terminal me-2"></i>测试日志</h6>
                    <div id="logArea" class="log-area"></div>
                    <button class="btn btn-sm btn-outline-secondary mt-2" onclick="clearLog()">清空日志</button>
                </div>
                
                <!-- 状态面板 -->
                <div class="test-section">
                    <h6><i class="fas fa-chart-line me-2"></i>测试状态</h6>
                    <div id="statusPanel">
                        <div class="badge bg-secondary">等待开始</div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="{{ url_for('static', filename='vendor/bootstrap/bootstrap.bundle.min.js') }}"></script>
    <script>
        const API_BASE = '/api/v3';
        let currentTable = '';
        let currentFilters = [];

        // 表选择变化事件
        document.getElementById('testTable').addEventListener('change', function() {
            currentTable = this.value;
            const form = document.getElementById('createForm');
            if (currentTable) {
                form.style.display = 'block';
                log(`选择测试表: ${currentTable}`);
                updateFilterFields();
            } else {
                form.style.display = 'none';
            }
        });

        // 更新筛选字段选项
        function updateFilterFields() {
            const filterField = document.getElementById('filterField');
            filterField.innerHTML = '<option value="">请选择字段...</option>';

            if (currentTable === 'eqp_status') {
                filterField.innerHTML += `
                    <option value="STATUS">状态</option>
                    <option value="DEVICE">设备</option>
                    <option value="STAGE">阶段</option>
                    <option value="LOT_ID">批次ID</option>
                    <option value="HANDLER_ID">处理器ID</option>
                    <option value="TESTER_ID">测试器ID</option>
                `;
            } else if (currentTable === 'lotpriorityconfig') {
                filterField.innerHTML += `
                    <option value="device">设备</option>
                    <option value="stage">阶段</option>
                    <option value="priority">优先级</option>
                    <option value="user">用户</option>
                `;
            } else if (currentTable === 'devicepriorityconfig') {
                filterField.innerHTML += `
                    <option value="device">设备</option>
                    <option value="priority">优先级</option>
                    <option value="user">用户</option>
                    <option value="from_time">开始时间</option>
                    <option value="end_time">结束时间</option>
                `;
            }
        }
        
        // 测试新增记录
        async function testCreate() {
            if (!currentTable) {
                alert('请先选择测试表');
                return;
            }
            
            const deviceName = document.getElementById('deviceName').value;
            const deviceStatus = document.getElementById('deviceStatus').value;
            
            if (!deviceName) {
                alert('请输入设备名称');
                return;
            }
            
            log(`开始测试新增记录...`);
            setStatus('testing', '正在测试新增...');
            
            try {
                const data = {};
                
                // 根据不同表构造不同的数据
                if (currentTable === 'eqp_status') {
                    data.HANDLER_ID = deviceName;
                    data.TESTER_ID = deviceName + '_TESTER';
                    data.STATUS = deviceStatus;
                    data.DEVICE = 'TEST_DEVICE';
                    data.STAGE = 'FT';
                    data.LOT_ID = 'TEST_LOT_' + Date.now();
                } else if (currentTable === 'lotpriorityconfig') {
                    data.device = deviceName;
                    data.stage = 'FT';
                    data.priority = 1;
                    data.user = 'test_user';
                } else if (currentTable === 'devicepriorityconfig') {
                    data.device = deviceName;
                    data.priority = 1;
                    data.from_time = '08:00:00';
                    data.end_time = '18:00:00';
                    data.user = 'test_user';
                }
                
                log(`发送数据: ${JSON.stringify(data, null, 2)}`);
                
                const response = await fetch(`${API_BASE}/tables/${currentTable}/data`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    credentials: 'same-origin',
                    body: JSON.stringify(data)
                });
                
                const result = await response.json();
                log(`API响应: ${JSON.stringify(result, null, 2)}`);
                
                if (result.success) {
                    setStatus('success', '新增成功');
                    log(`✅ 新增记录成功! ID: ${result.record_id || '未知'}`);
                    
                    // 自动刷新数据
                    setTimeout(() => testRead(), 1000);
                } else {
                    setStatus('error', '新增失败');
                    log(`❌ 新增记录失败: ${result.error}`);
                }
                
            } catch (error) {
                setStatus('error', '请求失败');
                log(`❌ 请求异常: ${error.message}`);
            }
        }
        
        // 测试读取数据
        async function testRead() {
            if (!currentTable) {
                alert('请先选择测试表');
                return;
            }

            log(`刷新 ${currentTable} 数据...`);

            try {
                const response = await fetch(`${API_BASE}/tables/${currentTable}/data?page=1&per_page=10`, {
                    method: 'GET',
                    credentials: 'same-origin'
                });

                const result = await response.json();

                if (result.success) {
                    log(`✅ 获取数据成功: ${result.total}条记录`);
                    renderTable(result.columns, result.data);
                } else {
                    log(`❌ 获取数据失败: ${result.error}`);
                }

            } catch (error) {
                log(`❌ 获取数据异常: ${error.message}`);
            }
        }

        // 测试删除记录
        async function testDelete() {
            if (!currentTable) {
                alert('请先选择测试表');
                return;
            }

            const recordId = document.getElementById('deleteId').value;
            if (!recordId) {
                alert('请输入要删除的记录ID');
                return;
            }

            if (!confirm(`确定要删除ID为 ${recordId} 的记录吗？`)) {
                return;
            }

            log(`开始测试删除记录 ID: ${recordId}...`);
            setStatus('testing', '正在测试删除...');

            try {
                const response = await fetch(`${API_BASE}/tables/${currentTable}/data/${recordId}`, {
                    method: 'DELETE',
                    credentials: 'same-origin'
                });

                const result = await response.json();
                log(`API响应: ${JSON.stringify(result, null, 2)}`);

                if (result.success) {
                    setStatus('success', '删除成功');
                    log(`✅ 删除记录成功! 影响行数: ${result.affected_rows || 1}`);

                    // 清空输入框
                    document.getElementById('deleteId').value = '';

                    // 自动刷新数据
                    setTimeout(() => testRead(), 1000);
                } else {
                    setStatus('error', '删除失败');
                    log(`❌ 删除记录失败: ${result.error}`);
                }

            } catch (error) {
                setStatus('error', '请求失败');
                log(`❌ 删除请求异常: ${error.message}`);
            }
        }

        // 测试更新记录
        async function testUpdate() {
            if (!currentTable) {
                alert('请先选择测试表');
                return;
            }

            const recordId = document.getElementById('updateId').value;
            const newStatus = document.getElementById('updateStatus').value;

            if (!recordId) {
                alert('请输入要更新的记录ID');
                return;
            }

            log(`开始测试更新记录 ID: ${recordId}...`);
            setStatus('testing', '正在测试更新...');

            try {
                const data = {};

                // 根据不同表构造不同的更新数据
                if (currentTable === 'eqp_status') {
                    data.STATUS = newStatus;
                    data.UPDATED_TIME = new Date().toISOString().slice(0, 19).replace('T', ' ');
                } else if (currentTable === 'lotpriorityconfig') {
                    data.priority = parseInt(newStatus === 'RUN' ? '1' : '2');
                    data.updated_time = new Date().toISOString().slice(0, 19).replace('T', ' ');
                } else if (currentTable === 'devicepriorityconfig') {
                    data.priority = parseInt(newStatus === 'RUN' ? '1' : '2');
                    data.updated_time = new Date().toISOString().slice(0, 19).replace('T', ' ');
                }

                log(`发送更新数据: ${JSON.stringify(data, null, 2)}`);

                const response = await fetch(`${API_BASE}/tables/${currentTable}/data/${recordId}`, {
                    method: 'PUT',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    credentials: 'same-origin',
                    body: JSON.stringify(data)
                });

                const result = await response.json();
                log(`API响应: ${JSON.stringify(result, null, 2)}`);

                if (result.success) {
                    setStatus('success', '更新成功');
                    log(`✅ 更新记录成功! 影响行数: ${result.affected_rows || 1}`);

                    // 清空输入框
                    document.getElementById('updateId').value = '';

                    // 自动刷新数据
                    setTimeout(() => testRead(), 1000);
                } else {
                    setStatus('error', '更新失败');
                    log(`❌ 更新记录失败: ${result.error}`);
                }

            } catch (error) {
                setStatus('error', '请求失败');
                log(`❌ 更新请求异常: ${error.message}`);
            }
        }
        
        // 渲染数据表格
        function renderTable(columns, data) {
            const container = document.getElementById('dataTable');
            
            if (!data || data.length === 0) {
                container.innerHTML = '<div class="alert alert-info">暂无数据</div>';
                return;
            }
            
            let html = '<div class="table-responsive"><table class="table table-sm table-striped">';
            
            // 表头
            html += '<thead class="table-light"><tr>';
            columns.forEach(col => {
                html += `<th>${col}</th>`;
            });
            html += '</tr></thead>';
            
            // 数据
            html += '<tbody>';
            data.slice(0, 5).forEach(row => { // 只显示前5条
                html += '<tr>';
                columns.forEach(col => {
                    const value = row[col] || '';
                    html += `<td title="${value}">${String(value).substring(0, 20)}${String(value).length > 20 ? '...' : ''}</td>`;
                });
                html += '</tr>';
            });
            html += '</tbody></table></div>';
            
            if (data.length > 5) {
                html += `<small class="text-muted">显示前5条，共${data.length}条记录</small>`;
            }
            
            container.innerHTML = html;
        }
        
        // 工具函数
        function log(message) {
            const logArea = document.getElementById('logArea');
            const time = new Date().toLocaleTimeString();
            logArea.innerHTML += `<div>[${time}] ${message}</div>`;
            logArea.scrollTop = logArea.scrollHeight;
        }
        
        function clearLog() {
            document.getElementById('logArea').innerHTML = '';
        }
        
        function clearForm() {
            document.getElementById('deviceName').value = '';
            document.getElementById('deviceStatus').value = 'IDLE';
        }
        
        function setStatus(type, message) {
            const panel = document.getElementById('statusPanel');
            const badgeClass = type === 'success' ? 'bg-success' : 
                             type === 'error' ? 'bg-danger' : 
                             type === 'testing' ? 'bg-warning' : 'bg-secondary';
            panel.innerHTML = `<div class="badge ${badgeClass}">${message}</div>`;
        }
        
        // 初始化
        log('CRUD测试页面已加载');
        log('选择测试表开始第一步测试');
    </script>
</body>
</html> 