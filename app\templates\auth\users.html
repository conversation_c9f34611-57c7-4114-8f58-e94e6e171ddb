{% extends "base.html" %}

{% block title %}车规芯片终测智能调度平台 - 用户管理
<style>
:root {
    --primary-color: #b72424;
    --secondary-color: #d73027;
    --success-color: #28a745;
    --warning-color: #ffc107;
    --danger-color: #dc3545;
    --info-color: #17a2b8;
}

.btn-primary {
    background-color: var(--primary-color);
    border-color: var(--primary-color);
}

.btn-primary:hover {
    background-color: var(--secondary-color);
    border-color: var(--secondary-color);
}

.navbar-brand, .nav-link.active {
    color: var(--primary-color) !important;
}

.card-header {
    background-color: var(--primary-color);
    color: white;
}
</style>
{% endblock %}

{% block page_title %}用户管理{% endblock %}

{% block content %}
<div class="container-fluid">
    <div class="row mb-4">
        <div class="col">
            <div class="card">
                <div class="card-body">
                    <div class="d-flex justify-content-between align-items-center mb-3">
                        <h5 class="card-title mb-0">用户列表</h5>
                        <button class="btn btn-primary" data-bs-toggle="modal" data-bs-target="#addUserModal">
                            <i class="fas fa-plus me-1"></i>添加用户
                        </button>
                    </div>
                    <div class="table-responsive">
                        <table class="table table-hover">
                            <thead>
                                <tr>
                                    <th>用户名</th>
                                    <th>角色</th>
                                    <th>创建时间</th>
                                    <th>权限设置</th>
                                    <th>操作</th>
                                </tr>
                            </thead>
                            <tbody id="userTableBody">
                                <!-- 用户列表将通过JavaScript动态加载 -->
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- 添加用户模态框 -->
<div class="modal fade" id="addUserModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">添加用户</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <form id="addUserForm">
                    <div class="mb-3">
                        <label class="form-label">用户名</label>
                        <input type="text" class="form-control" name="username" required>
                    </div>
                    <div class="mb-3">
                        <label class="form-label">密码</label>
                        <input type="password" class="form-control" id="password" name="password" autocomplete="new-password" required>
                    </div>
                    <div class="mb-3">
                        <label class="form-label">角色</label>
                        <select class="form-select" name="role" required>
                            <option value="admin">管理员</option>
                            <option value="boss">boss</option>
                            <option value="Operator">Operator</option>
                        </select>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
                <button type="button" class="btn btn-primary" onclick="addUser()">保存</button>
            </div>
        </div>
    </div>
</div>

<!-- 编辑用户模态框 -->
<div class="modal fade" id="editUserModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">编辑用户</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <form id="editUserForm">
                    <input type="hidden" name="username">
                    <div class="mb-3">
                        <label class="form-label">新密码（留空表示不修改）</label>
                        <input type="password" class="form-control" id="passwordEdit" name="password" autocomplete="new-password">
                    </div>
                    <div class="mb-3">
                        <label class="form-label">角色</label>
                        <select class="form-select" name="role" required>
                            <option value="admin">管理员</option>
                            <option value="boss">boss</option>
                            <option value="Operator">Operator</option>
                        </select>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
                <button type="button" class="btn btn-primary" onclick="updateUser()">保存</button>
            </div>
        </div>
    </div>
</div>

<!-- 权限设置模态框 -->
<div class="modal fade" id="permissionModal" tabindex="-1">
    <div class="modal-dialog modal-xl">
        <div class="modal-content">
            <div class="modal-header bg-light">
                <h5 class="modal-title">
                    <i class="fas fa-key me-2"></i>设置用户权限
                    <span id="permissionUserName" class="badge bg-primary ms-2"></span>
                </h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <form id="permissionForm">
                    <input type="hidden" name="username">
                    <div id="menuPermissions" class="mb-3">
                        <!-- 菜单权限将通过JavaScript动态加载 -->
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
                <button type="button" class="btn btn-primary" onclick="savePermissions()">
                    <i class="fas fa-save me-1"></i>保存权限
                </button>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
// 加载用户列表
function loadUsers() {
    fetch('/api/auth/users')
        .then(response => {
            if (!response.ok) {
                throw new Error(`HTTP error! status: ${response.status}`);
            }
            return response.json();
        })
        .then(users => {
            const tbody = document.getElementById('userTableBody');
            tbody.innerHTML = users.map(user => `
                <tr>
                    <td>${user.username}</td>
                    <td>${getRoleName(user.role)}</td>
                    <td>${new Date(user.created_at).toLocaleString()}</td>
                    <td>
                        ${user.role !== 'admin' ? `<button class="btn btn-info btn-sm" onclick="showPermissions('${user.username}')">
                            <i class="fas fa-key me-1"></i>权限设置
                        </button>` : '<span class="text-muted">管理员无需设置</span>'}
                    </td>
                    <td>
                        <button class="btn btn-primary btn-sm me-1" onclick="showEditUser('${user.username}', '${user.role}')">
                            <i class="fas fa-edit me-1"></i>编辑
                        </button>
                        <button class="btn btn-danger btn-sm" onclick="deleteUser('${user.username}')" ${user.username === 'admin' ? 'disabled' : ''}>
                            <i class="fas fa-trash me-1"></i>删除
                        </button>
                    </td>
                </tr>
            `).join('');
        })
        .catch(error => {
            console.error('加载用户列表失败:', error);
            document.getElementById('userTableBody').innerHTML = `
                <tr>
                    <td colspan="5" class="text-center text-danger">
                        加载用户列表失败: ${error.message}
                    </td>
                </tr>
            `;
        });
}

// 获取角色名称
function getRoleName(role) {
    const roleNames = {
        'admin': '管理员',
        'boss': 'boss',
        'Operator': 'Operator'
    };
    return roleNames[role] || role;
}

// 添加用户
function addUser() {
    const form = document.getElementById('addUserForm');
    const formData = new FormData(form);
    const data = Object.fromEntries(formData.entries());
    
    fetch('/api/auth/users', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json'
        },
        body: JSON.stringify(data)
    })
    .then(response => response.json())
    .then(result => {
        if (result.error) {
            alert(result.error);
        } else {
            bootstrap.Modal.getInstance(document.getElementById('addUserModal')).hide();
            form.reset();
            loadUsers();
        }
    });
}

// 显示编辑用户模态框
function showEditUser(username, role) {
    const form = document.getElementById('editUserForm');
    form.username.value = username;
    form.role.value = role;
    new bootstrap.Modal(document.getElementById('editUserModal')).show();
}

// 更新用户
function updateUser() {
    const form = document.getElementById('editUserForm');
    const formData = new FormData(form);
    const data = Object.fromEntries(formData.entries());
    if (!data.password) delete data.password;
    
    fetch(`/api/auth/users/${data.username}`, {
        method: 'PUT',
        headers: {
            'Content-Type': 'application/json'
        },
        body: JSON.stringify(data)
    })
    .then(response => response.json())
    .then(result => {
        if (result.error) {
            alert(result.error);
        } else {
            bootstrap.Modal.getInstance(document.getElementById('editUserModal')).hide();
            form.reset();
            loadUsers();
        }
    });
}

// 删除用户
function deleteUser(username) {
    if (!confirm('确定要删除此用户吗？')) return;
    
    fetch(`/api/auth/users/${username}`, {
        method: 'DELETE'
    })
    .then(response => {
        if (response.ok) {
            alert('用户删除成功');
            loadUsers();
        } else {
            return response.json().then(data => {
                throw new Error(data.error || '删除失败');
            });
        }
    })
    .catch(error => {
        alert('删除失败: ' + error.message);
        console.error('删除用户错误:', error);
    });
}

// 显示权限设置模态框
function showPermissions(username) {
    console.log('开始设置用户权限:', username);
    
    const form = document.getElementById('permissionForm');
    if (!form) {
        console.error('找不到权限表单元素');
        alert('权限设置界面初始化失败，请刷新页面重试');
        return;
    }
    
    form.username.value = username;
    
    // 显示用户名
    const userNameElement = document.getElementById('permissionUserName');
    if (userNameElement) {
        userNameElement.textContent = username;
    }
    
    // 显示加载状态
    const container = document.getElementById('menuPermissions');
    if (container) {
        container.innerHTML = '<div class="text-center"><i class="fas fa-spinner fa-spin me-2"></i>正在加载权限设置...</div>';
    }
    
    // 加载菜单结构
    fetch('/api/v2/auth/menu-settings')
        .then(response => {
            if (!response.ok) {
                throw new Error(`菜单设置加载失败: ${response.status} ${response.statusText}`);
            }
            return response.json();
        })
        .then(menus => {
            console.log('菜单设置加载成功:', menus);
            
            // 加载用户权限
            return fetch(`/api/v2/auth/users/${username}/permissions`)
                .then(response => {
                    if (!response.ok) {
                        throw new Error(`用户权限加载失败: ${response.status} ${response.statusText}`);
                    }
                    return response.json();
                })
                .then(permissions => {
                    console.log('用户权限加载成功:', permissions);
                    
                    if (container) {
                        container.innerHTML = buildPermissionTree(menus, permissions);
                    }
                    
                    // 显示模态框
                    const modal = document.getElementById('permissionModal');
                    if (modal) {
                        new bootstrap.Modal(modal).show();
                    } else {
                        console.error('找不到权限模态框元素');
                        alert('权限设置模态框初始化失败');
                    }
                });
        })
        .catch(error => {
            console.error('权限设置加载失败:', error);
            if (container) {
                container.innerHTML = `<div class="alert alert-danger">
                    <i class="fas fa-exclamation-triangle me-2"></i>
                    权限设置加载失败: ${error.message}
                    <br><small>请检查网络连接或联系管理员</small>
                </div>`;
            }
            alert(`权限设置加载失败: ${error.message}`);
        });
}

// 构建权限树HTML
function buildPermissionTree(menus, permissions) {
    // 使用Bootstrap折叠面板和多列布局优化权限设置界面
    return `
    <div class="accordion" id="permissionAccordion">
        <!-- 1. 排产管理 -->
        <div class="accordion-item">
            <h2 class="accordion-header" id="headingOne">
                <button class="accordion-button" type="button" data-bs-toggle="collapse" data-bs-target="#collapseOne" aria-expanded="true" aria-controls="collapseOne">
                    <div class="form-check">
                        <input type="checkbox" class="form-check-input" id="menu_1" 
                               name="permissions[]" value="1"
                               ${permissions.includes(1) ? 'checked' : ''}>
                        <label class="form-check-label" for="menu_1">
                            <i class="fas fa-industry me-2"></i>排产管理
                        </label>
                    </div>
                </button>
            </h2>
            <div id="collapseOne" class="accordion-collapse collapse" aria-labelledby="headingOne" data-bs-parent="#permissionAccordion">
                <div class="accordion-body py-2">
                    <div class="row">
                        <div class="col-md-6">
                            <div class="form-check mb-2">
                                <input type="checkbox" class="form-check-input" id="menu_7" 
                                       name="permissions[]" value="7"
                                       ${permissions.includes(7) ? 'checked' : ''}>
                                <label class="form-check-label" for="menu_7">
                                    手动排产
                                </label>
                            </div>
                            <div class="form-check mb-2">
                                <input type="checkbox" class="form-check-input" id="menu_8" 
                                       name="permissions[]" value="8"
                                       ${permissions.includes(8) ? 'checked' : ''}>
                                <label class="form-check-label" for="menu_8">
                                    自动排产
                                </label>
                            </div>
                            <div class="form-check mb-2">
                                <input type="checkbox" class="form-check-input" id="menu_9" 
                                       name="permissions[]" value="9"
                                       ${permissions.includes(9) ? 'checked' : ''}>
                                <label class="form-check-label" for="menu_9">
                                    算法设置
                                </label>
                            </div>
                            <div class="form-check mb-2">
                                <input type="checkbox" class="form-check-input" id="menu_25" 
                                       name="permissions[]" value="25"
                                       ${permissions.includes(25) ? 'checked' : ''}>
                                <label class="form-check-label" for="menu_25">
                                    <strong>优先级设定</strong>
                                </label>
                            </div>
                            <div class="ms-3">
                                <div class="form-check mb-2">
                                    <input type="checkbox" class="form-check-input" id="menu_31" 
                                           name="permissions[]" value="31"
                                           ${permissions.includes(31) ? 'checked' : ''}>
                                    <label class="form-check-label" for="menu_31">
                                        ├─ 产品优先级配置
                                    </label>
                                </div>
                                <div class="form-check mb-2">
                                    <input type="checkbox" class="form-check-input" id="menu_32" 
                                           name="permissions[]" value="32"
                                           ${permissions.includes(32) ? 'checked' : ''}>
                                    <label class="form-check-label" for="menu_32">
                                        └─ 批次优先级配置
                                    </label>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="form-check mb-2">
                                <input type="checkbox" class="form-check-input" id="menu_33" 
                                       name="permissions[]" value="33"
                                       ${permissions.includes(33) ? 'checked' : ''}>
                                <label class="form-check-label" for="menu_33">
                                    <strong>排产预览</strong>
                                </label>
                            </div>
                            <div class="ms-3">
                                <div class="form-check mb-2">
                                    <input type="checkbox" class="form-check-input" id="menu_34" 
                                           name="permissions[]" value="34"
                                           ${permissions.includes(34) ? 'checked' : ''}>
                                    <label class="form-check-label" for="menu_34">
                                        ├─ 待排产批次
                                    </label>
                                </div>
                                <div class="form-check mb-2">
                                    <input type="checkbox" class="form-check-input" id="menu_35" 
                                           name="permissions[]" value="35"
                                           ${permissions.includes(35) ? 'checked' : ''}>
                                    <label class="form-check-label" for="menu_35">
                                        └─ 已排产批次
                                    </label>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- 2. 订单管理 -->
        <div class="accordion-item">
            <h2 class="accordion-header" id="headingTwo">
                <button class="accordion-button collapsed" type="button" data-bs-toggle="collapse" data-bs-target="#collapseTwo" aria-expanded="false" aria-controls="collapseTwo">
                    <div class="form-check">
                        <input type="checkbox" class="form-check-input" id="menu_2" 
                               name="permissions[]" value="2"
                               ${permissions.includes(2) ? 'checked' : ''}>
                        <label class="form-check-label" for="menu_2">
                            订单管理
                        </label>
                    </div>
                </button>
            </h2>
            <div id="collapseTwo" class="accordion-collapse collapse" aria-labelledby="headingTwo" data-bs-parent="#permissionsAccordion">
                <div class="accordion-body py-2">
                    <div class="row">
                        <div class="col-md-6">
                            <div class="form-check mb-2">
                                <input type="checkbox" class="form-check-input" id="menu_10" 
                                       name="permissions[]" value="10"
                                       ${permissions.includes(10) ? 'checked' : ''}>
                                <label class="form-check-label" for="menu_10">
                                    手动导入订单
                                </label>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="form-check mb-2">
                                <input type="checkbox" class="form-check-input" id="menu_11" 
                                       name="permissions[]" value="11"
                                       ${permissions.includes(11) ? 'checked' : ''}>
                                <label class="form-check-label" for="menu_11">
                                    自动导入订单
                                </label>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- 3. FCST分析菜单已移除 -->
        
        <!-- 4. 资源管理 -->
        <div class="accordion-item">
            <h2 class="accordion-header" id="headingFour">
                <button class="accordion-button collapsed" type="button" data-bs-toggle="collapse" data-bs-target="#collapseFour" aria-expanded="false" aria-controls="collapseFour">
                    <div class="form-check">
                        <input type="checkbox" class="form-check-input" id="menu_4" 
                               name="permissions[]" value="4"
                               ${permissions.includes(4) ? 'checked' : ''}>
                        <label class="form-check-label" for="menu_4">
                            资源管理
                        </label>
                    </div>
                </button>
            </h2>
            <div id="collapseFour" class="accordion-collapse collapse" aria-labelledby="headingFour" data-bs-parent="#permissionsAccordion">
                <div class="accordion-body py-2">
                    <div class="row">
                        <div class="col-md-6">
                            <div class="form-check mb-2">
                                <input type="checkbox" class="form-check-input" id="menu_14" 
                                       name="permissions[]" value="14"
                                       ${permissions.includes(14) ? 'checked' : ''}>
                                <label class="form-check-label" for="menu_14">
                                    设备资源
                                </label>
                            </div>
                            <div class="form-check mb-2">
                                <input type="checkbox" class="form-check-input" id="menu_15" 
                                       name="permissions[]" value="15"
                                       ${permissions.includes(15) ? 'checked' : ''}>
                                <label class="form-check-label" for="menu_15">
                                    测试规范
                                </label>
                            </div>
                            <div class="form-check mb-2">
                                <input type="checkbox" class="form-check-input" id="menu_16" 
                                       name="permissions[]" value="16"
                                       ${permissions.includes(16) ? 'checked' : ''}>
                                <label class="form-check-label" for="menu_16">
                                    套件资源
                                </label>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="form-check mb-2">
                                <input type="checkbox" class="form-check-input" id="menu_17" 
                                       name="permissions[]" value="17"
                                       ${permissions.includes(17) ? 'checked' : ''}>
                                <label class="form-check-label" for="menu_17">
                                    UPH
                                </label>
                            </div>
                            <div class="form-check mb-2">
                                <input type="checkbox" class="form-check-input" id="menu_18" 
                                       name="permissions[]" value="18"
                                       ${permissions.includes(18) ? 'checked' : ''}>
                                <label class="form-check-label" for="menu_18">
                                    产品周期
                                </label>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- 5. WIP跟踪 -->
        <div class="accordion-item">
            <h2 class="accordion-header">
                <button class="accordion-button collapsed" type="button" data-bs-toggle="collapse" data-bs-target="#collapse5">
                    <div class="form-check mb-0">
                        <input type="checkbox" class="form-check-input" id="menu_5" 
                               name="permissions[]" value="5"
                               ${permissions.includes(5) ? 'checked' : ''}>
                        <label class="form-check-label ms-2" for="menu_5">
                            <i class="fas fa-tasks me-2"></i>WIP跟踪
                        </label>
                    </div>
                </button>
            </h2>
            <div id="collapse5" class="accordion-collapse collapse" data-bs-parent="#permissionAccordion">
                <div class="accordion-body py-2">
                    <div class="row">
                        <div class="col-md-6">
                            <div class="form-check mb-2">
                                <input type="checkbox" class="form-check-input" id="menu_19" 
                                       name="permissions[]" value="19"
                                       ${permissions.includes(19) ? 'checked' : ''}>
                                <label class="form-check-label" for="menu_19">
                                    按产品
                                </label>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="form-check mb-2">
                                <input type="checkbox" class="form-check-input" id="menu_20" 
                                       name="permissions[]" value="20"
                                       ${permissions.includes(20) ? 'checked' : ''}>
                                <label class="form-check-label" for="menu_20">
                                    按批次
                                </label>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- 6. 系统管理 -->
        <div class="accordion-item">
            <h2 class="accordion-header">
                <button class="accordion-button collapsed" type="button" data-bs-toggle="collapse" data-bs-target="#collapse6">
                    <div class="form-check mb-0">
                        <input type="checkbox" class="form-check-input" id="menu_6" 
                               name="permissions[]" value="6"
                               ${permissions.includes(6) ? 'checked' : ''}>
                        <label class="form-check-label ms-2" for="menu_6">
                            <i class="fas fa-tools me-2"></i>系统管理
                        </label>
                    </div>
                </button>
            </h2>
            <div id="collapse6" class="accordion-collapse collapse" data-bs-parent="#permissionAccordion">
                <div class="accordion-body py-2">
                    <div class="row">
                        <div class="col-md-6">
                            <div class="form-check mb-2">
                                <input type="checkbox" class="form-check-input" id="menu_21" 
                                       name="permissions[]" value="21"
                                       ${permissions.includes(21) ? 'checked' : ''}>
                                <label class="form-check-label" for="menu_21">
                                    用户管理
                                </label>
                            </div>
                            <div class="form-check mb-2">
                                <input type="checkbox" class="form-check-input" id="menu_23" 
                                       name="permissions[]" value="23"
                                       ${permissions.includes(23) ? 'checked' : ''}>
                                <label class="form-check-label" for="menu_23">
                                    系统日志
                                </label>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="form-check mb-2">
                                <input type="checkbox" class="form-check-input" id="menu_24" 
                                       name="permissions[]" value="24"
                                       ${permissions.includes(24) ? 'checked' : ''}>
                                <label class="form-check-label" for="menu_24">
                                    AI助手
                                </label>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="mt-3">
        <button type="button" class="btn btn-sm btn-outline-secondary me-2" onclick="selectAllPermissions()">全选</button>
        <button type="button" class="btn btn-sm btn-outline-secondary" onclick="deselectAllPermissions()">取消全选</button>
    </div>
    `;
}

// 全选功能
function selectAllPermissions() {
    const checkboxes = document.querySelectorAll('#permissionForm input[name="permissions[]"]');
    checkboxes.forEach(checkbox => checkbox.checked = true);
}

// 取消全选功能
function deselectAllPermissions() {
    const checkboxes = document.querySelectorAll('#permissionForm input[name="permissions[]"]');
    checkboxes.forEach(checkbox => checkbox.checked = false);
}

// 保存权限设置
function savePermissions() {
    const form = document.getElementById('permissionForm');
    const username = form.username.value;
    const permissions = Array.from(form.querySelectorAll('input[name="permissions[]"]:checked'))
        .map(input => parseInt(input.value));
    
    // 确保有选中的权限
    if (permissions.length === 0) {
        if (!confirm('您没有选择任何权限，用户将无法访问任何菜单。是否继续？')) {
            return;
        }
    }
    
    // 简化请求数据格式 - 直接使用权限ID数组
    console.log('发送权限数据:', permissions);
    
    fetch(`/api/v2/auth/users/${username}/permissions`, {
        method: 'PUT',
        headers: {
            'Content-Type': 'application/json'
        },
        // 直接发送权限ID数组，包装在两种API支持的格式中
        body: JSON.stringify({
            "permissions": permissions
        })
    })
    .then(response => {
        console.log('服务器响应状态:', response.status);
        // 即使状态码不是200，也尝试解析响应内容，以获取更多错误信息
        return response.json().then(data => {
            if (!response.ok) {
                console.error('服务器返回错误:', data);
                throw new Error(`HTTP error! Status: ${response.status}, 详情: ${JSON.stringify(data)}`);
            }
            return data;
        });
    })
    .then(result => {
        console.log('保存权限成功:', result);
        if (result.error) {
            alert('保存权限失败: ' + result.error);
        } else {
            alert('权限保存成功！');
            bootstrap.Modal.getInstance(document.getElementById('permissionModal')).hide();
            loadUsers();
        }
    })
    .catch(error => {
        console.error('保存权限时出错:', error);
        alert('保存权限时发生错误: ' + error.message);
    });
}

// 页面加载完成后执行
document.addEventListener('DOMContentLoaded', function() {
    loadUsers();
});

// 父菜单选择变更时，自动选择/取消选择所有子菜单
document.addEventListener('change', function(e) {
    if (e.target.matches('input[type="checkbox"][name="permissions[]"]')) {
        // 获取菜单ID
        const menuId = parseInt(e.target.value);
        
        // 父菜单ID对应的子菜单映射
        const menuMap = {
            1: [7, 8, 9, 25],  // 排产管理
            2: [10, 11],       // 订单管理
            4: [14, 15, 16, 17, 18], // 资源管理
            5: [19, 20],       // WIP跟踪
            6: [21, 23, 24]    // 系统管理
        };
        
        // 检查是否是父菜单
        if (menuMap[menuId]) {
            const childMenus = menuMap[menuId];
            const checked = e.target.checked;
            
            // 选择/取消选择所有子菜单
            childMenus.forEach(childId => {
                const childCheckbox = document.getElementById(`menu_${childId}`);
                if (childCheckbox) {
                    childCheckbox.checked = checked;
                }
            });
        }
    }
});
</script>
{% endblock %} 