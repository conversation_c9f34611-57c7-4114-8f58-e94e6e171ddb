{% extends "base.html" %}

{% block title %}{{ page_title }} - AEC-FT ICP{% endblock %}

{% block extra_css %}
<style>
/* 通用资源管理v3样式 */
.page-header {
    background: linear-gradient(135deg, #007bff 0%, #0056b3 100%);
    color: white;
    padding: 2rem 0;
    margin-bottom: 2rem;
}

.feature-card {
    border: 1px solid #dee2e6;
    border-radius: 8px;
    padding: 20px;
    margin-bottom: 20px;
    background: white;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.search-toolbar {
    background: #f8f9fa;
    border-radius: 8px;
    padding: 15px;
    margin-bottom: 20px;
}

.table-container {
    background: white;
    border-radius: 8px;
    overflow: hidden;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.data-table {
    margin: 0;
}

.loading-overlay {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(255,255,255,0.8);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 1000;
}

.sort-header {
    cursor: pointer;
    user-select: none;
}

.sort-header:hover {
    background-color: #f8f9fa;
}

.sort-indicator {
    margin-left: 5px;
    opacity: 0.5;
}

.sort-active {
    opacity: 1;
}

.pagination-container {
    background: white;
    padding: 15px;
    border-top: 1px solid #dee2e6;
}

/* 统计卡片样式 */
.stats-cards {
    margin-bottom: 20px;
}

.stats-card {
    background: white;
    border-radius: 8px;
    padding: 20px;
    text-align: center;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
    border: 1px solid #dee2e6;
}

.stats-number {
    font-size: 2rem;
    font-weight: bold;
    margin-bottom: 5px;
}

.stats-label {
    color: #6c757d;
    font-size: 14px;
}

/* 筛选器样式 */
.filter-container {
    background: #f8f9fa;
    border: 1px solid #dee2e6;
    border-radius: 8px;
    padding: 15px;
    margin-bottom: 15px;
}

.filter-item {
    background: white;
    border: 1px solid #dee2e6;
    border-radius: 6px;
    padding: 10px;
    margin-bottom: 10px;
}

.filter-badge {
    background: #007bff;
    color: white;
    padding: 4px 8px;
    border-radius: 4px;
    font-size: 12px;
    margin-right: 5px;
    margin-bottom: 5px;
    display: inline-block;
}

.filter-badge .remove-filter {
    margin-left: 5px;
    cursor: pointer;
    opacity: 0.7;
}

.filter-badge .remove-filter:hover {
    opacity: 1;
}

/* 专业数据样式 */
.badge-status {
    padding: 4px 8px;
    border-radius: 4px;
    font-size: 12px;
    font-weight: bold;
}

.status-active { background-color: #28a745; color: #fff; }
.status-inactive { background-color: #dc3545; color: #fff; }
.status-maintenance { background-color: #ffc107; color: #000; }
.status-unknown { background-color: #6c757d; color: #fff; }

.priority-high { background-color: #dc3545; color: #fff; }
.priority-medium { background-color: #ffc107; color: #000; }
.priority-low { background-color: #28a745; color: #fff; }

.uph-high { background-color: #28a745; color: #fff; }
.uph-medium { background-color: #ffc107; color: #000; }
.uph-low { background-color: #dc3545; color: #fff; }
</style>
{% endblock %}

{% block content %}
<!-- 页面头部 -->
<div class="page-header">
    <div class="container-fluid">
        <div class="row align-items-center">
            <div class="col-md-8">
                <h1 class="mb-1">
                    <i class="{{ page_icon }} me-2"></i>{{ page_title }}
                </h1>
                <p class="mb-0 opacity-75">{{ page_description }}</p>
            </div>
            <div class="col-md-4 text-end">
                <span class="badge bg-success fs-6">API v3</span>
                <span class="badge bg-info fs-6 ms-2">实时数据</span>
            </div>
        </div>
    </div>
</div>

<div class="container-fluid">
    <!-- 统计卡片 -->
    <div class="row stats-cards" id="statsCards">
        <!-- 统计卡片将通过JavaScript动态生成 -->
    </div>

    <!-- 操作工具栏 -->
    <div class="search-toolbar">
        <div class="row align-items-center">
            <div class="col-md-3">
                <div class="input-group">
                    <span class="input-group-text">
                        <i class="fas fa-search"></i>
                    </span>
                    <input type="text" class="form-control" id="globalSearch" 
                           placeholder="全局搜索...">
                    <button class="btn btn-primary" onclick="performSearch()">搜索</button>
                </div>
            </div>
            <div class="col-md-2">
                <select class="form-select" id="perPage">
                    <option value="25">25条/页</option>
                    <option value="50" selected>50条/页</option>
                    <option value="100">100条/页</option>
                    <option value="200">200条/页</option>
                </select>
            </div>
            <div class="col-md-7">
                <div class="btn-group" role="group">
                    <button class="btn btn-success" onclick="refreshData()">
                        <i class="fas fa-sync me-1"></i>刷新
                    </button>
                    <button class="btn btn-info" onclick="showAdvancedFilters()">
                        <i class="fas fa-filter me-1"></i>高级筛选
                    </button>
                    <button class="btn btn-warning" onclick="exportData('excel')">
                        <i class="fas fa-file-excel me-1"></i>导出Excel
                    </button>
                    <button class="btn btn-secondary" onclick="exportData('csv')">
                        <i class="fas fa-file-csv me-1"></i>导出CSV
                    </button>
                    <button class="btn btn-primary" onclick="addNewRecord()">
                        <i class="fas fa-plus me-1"></i>新增
                    </button>
                </div>
            </div>
        </div>
    </div>

    <!-- 高级筛选器 -->
    <div id="advancedFilters" class="filter-container" style="display: none;">
        <div class="row">
            <div class="col-md-12">
                <h6><i class="fas fa-filter me-2"></i>高级筛选</h6>
                <div id="activeFilters" class="mb-3">
                    <!-- 活动筛选器将显示在这里 -->
                </div>
                <div class="row">
                    <div class="col-md-3">
                        <select class="form-select" id="filterField">
                            <option value="">选择字段</option>
                            <!-- 字段选项将通过JavaScript动态生成 -->
                        </select>
                    </div>
                    <div class="col-md-2">
                        <select class="form-select" id="filterOperator">
                            <option value="contains">包含</option>
                            <option value="equals">等于</option>
                            <option value="starts_with">开始于</option>
                            <option value="ends_with">结束于</option>
                            <option value="greater_than">大于</option>
                            <option value="less_than">小于</option>
                        </select>
                    </div>
                    <div class="col-md-3">
                        <input type="text" class="form-control" id="filterValue" placeholder="筛选值">
                    </div>
                    <div class="col-md-2">
                        <button class="btn btn-primary" onclick="addFilter()">
                            <i class="fas fa-plus me-1"></i>添加筛选
                        </button>
                    </div>
                    <div class="col-md-2">
                        <button class="btn btn-outline-secondary" onclick="clearAllFilters()">
                            <i class="fas fa-times me-1"></i>清空筛选
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- 数据表格 -->
    <div class="table-container position-relative">
        <div id="loadingOverlay" class="loading-overlay" style="display: none;">
            <div class="text-center">
                <div class="spinner-border text-primary" role="status">
                    <span class="visually-hidden">加载中...</span>
                </div>
                <div class="mt-2">正在加载数据...</div>
            </div>
        </div>
        
        <div class="table-responsive">
            <table class="table table-hover data-table" id="dataTable">
                <thead class="table-light" id="tableHeaders">
                    <!-- 表头将通过JavaScript动态生成 -->
                </thead>
                <tbody id="tableBody">
                    <tr>
                        <td colspan="100%" class="text-center py-4">
                            <i class="fas fa-spinner fa-spin me-2"></i>正在加载数据...
                        </td>
                    </tr>
                </tbody>
            </table>
        </div>
        
        <!-- 分页导航 -->
        <div class="pagination-container">
            <div class="row align-items-center">
                <div class="col-md-6">
                    <div id="dataInfo" class="text-muted">
                        显示第 1-50 条，共 0 条记录
                    </div>
                </div>
                <div class="col-md-6">
                    <nav>
                        <ul class="pagination justify-content-end mb-0" id="pagination">
                            <!-- 分页按钮将通过JavaScript生成 -->
                        </ul>
                    </nav>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script src="{{ url_for('static', filename='vendor/bootstrap/bootstrap.bundle.min.js') }}"></script>
<script>
// 全局变量
const API_BASE = '/api/v3';
const TABLE_NAME = '{{ table_name }}';
const PAGE_CONFIG = {
    title: '{{ page_title }}',
    icon: '{{ page_icon }}',
    description: '{{ page_description }}'
};

let currentData = [];
let currentColumns = [];
let currentPage = 1;
let totalPages = 1;
let currentSort = { field: '', order: 'asc' };
let currentFilters = [];
let currentSearch = '';

// 页面加载时初始化
document.addEventListener('DOMContentLoaded', function() {
    console.log(`🚀 ${PAGE_CONFIG.title} v3页面加载完成`);
    initializePage();
});

// 初始化页面
async function initializePage() {
    try {
        await loadTableStructure();
        await loadData();
        bindEvents();
    } catch (error) {
        console.error('页面初始化失败:', error);
        showError('页面初始化失败: ' + error.message);
    }
}

// 加载表结构
async function loadTableStructure() {
    try {
        const response = await fetch(`${API_BASE}/tables/${TABLE_NAME}/structure`);
        const result = await response.json();
        
        if (result.success) {
            currentColumns = result.columns;
            setupFilterFields();
        } else {
            throw new Error(result.error);
        }
    } catch (error) {
        console.error('加载表结构失败:', error);
        throw error;
    }
}

// 设置筛选字段选项
function setupFilterFields() {
    const filterField = document.getElementById('filterField');
    filterField.innerHTML = '<option value="">选择字段</option>';
    
    currentColumns.forEach(column => {
        const option = document.createElement('option');
        option.value = column.name;
        option.textContent = column.display_name || column.name;
        filterField.appendChild(option);
    });
}

// 加载数据
async function loadData() {
    showLoading(true);

    try {
        const params = new URLSearchParams({
            page: currentPage,
            per_page: document.getElementById('perPage').value
        });

        // 添加搜索条件
        if (currentSearch) {
            params.append('search', currentSearch);
        }

        // 添加排序条件
        if (currentSort.field) {
            params.append('sort_by', currentSort.field);
            params.append('sort_order', currentSort.order);
        }

        // 添加筛选条件
        if (currentFilters.length > 0) {
            params.append('filters', JSON.stringify(currentFilters));
        }

        const response = await fetch(`${API_BASE}/tables/${TABLE_NAME}/data?${params}`);
        const result = await response.json();

        if (result.success) {
            currentData = result.data;
            totalPages = result.pages;
            renderTable(result.data);
            renderPagination(result);
            updateStats(result.data);
            updateDataInfo(result);
        } else {
            showError('数据加载失败: ' + result.error);
        }

    } catch (error) {
        showError('请求失败: ' + error.message);
    } finally {
        showLoading(false);
    }
}

// 渲染表格
function renderTable(data) {
    const thead = document.getElementById('tableHeaders');
    const tbody = document.getElementById('tableBody');

    // 渲染表头
    if (currentColumns.length > 0) {
        thead.innerHTML = `
            <tr>
                ${currentColumns.map(column => `
                    <th class="sort-header" onclick="sortBy('${column.name}')">
                        ${column.display_name || column.name}
                        <i class="fas fa-sort sort-indicator"></i>
                    </th>
                `).join('')}
                <th>操作</th>
            </tr>
        `;
    }

    // 渲染表格数据
    if (!data || data.length === 0) {
        tbody.innerHTML = `
            <tr>
                <td colspan="${currentColumns.length + 1}" class="text-center py-4">
                    <i class="fas fa-inbox fa-2x text-muted mb-2"></i>
                    <div>暂无数据</div>
                </td>
            </tr>
        `;
        return;
    }

    tbody.innerHTML = data.map(row => `
        <tr>
            ${currentColumns.map(column => `
                <td>${formatCellValue(row[column.name], column)}</td>
            `).join('')}
            <td>
                <div class="btn-group btn-group-sm">
                    <button class="btn btn-outline-primary" onclick="editRecord(${row.id})" title="编辑">
                        <i class="fas fa-edit"></i>
                    </button>
                    <button class="btn btn-outline-info" onclick="viewDetails(${row.id})" title="详情">
                        <i class="fas fa-eye"></i>
                    </button>
                    <button class="btn btn-outline-danger" onclick="deleteRecord(${row.id})" title="删除">
                        <i class="fas fa-trash"></i>
                    </button>
                </div>
            </td>
        </tr>
    `).join('');

    // 更新排序指示器
    updateSortIndicators();
}

// 格式化单元格值
function formatCellValue(value, column) {
    if (!value && value !== 0) return '';

    // 根据字段类型格式化
    switch (column.type) {
        case 'datetime':
            return formatDateTime(value);
        case 'date':
            return formatDate(value);
        case 'number':
            return formatNumber(value);
        case 'status':
            return formatStatus(value);
        case 'priority':
            return formatPriority(value);
        case 'uph':
            return formatUph(value);
        default:
            return value;
    }
}

// 格式化状态
function formatStatus(status) {
    if (!status) return '';
    const statusClass = getStatusClass(status);
    return `<span class="badge-status ${statusClass}">${status}</span>`;
}

// 格式化优先级
function formatPriority(priority) {
    if (!priority) return '';
    const priorityClass = getPriorityClass(priority);
    return `<span class="badge-status ${priorityClass}">${priority}</span>`;
}

// 格式化UPH
function formatUph(uph) {
    if (!uph) return '';
    const uphClass = getUphClass(parseFloat(uph));
    return `<span class="badge-status ${uphClass}">${uph}</span>`;
}

// 获取状态样式类
function getStatusClass(status) {
    const statusLower = status.toLowerCase();
    if (statusLower.includes('active') || statusLower.includes('运行')) return 'status-active';
    if (statusLower.includes('inactive') || statusLower.includes('停机')) return 'status-inactive';
    if (statusLower.includes('maintenance') || statusLower.includes('维护')) return 'status-maintenance';
    return 'status-unknown';
}

// 获取优先级样式类
function getPriorityClass(priority) {
    const priorityLower = priority.toLowerCase();
    if (priorityLower.includes('high') || priorityLower.includes('高')) return 'priority-high';
    if (priorityLower.includes('medium') || priorityLower.includes('中')) return 'priority-medium';
    if (priorityLower.includes('low') || priorityLower.includes('低')) return 'priority-low';
    return 'priority-medium';
}

// 获取UPH样式类
function getUphClass(uphValue) {
    if (uphValue >= 1000) return 'uph-high';
    if (uphValue >= 500) return 'uph-medium';
    return 'uph-low';
}

// 渲染分页
function renderPagination(result) {
    const pagination = document.getElementById('pagination');
    const totalPages = result.pages;
    const currentPageNum = currentPage;

    let html = '';

    // 上一页
    html += `
        <li class="page-item ${currentPageNum <= 1 ? 'disabled' : ''}">
            <a class="page-link" href="#" onclick="goToPage(${currentPageNum - 1})">
                <i class="fas fa-chevron-left"></i>
            </a>
        </li>
    `;

    // 页码
    const startPage = Math.max(1, currentPageNum - 2);
    const endPage = Math.min(totalPages, currentPageNum + 2);

    if (startPage > 1) {
        html += `<li class="page-item"><a class="page-link" href="#" onclick="goToPage(1)">1</a></li>`;
        if (startPage > 2) {
            html += `<li class="page-item disabled"><span class="page-link">...</span></li>`;
        }
    }

    for (let i = startPage; i <= endPage; i++) {
        html += `
            <li class="page-item ${i === currentPageNum ? 'active' : ''}">
                <a class="page-link" href="#" onclick="goToPage(${i})">${i}</a>
            </li>
        `;
    }

    if (endPage < totalPages) {
        if (endPage < totalPages - 1) {
            html += `<li class="page-item disabled"><span class="page-link">...</span></li>`;
        }
        html += `<li class="page-item"><a class="page-link" href="#" onclick="goToPage(${totalPages})">${totalPages}</a></li>`;
    }

    // 下一页
    html += `
        <li class="page-item ${currentPageNum >= totalPages ? 'disabled' : ''}">
            <a class="page-link" href="#" onclick="goToPage(${currentPageNum + 1})">
                <i class="fas fa-chevron-right"></i>
            </a>
        </li>
    `;

    pagination.innerHTML = html;
}

// 更新统计信息
function updateStats(data) {
    const statsContainer = document.getElementById('statsCards');

    // 基础统计
    const totalRecords = data.length;
    const stats = {
        total: totalRecords,
        active: 0,
        inactive: 0,
        high_priority: 0,
        avg_value: 0
    };

    // 根据表类型计算特定统计
    data.forEach(row => {
        // 状态统计
        const status = (row.STATUS || row.EQP_STATUS || '').toLowerCase();
        if (status.includes('active') || status.includes('运行')) {
            stats.active++;
        } else if (status.includes('inactive') || status.includes('停机')) {
            stats.inactive++;
        }

        // 优先级统计
        const priority = (row.PRIORITY || '').toLowerCase();
        if (priority.includes('high') || priority.includes('高')) {
            stats.high_priority++;
        }

        // 数值统计（UPH、良率等）
        const value = parseFloat(row.UPH || row.YIELD_RATE || row.FIRST_PASS_YIELD || 0);
        if (value > 0) {
            stats.avg_value += value;
        }
    });

    if (totalRecords > 0) {
        stats.avg_value = Math.round(stats.avg_value / totalRecords);
    }

    // 渲染统计卡片
    statsContainer.innerHTML = `
        <div class="col-md-3">
            <div class="stats-card">
                <div class="stats-number text-primary">${totalRecords}</div>
                <div class="stats-label">记录总数</div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="stats-card">
                <div class="stats-number text-success">${stats.active}</div>
                <div class="stats-label">活跃状态</div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="stats-card">
                <div class="stats-number text-warning">${stats.high_priority}</div>
                <div class="stats-label">高优先级</div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="stats-card">
                <div class="stats-number text-info">${stats.avg_value}</div>
                <div class="stats-label">平均值</div>
            </div>
        </div>
    `;
}

// 更新数据信息
function updateDataInfo(result) {
    const start = (currentPage - 1) * parseInt(document.getElementById('perPage').value) + 1;
    const end = Math.min(start + result.data.length - 1, result.total);

    document.getElementById('dataInfo').textContent =
        `显示第 ${start}-${end} 条，共 ${result.total} 条记录`;
}

// 绑定事件
function bindEvents() {
    // 每页数量变化
    document.getElementById('perPage').addEventListener('change', function() {
        currentPage = 1;
        loadData();
    });

    // 搜索框回车事件
    document.getElementById('globalSearch').addEventListener('keypress', function(e) {
        if (e.key === 'Enter') {
            performSearch();
        }
    });

    // 筛选值回车事件
    document.getElementById('filterValue').addEventListener('keypress', function(e) {
        if (e.key === 'Enter') {
            addFilter();
        }
    });
}

// 执行搜索
function performSearch() {
    currentSearch = document.getElementById('globalSearch').value;
    currentPage = 1;
    loadData();
}

// 显示/隐藏高级筛选
function showAdvancedFilters() {
    const filtersDiv = document.getElementById('advancedFilters');
    filtersDiv.style.display = filtersDiv.style.display === 'none' ? 'block' : 'none';
}

// 添加筛选条件
function addFilter() {
    const field = document.getElementById('filterField').value;
    const operator = document.getElementById('filterOperator').value;
    const value = document.getElementById('filterValue').value;

    if (!field || !value) {
        alert('请选择字段并输入筛选值');
        return;
    }

    // 检查是否已存在相同筛选条件
    const existingFilter = currentFilters.find(f =>
        f.field === field && f.operator === operator && f.value === value
    );

    if (existingFilter) {
        alert('该筛选条件已存在');
        return;
    }

    // 添加筛选条件
    currentFilters.push({ field, operator, value });

    // 清空输入
    document.getElementById('filterField').value = '';
    document.getElementById('filterValue').value = '';

    // 更新显示
    updateActiveFilters();

    // 重新加载数据
    currentPage = 1;
    loadData();
}

// 更新活动筛选器显示
function updateActiveFilters() {
    const container = document.getElementById('activeFilters');

    if (currentFilters.length === 0) {
        container.innerHTML = '<small class="text-muted">暂无筛选条件</small>';
        return;
    }

    container.innerHTML = currentFilters.map((filter, index) => `
        <span class="filter-badge">
            ${filter.field} ${getOperatorText(filter.operator)} "${filter.value}"
            <span class="remove-filter" onclick="removeFilter(${index})">×</span>
        </span>
    `).join('');
}

// 获取操作符文本
function getOperatorText(operator) {
    const operatorMap = {
        'contains': '包含',
        'equals': '等于',
        'starts_with': '开始于',
        'ends_with': '结束于',
        'greater_than': '大于',
        'less_than': '小于'
    };
    return operatorMap[operator] || operator;
}

// 移除筛选条件
function removeFilter(index) {
    currentFilters.splice(index, 1);
    updateActiveFilters();
    currentPage = 1;
    loadData();
}

// 清空所有筛选条件
function clearAllFilters() {
    currentFilters = [];
    updateActiveFilters();
    currentPage = 1;
    loadData();
}

// 排序
function sortBy(field) {
    if (currentSort.field === field) {
        currentSort.order = currentSort.order === 'asc' ? 'desc' : 'asc';
    } else {
        currentSort.field = field;
        currentSort.order = 'asc';
    }

    currentPage = 1;
    loadData();
}

// 更新排序指示器
function updateSortIndicators() {
    document.querySelectorAll('.sort-indicator').forEach(indicator => {
        indicator.className = 'fas fa-sort sort-indicator';
    });

    if (currentSort.field) {
        const header = document.querySelector(`th[onclick*="'${currentSort.field}'"] .sort-indicator`);
        if (header) {
            header.className = `fas fa-sort-${currentSort.order === 'asc' ? 'up' : 'down'} sort-indicator sort-active`;
        }
    }
}

// 跳转页面
function goToPage(page) {
    if (page >= 1 && page <= totalPages && page !== currentPage) {
        currentPage = page;
        loadData();
    }
}

// 刷新数据
function refreshData() {
    loadData();
}

// 导出数据
function exportData(format) {
    const params = new URLSearchParams({
        format: format
    });

    if (currentSearch) {
        params.append('search', currentSearch);
    }

    if (currentSort.field) {
        params.append('sort_by', currentSort.field);
        params.append('sort_order', currentSort.order);
    }

    if (currentFilters.length > 0) {
        params.append('filters', JSON.stringify(currentFilters));
    }

    const exportUrl = `${API_BASE}/tables/${TABLE_NAME}/export?${params}`;

    const link = document.createElement('a');
    link.href = exportUrl;
    link.style.display = 'none';
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
}

// 新增记录
function addNewRecord() {
    // 这里可以打开新增记录的模态框
    alert('新增功能开发中...');
}

// 编辑记录
function editRecord(id) {
    const record = currentData.find(item => item.id == id);
    if (!record) return;

    // 这里可以打开编辑记录的模态框
    alert(`编辑记录 ID: ${id}`);
}

// 查看详情
function viewDetails(id) {
    const record = currentData.find(item => item.id == id);
    if (!record) return;

    // 创建详情显示内容
    const detailsHtml = `
        <div class="row">
            ${currentColumns.map(column => `
                <div class="col-md-6 mb-2">
                    <strong>${column.display_name || column.name}:</strong>
                    <span class="ms-2">${formatCellValue(record[column.name], column)}</span>
                </div>
            `).join('')}
        </div>
    `;

    // 显示详情模态框
    const modal = document.createElement('div');
    modal.className = 'modal fade';
    modal.innerHTML = `
        <div class="modal-dialog modal-xl">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">${PAGE_CONFIG.title}详情</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body">
                    ${detailsHtml}
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">关闭</button>
                </div>
            </div>
        </div>
    `;

    document.body.appendChild(modal);
    const bsModal = new bootstrap.Modal(modal);
    bsModal.show();

    // 模态框关闭后移除DOM元素
    modal.addEventListener('hidden.bs.modal', function() {
        document.body.removeChild(modal);
    });
}

// 删除记录
async function deleteRecord(id) {
    if (!confirm('确定要删除这条记录吗？')) return;

    try {
        const response = await fetch(`${API_BASE}/tables/${TABLE_NAME}/data/${id}`, {
            method: 'DELETE'
        });

        const result = await response.json();

        if (result.success) {
            showSuccess('删除成功');
            loadData();
        } else {
            showError('删除失败: ' + result.error);
        }

    } catch (error) {
        showError('请求失败: ' + error.message);
    }
}

// 工具函数
function formatDateTime(dateStr) {
    if (!dateStr) return '';
    try {
        return new Date(dateStr).toLocaleString('zh-CN');
    } catch {
        return dateStr;
    }
}

function formatDate(dateStr) {
    if (!dateStr) return '';
    try {
        return new Date(dateStr).toLocaleDateString('zh-CN');
    } catch {
        return dateStr;
    }
}

function formatNumber(num) {
    if (!num && num !== 0) return '';
    return parseFloat(num).toLocaleString();
}

function showLoading(show) {
    document.getElementById('loadingOverlay').style.display = show ? 'flex' : 'none';
}

function showSuccess(message) {
    // 可以使用更好的通知组件
    alert('✅ ' + message);
}

function showError(message) {
    // 可以使用更好的通知组件
    alert('❌ ' + message);
}

console.log('✅ 通用资源管理v3 完整功能加载完成');
</script>
{% endblock %}
