<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>产品周期管理 v3 - APS平台</title>
    <link href="{{ url_for('static', filename='vendor/bootstrap/bootstrap.min.css') }}" rel="stylesheet">
    <link href="{{ url_for('static', filename='vendor/fontawesome/css/all.min.css') }}" rel="stylesheet">
    <style>
        .page-header {
            background: linear-gradient(135deg, #fd7e14 0%, #ffc107 100%);
            color: white;
            padding: 2rem 0;
            margin-bottom: 2rem;
        }
        .feature-card {
            border: 1px solid #ddd;
            border-radius: 8px;
            padding: 20px;
            margin-bottom: 20px;
            background: white;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        .search-toolbar {
            background: #f8f9fa;
            border-radius: 8px;
            padding: 15px;
            margin-bottom: 20px;
        }
        .table-container {
            background: white;
            border-radius: 8px;
            overflow: hidden;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        .data-table {
            margin: 0;
        }
        .stage-badge {
            padding: 4px 8px;
            border-radius: 4px;
            font-size: 12px;
            font-weight: bold;
        }
        .stage-ft { background-color: #fff3cd; color: #856404; }
        .stage-cp { background-color: #d1ecf1; color: #0c5460; }
        .stage-sort { background-color: #d4edda; color: #155724; }
        .stage-other { background-color: #e2e3e5; color: #383d41; }
        .ct-value-cell {
            text-align: right;
            font-weight: bold;
            color: #fd7e14;
        }
        .pagination-container {
            background: white;
            padding: 15px;
            border-top: 1px solid #dee2e6;
        }
        .stats-cards {
            margin-bottom: 20px;
        }
        .stats-card {
            background: white;
            border-radius: 8px;
            padding: 20px;
            text-align: center;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        .stats-number {
            font-size: 2rem;
            font-weight: bold;
            margin-bottom: 5px;
        }
        .stats-label {
            color: #6c757d;
            font-size: 14px;
        }
        .loading-overlay {
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: rgba(255,255,255,0.8);
            display: flex;
            align-items: center;
            justify-content: center;
            z-index: 1000;
        }
        .sort-header {
            cursor: pointer;
            user-select: none;
        }
        .sort-header:hover {
            background-color: #f8f9fa;
        }
        .sort-indicator {
            margin-left: 5px;
            opacity: 0.5;
        }
        .sort-active {
            opacity: 1;
        }
        .product-cell {
            font-family: 'Monaco', 'Consolas', monospace;
            font-weight: bold;
            color: #fd7e14;
        }
        .lot-id-cell {
            font-family: 'Monaco', 'Consolas', monospace;
            font-weight: bold;
            color: #0066cc;
        }
        .yield-cell {
            text-align: right;
            font-weight: bold;
        }
        .yield-high { color: #28a745; }
        .yield-medium { color: #ffc107; }
        .yield-low { color: #dc3545; }
    </style>
</head>
<body>
    <!-- 页面头部 -->
    <div class="page-header">
        <div class="container-fluid">
            <div class="row align-items-center">
                <div class="col-md-8">
                    <h1 class="mb-1">
                        <i class="fas fa-clock me-2"></i>产品周期管理 v3
                    </h1>
                    <p class="mb-0 opacity-75">基于API v3的现代化产品生产周期管理系统</p>
                </div>
                <div class="col-md-4 text-end">
                    <span class="badge bg-success fs-6">API v3</span>
                    <span class="badge bg-info fs-6 ms-2">实时数据</span>
                </div>
            </div>
        </div>
    </div>

    <div class="container-fluid">
        <!-- 统计卡片 -->
        <div class="row stats-cards">
            <div class="col-md-3">
                <div class="stats-card">
                    <div class="stats-number text-primary" id="totalLots">0</div>
                    <div class="stats-label">批次总数</div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="stats-card">
                    <div class="stats-number text-success" id="avgYield">0%</div>
                    <div class="stats-label">平均良率</div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="stats-card">
                    <div class="stats-number text-warning" id="totalProducts">0</div>
                    <div class="stats-label">产品数量</div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="stats-card">
                    <div class="stats-number text-info" id="totalStages">0</div>
                    <div class="stats-label">工序数量</div>
                </div>
            </div>
        </div>

        <!-- 操作工具栏 -->
        <div class="search-toolbar">
            <div class="row align-items-center">
                <div class="col-md-4">
                    <div class="input-group">
                        <span class="input-group-text">
                            <i class="fas fa-search"></i>
                        </span>
                        <input type="text" class="form-control" id="globalSearch" 
                               placeholder="搜索批次号、产品名称、工序...">
                        <button class="btn btn-warning" onclick="performSearch()">搜索</button>
                    </div>
                </div>
                <div class="col-md-2">
                    <select class="form-select" id="stageFilter">
                        <option value="">所有工序</option>
                        <option value="FT">FT</option>
                        <option value="CP">CP</option>
                        <option value="SORT">SORT</option>
                    </select>
                </div>
                <div class="col-md-2">
                    <select class="form-select" id="perPage">
                        <option value="25">25条/页</option>
                        <option value="50" selected>50条/页</option>
                        <option value="100">100条/页</option>
                    </select>
                </div>
                <div class="col-md-4">
                    <div class="btn-group" role="group">
                        <button class="btn btn-success" onclick="refreshData()">
                            <i class="fas fa-sync me-1"></i>刷新
                        </button>
                        <button class="btn btn-info" onclick="exportData()">
                            <i class="fas fa-download me-1"></i>导出
                        </button>
                        <button class="btn btn-warning" onclick="addNewRecord()">
                            <i class="fas fa-plus me-1"></i>新增
                        </button>
                        <button class="btn btn-primary" onclick="showCycleAnalysis()">
                            <i class="fas fa-chart-pie me-1"></i>周期分析
                        </button>
                    </div>
                </div>
            </div>
        </div>

        <!-- 数据表格 -->
        <div class="table-container position-relative">
            <div id="loadingOverlay" class="loading-overlay" style="display: none;">
                <div class="text-center">
                    <div class="spinner-border text-warning" role="status">
                        <span class="visually-hidden">加载中...</span>
                    </div>
                    <div class="mt-2">正在加载数据...</div>
                </div>
            </div>
            
            <div class="table-responsive">
                <table class="table table-hover data-table" id="dataTable">
                    <thead class="table-light">
                        <tr>
                            <th class="sort-header" onclick="sortBy('id')">
                                ID <i class="fas fa-sort sort-indicator"></i>
                            </th>
                            <th class="sort-header" onclick="sortBy('LOT_ID')">
                                批次号 <i class="fas fa-sort sort-indicator"></i>
                            </th>
                            <th class="sort-header" onclick="sortBy('DEVICE')">
                                产品名称 <i class="fas fa-sort sort-indicator"></i>
                            </th>
                            <th class="sort-header" onclick="sortBy('STAGE')">
                                工序 <i class="fas fa-sort sort-indicator"></i>
                            </th>
                            <th class="sort-header" onclick="sortBy('LOT_QTY')">
                                批次数量 <i class="fas fa-sort sort-indicator"></i>
                            </th>
                            <th class="sort-header" onclick="sortBy('GOOD_QTY')">
                                良品数量 <i class="fas fa-sort sort-indicator"></i>
                            </th>
                            <th class="sort-header" onclick="sortBy('FIRST_PASS_YIELD')">
                                良率 <i class="fas fa-sort sort-indicator"></i>
                            </th>
                            <th class="sort-header" onclick="sortBy('MAIN_EQP_ID')">
                                主设备 <i class="fas fa-sort sort-indicator"></i>
                            </th>
                            <th class="sort-header" onclick="sortBy('LOT_START_TIME')">
                                开始时间 <i class="fas fa-sort sort-indicator"></i>
                            </th>
                            <th class="sort-header" onclick="sortBy('LOT_END_TIME')">
                                结束时间 <i class="fas fa-sort sort-indicator"></i>
                            </th>
                            <th>操作</th>
                        </tr>
                    </thead>
                    <tbody id="tableBody">
                        <tr>
                            <td colspan="11" class="text-center py-4">
                                <i class="fas fa-spinner fa-spin me-2"></i>正在加载数据...
                            </td>
                        </tr>
                    </tbody>
                </table>
            </div>
            
            <!-- 分页导航 -->
            <div class="pagination-container">
                <div class="row align-items-center">
                    <div class="col-md-6">
                        <div id="dataInfo" class="text-muted">
                            显示第 1-50 条，共 0 条记录
                        </div>
                    </div>
                    <div class="col-md-6">
                        <nav>
                            <ul class="pagination justify-content-end mb-0" id="pagination">
                                <!-- 分页按钮将通过JavaScript生成 -->
                            </ul>
                        </nav>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- 编辑模态框 -->
    <div class="modal fade" id="editModal" tabindex="-1">
        <div class="modal-dialog modal-lg">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">编辑产品周期</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body">
                    <form id="editForm">
                        <input type="hidden" id="editId">
                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label class="form-label">批次号</label>
                                    <input type="text" class="form-control" id="editLotId" required>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label class="form-label">产品名称</label>
                                    <input type="text" class="form-control" id="editDevice" required>
                                </div>
                            </div>
                        </div>
                        <div class="row">
                            <div class="col-md-4">
                                <div class="mb-3">
                                    <label class="form-label">工序</label>
                                    <select class="form-select" id="editStage" required>
                                        <option value="">请选择工序</option>
                                        <option value="FT">FT</option>
                                        <option value="CP">CP</option>
                                        <option value="SORT">SORT</option>
                                    </select>
                                </div>
                            </div>
                            <div class="col-md-4">
                                <div class="mb-3">
                                    <label class="form-label">批次数量</label>
                                    <input type="number" class="form-control" id="editLotQty" min="1">
                                </div>
                            </div>
                            <div class="col-md-4">
                                <div class="mb-3">
                                    <label class="form-label">良品数量</label>
                                    <input type="number" class="form-control" id="editGoodQty" min="0">
                                </div>
                            </div>
                        </div>
                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label class="form-label">主设备ID</label>
                                    <input type="text" class="form-control" id="editMainEqpId">
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label class="form-label">辅助设备ID</label>
                                    <input type="text" class="form-control" id="editAuxiliaryEqpId">
                                </div>
                            </div>
                        </div>
                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label class="form-label">开始时间</label>
                                    <input type="datetime-local" class="form-control" id="editStartTime">
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label class="form-label">结束时间</label>
                                    <input type="datetime-local" class="form-control" id="editEndTime">
                                </div>
                            </div>
                        </div>
                    </form>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
                    <button type="button" class="btn btn-warning" onclick="saveEdit()">保存</button>
                </div>
            </div>
        </div>
    </div>

    <!-- 周期分析模态框 -->
    <div class="modal fade" id="cycleAnalysisModal" tabindex="-1">
        <div class="modal-dialog modal-xl">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">产品周期分析报告</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body">
                    <div id="cycleAnalysisContent">
                        <div class="text-center">
                            <div class="spinner-border text-warning" role="status">
                                <span class="visually-hidden">分析中...</span>
                            </div>
                            <div class="mt-2">正在分析产品周期数据...</div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="{{ url_for('static', filename='vendor/bootstrap/bootstrap.bundle.min.js') }}"></script>
    <script>
        // 全局变量
        const API_BASE = '/api/v3';
        const TABLE_NAME = 'ct';
        let currentData = [];
        let currentPage = 1;
        let totalPages = 1;
        let currentSort = { field: '', order: 'asc' };
        let currentFilters = [];

        // 页面加载时初始化
        document.addEventListener('DOMContentLoaded', function() {
            console.log('🚀 产品周期管理v3页面加载完成');
            loadData();

            // 绑定事件
            document.getElementById('stageFilter').addEventListener('change', applyFilters);
            document.getElementById('perPage').addEventListener('change', function() {
                currentPage = 1;
                loadData();
            });

            // 搜索框回车事件
            document.getElementById('globalSearch').addEventListener('keypress', function(e) {
                if (e.key === 'Enter') {
                    performSearch();
                }
            });
        });

        // 加载数据
        async function loadData() {
            showLoading(true);

            try {
                const params = new URLSearchParams({
                    page: currentPage,
                    per_page: document.getElementById('perPage').value
                });

                // 添加搜索条件
                const searchTerm = document.getElementById('globalSearch').value;
                if (searchTerm) {
                    params.append('search', searchTerm);
                }

                // 添加排序条件
                if (currentSort.field) {
                    params.append('sort_by', currentSort.field);
                    params.append('sort_order', currentSort.order);
                }

                // 添加筛选条件
                if (currentFilters.length > 0) {
                    params.append('filters', JSON.stringify(currentFilters));
                }

                const response = await fetch(`${API_BASE}/tables/${TABLE_NAME}/data?${params}`);
                const result = await response.json();

                if (result.success) {
                    currentData = result.data;
                    totalPages = result.pages;
                    renderTable(result.data);
                    renderPagination(result);
                    updateStats(result.data);
                    updateDataInfo(result);
                } else {
                    showError('数据加载失败: ' + result.error);
                }

            } catch (error) {
                showError('请求失败: ' + error.message);
            } finally {
                showLoading(false);
            }
        }

        // 渲染表格
        function renderTable(data) {
            const tbody = document.getElementById('tableBody');

            if (!data || data.length === 0) {
                tbody.innerHTML = `
                    <tr>
                        <td colspan="11" class="text-center py-4">
                            <i class="fas fa-inbox fa-2x text-muted mb-2"></i>
                            <div>暂无数据</div>
                        </td>
                    </tr>
                `;
                return;
            }

            tbody.innerHTML = data.map(row => `
                <tr>
                    <td>${row.id || ''}</td>
                    <td class="lot-id-cell">${row.LOT_ID || ''}</td>
                    <td class="product-cell">${row.DEVICE || ''}</td>
                    <td>
                        <span class="stage-badge stage-${(row.STAGE || '').toLowerCase()}">
                            ${row.STAGE || ''}
                        </span>
                    </td>
                    <td class="text-end">${formatNumber(row.LOT_QTY)}</td>
                    <td class="text-end">${formatNumber(row.GOOD_QTY)}</td>
                    <td class="yield-cell">
                        <span class="yield-${getYieldClass(row.FIRST_PASS_YIELD)}">
                            ${formatYield(row.FIRST_PASS_YIELD)}
                        </span>
                    </td>
                    <td>${row.MAIN_EQP_ID || ''}</td>
                    <td>${formatDateTime(row.LOT_START_TIME)}</td>
                    <td>${formatDateTime(row.LOT_END_TIME)}</td>
                    <td>
                        <div class="btn-group btn-group-sm">
                            <button class="btn btn-outline-primary" onclick="editRecord(${row.id})" title="编辑">
                                <i class="fas fa-edit"></i>
                            </button>
                            <button class="btn btn-outline-info" onclick="viewDetails(${row.id})" title="详情">
                                <i class="fas fa-eye"></i>
                            </button>
                            <button class="btn btn-outline-danger" onclick="deleteRecord(${row.id})" title="删除">
                                <i class="fas fa-trash"></i>
                            </button>
                        </div>
                    </td>
                </tr>
            `).join('');
        }

        // 渲染分页
        function renderPagination(result) {
            const pagination = document.getElementById('pagination');
            const totalPages = result.pages;
            const currentPageNum = currentPage;

            let html = '';

            // 上一页
            html += `
                <li class="page-item ${currentPageNum <= 1 ? 'disabled' : ''}">
                    <a class="page-link" href="#" onclick="goToPage(${currentPageNum - 1})">
                        <i class="fas fa-chevron-left"></i>
                    </a>
                </li>
            `;

            // 页码
            const startPage = Math.max(1, currentPageNum - 2);
            const endPage = Math.min(totalPages, currentPageNum + 2);

            if (startPage > 1) {
                html += `<li class="page-item"><a class="page-link" href="#" onclick="goToPage(1)">1</a></li>`;
                if (startPage > 2) {
                    html += `<li class="page-item disabled"><span class="page-link">...</span></li>`;
                }
            }

            for (let i = startPage; i <= endPage; i++) {
                html += `
                    <li class="page-item ${i === currentPageNum ? 'active' : ''}">
                        <a class="page-link" href="#" onclick="goToPage(${i})">${i}</a>
                    </li>
                `;
            }

            if (endPage < totalPages) {
                if (endPage < totalPages - 1) {
                    html += `<li class="page-item disabled"><span class="page-link">...</span></li>`;
                }
                html += `<li class="page-item"><a class="page-link" href="#" onclick="goToPage(${totalPages})">${totalPages}</a></li>`;
            }

            // 下一页
            html += `
                <li class="page-item ${currentPageNum >= totalPages ? 'disabled' : ''}">
                    <a class="page-link" href="#" onclick="goToPage(${currentPageNum + 1})">
                        <i class="fas fa-chevron-right"></i>
                    </a>
                </li>
            `;

            pagination.innerHTML = html;
        }

        // 更新统计信息
        function updateStats(data) {
            const totalLots = data.length;
            const totalProducts = new Set(data.map(row => row.DEVICE)).size;
            const totalStages = new Set(data.map(row => row.STAGE)).size;

            // 计算平均良率
            const yieldValues = data.map(row => parseFloat(row.FIRST_PASS_YIELD) || 0).filter(yield => yield > 0);
            const avgYield = yieldValues.length > 0 ?
                Math.round(yieldValues.reduce((a, b) => a + b, 0) / yieldValues.length) : 0;

            document.getElementById('totalLots').textContent = totalLots;
            document.getElementById('avgYield').textContent = avgYield + '%';
            document.getElementById('totalProducts').textContent = totalProducts;
            document.getElementById('totalStages').textContent = totalStages;
        }

        // 更新数据信息
        function updateDataInfo(result) {
            const start = (currentPage - 1) * parseInt(document.getElementById('perPage').value) + 1;
            const end = Math.min(start + result.data.length - 1, result.total);

            document.getElementById('dataInfo').textContent =
                `显示第 ${start}-${end} 条，共 ${result.total} 条记录`;
        }

        // 执行搜索
        function performSearch() {
            currentPage = 1;
            loadData();
        }

        // 应用筛选
        function applyFilters() {
            const stageFilter = document.getElementById('stageFilter').value;

            currentFilters = [];
            if (stageFilter) {
                currentFilters.push({
                    field: 'STAGE',
                    operator: 'equals',
                    value: stageFilter
                });
            }

            currentPage = 1;
            loadData();
        }

        // 排序
        function sortBy(field) {
            if (currentSort.field === field) {
                currentSort.order = currentSort.order === 'asc' ? 'desc' : 'asc';
            } else {
                currentSort.field = field;
                currentSort.order = 'asc';
            }

            // 更新排序指示器
            updateSortIndicators();

            currentPage = 1;
            loadData();
        }

        // 更新排序指示器
        function updateSortIndicators() {
            document.querySelectorAll('.sort-indicator').forEach(indicator => {
                indicator.className = 'fas fa-sort sort-indicator';
            });

            if (currentSort.field) {
                const header = document.querySelector(`th[onclick="sortBy('${currentSort.field}')"] .sort-indicator`);
                if (header) {
                    header.className = `fas fa-sort-${currentSort.order === 'asc' ? 'up' : 'down'} sort-indicator sort-active`;
                }
            }
        }

        // 跳转页面
        function goToPage(page) {
            if (page >= 1 && page <= totalPages && page !== currentPage) {
                currentPage = page;
                loadData();
            }
        }

        // 刷新数据
        function refreshData() {
            loadData();
        }

        // 导出数据
        function exportData() {
            const params = new URLSearchParams({
                format: 'excel'
            });

            const searchTerm = document.getElementById('globalSearch').value;
            if (searchTerm) {
                params.append('search', searchTerm);
            }

            if (currentSort.field) {
                params.append('sort_by', currentSort.field);
                params.append('sort_order', currentSort.order);
            }

            if (currentFilters.length > 0) {
                params.append('filters', JSON.stringify(currentFilters));
            }

            const exportUrl = `${API_BASE}/tables/${TABLE_NAME}/export?${params}`;

            const link = document.createElement('a');
            link.href = exportUrl;
            link.style.display = 'none';
            document.body.appendChild(link);
            link.click();
            document.body.removeChild(link);
        }

        // 新增记录
        function addNewRecord() {
            // 清空表单
            document.getElementById('editForm').reset();
            document.getElementById('editId').value = '';
            document.querySelector('#editModal .modal-title').textContent = '新增产品周期';

            // 显示模态框
            new bootstrap.Modal(document.getElementById('editModal')).show();
        }

        // 编辑记录
        function editRecord(id) {
            const record = currentData.find(item => item.id == id);
            if (!record) return;

            // 填充表单
            document.getElementById('editId').value = record.id;
            document.getElementById('editLotId').value = record.LOT_ID || '';
            document.getElementById('editDevice').value = record.DEVICE || '';
            document.getElementById('editStage').value = record.STAGE || '';
            document.getElementById('editLotQty').value = record.LOT_QTY || '';
            document.getElementById('editGoodQty').value = record.GOOD_QTY || '';
            document.getElementById('editMainEqpId').value = record.MAIN_EQP_ID || '';
            document.getElementById('editAuxiliaryEqpId').value = record.AUXILIARY_EQP_ID || '';

            // 处理时间字段
            if (record.LOT_START_TIME) {
                document.getElementById('editStartTime').value = formatDateTimeForInput(record.LOT_START_TIME);
            }
            if (record.LOT_END_TIME) {
                document.getElementById('editEndTime').value = formatDateTimeForInput(record.LOT_END_TIME);
            }

            document.querySelector('#editModal .modal-title').textContent = '编辑产品周期';

            // 显示模态框
            new bootstrap.Modal(document.getElementById('editModal')).show();
        }

        // 查看详情
        function viewDetails(id) {
            const record = currentData.find(item => item.id == id);
            if (!record) return;

            // 计算良率
            const yieldRate = record.LOT_QTY && record.GOOD_QTY ?
                ((record.GOOD_QTY / record.LOT_QTY) * 100).toFixed(2) + '%' : '未知';

            // 计算周期时间
            const cycleTime = record.LOT_START_TIME && record.LOT_END_TIME ?
                calculateCycleTime(record.LOT_START_TIME, record.LOT_END_TIME) : '未知';

            // 创建详情显示内容
            const detailsHtml = `
                <div class="row">
                    <div class="col-md-6">
                        <h6>基本信息</h6>
                        <table class="table table-sm">
                            <tr><td>批次号:</td><td class="lot-id-cell">${record.LOT_ID || ''}</td></tr>
                            <tr><td>产品名称:</td><td class="product-cell">${record.DEVICE || ''}</td></tr>
                            <tr><td>工序:</td><td><span class="stage-badge stage-${(record.STAGE || '').toLowerCase()}">${record.STAGE || ''}</span></td></tr>
                            <tr><td>芯片ID:</td><td>${record.CHIP_ID || ''}</td></tr>
                            <tr><td>封装型号:</td><td>${record.PKG_PN || ''}</td></tr>
                        </table>
                    </div>
                    <div class="col-md-6">
                        <h6>数量信息</h6>
                        <table class="table table-sm">
                            <tr><td>批次数量:</td><td>${formatNumber(record.LOT_QTY)}</td></tr>
                            <tr><td>实际数量:</td><td>${formatNumber(record.ACT_QTY)}</td></tr>
                            <tr><td>良品数量:</td><td>${formatNumber(record.GOOD_QTY)}</td></tr>
                            <tr><td>不良数量:</td><td>${formatNumber(record.REJECT_QTY)}</td></tr>
                            <tr><td>良率:</td><td><span class="yield-${getYieldClass(record.FIRST_PASS_YIELD)}">${yieldRate}</span></td></tr>
                        </table>
                    </div>
                </div>
                <div class="row">
                    <div class="col-md-6">
                        <h6>设备信息</h6>
                        <table class="table table-sm">
                            <tr><td>主设备:</td><td>${record.MAIN_EQP_ID || ''}</td></tr>
                            <tr><td>辅助设备:</td><td>${record.AUXILIARY_EQP_ID || ''}</td></tr>
                            <tr><td>测试程序:</td><td>${record.FT_TEST_PROGRAM || ''}</td></tr>
                        </table>
                    </div>
                    <div class="col-md-6">
                        <h6>时间信息</h6>
                        <table class="table table-sm">
                            <tr><td>开始时间:</td><td>${formatDateTime(record.LOT_START_TIME)}</td></tr>
                            <tr><td>结束时间:</td><td>${formatDateTime(record.LOT_END_TIME)}</td></tr>
                            <tr><td>周期时间:</td><td>${cycleTime}</td></tr>
                            <tr><td>创建时间:</td><td>${formatDateTime(record.created_at)}</td></tr>
                            <tr><td>更新时间:</td><td>${formatDateTime(record.updated_at)}</td></tr>
                        </table>
                    </div>
                </div>
            `;

            // 显示详情模态框
            const modal = document.createElement('div');
            modal.className = 'modal fade';
            modal.innerHTML = `
                <div class="modal-dialog modal-xl">
                    <div class="modal-content">
                        <div class="modal-header">
                            <h5 class="modal-title">产品周期详情</h5>
                            <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                        </div>
                        <div class="modal-body">
                            ${detailsHtml}
                        </div>
                        <div class="modal-footer">
                            <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">关闭</button>
                        </div>
                    </div>
                </div>
            `;

            document.body.appendChild(modal);
            const bsModal = new bootstrap.Modal(modal);
            bsModal.show();

            // 模态框关闭后移除DOM元素
            modal.addEventListener('hidden.bs.modal', function() {
                document.body.removeChild(modal);
            });
        }

        // 保存编辑
        async function saveEdit() {
            const id = document.getElementById('editId').value;
            const data = {
                LOT_ID: document.getElementById('editLotId').value,
                DEVICE: document.getElementById('editDevice').value,
                STAGE: document.getElementById('editStage').value,
                LOT_QTY: document.getElementById('editLotQty').value,
                GOOD_QTY: document.getElementById('editGoodQty').value,
                MAIN_EQP_ID: document.getElementById('editMainEqpId').value,
                AUXILIARY_EQP_ID: document.getElementById('editAuxiliaryEqpId').value,
                LOT_START_TIME: document.getElementById('editStartTime').value,
                LOT_END_TIME: document.getElementById('editEndTime').value
            };

            try {
                let response;
                if (id) {
                    // 更新
                    response = await fetch(`${API_BASE}/tables/${TABLE_NAME}/data/${id}`, {
                        method: 'PUT',
                        headers: { 'Content-Type': 'application/json' },
                        body: JSON.stringify(data)
                    });
                } else {
                    // 新增
                    response = await fetch(`${API_BASE}/tables/${TABLE_NAME}/data`, {
                        method: 'POST',
                        headers: { 'Content-Type': 'application/json' },
                        body: JSON.stringify(data)
                    });
                }

                const result = await response.json();

                if (result.success) {
                    bootstrap.Modal.getInstance(document.getElementById('editModal')).hide();
                    showSuccess(id ? '更新成功' : '新增成功');
                    loadData();
                } else {
                    showError('保存失败: ' + result.error);
                }

            } catch (error) {
                showError('请求失败: ' + error.message);
            }
        }

        // 删除记录
        async function deleteRecord(id) {
            if (!confirm('确定要删除这条记录吗？')) return;

            try {
                const response = await fetch(`${API_BASE}/tables/${TABLE_NAME}/data/${id}`, {
                    method: 'DELETE'
                });

                const result = await response.json();

                if (result.success) {
                    showSuccess('删除成功');
                    loadData();
                } else {
                    showError('删除失败: ' + result.error);
                }

            } catch (error) {
                showError('请求失败: ' + error.message);
            }
        }

        // 显示周期分析
        function showCycleAnalysis() {
            const modal = new bootstrap.Modal(document.getElementById('cycleAnalysisModal'));
            modal.show();

            // 生成分析报告
            generateCycleAnalysis();
        }

        // 生成周期分析报告
        function generateCycleAnalysis() {
            const content = document.getElementById('cycleAnalysisContent');

            setTimeout(() => {
                const stages = [...new Set(currentData.map(row => row.STAGE))];
                const products = [...new Set(currentData.map(row => row.DEVICE))];

                const stageStats = stages.map(stage => {
                    const stageData = currentData.filter(row => row.STAGE === stage);
                    const totalQty = stageData.reduce((sum, row) => sum + (parseInt(row.LOT_QTY) || 0), 0);
                    const goodQty = stageData.reduce((sum, row) => sum + (parseInt(row.GOOD_QTY) || 0), 0);
                    const avgYield = totalQty > 0 ? ((goodQty / totalQty) * 100).toFixed(2) : 0;
                    return { stage, count: stageData.length, totalQty, goodQty, avgYield };
                });

                const productStats = products.slice(0, 10).map(product => {
                    const productData = currentData.filter(row => row.DEVICE === product);
                    const totalQty = productData.reduce((sum, row) => sum + (parseInt(row.LOT_QTY) || 0), 0);
                    const goodQty = productData.reduce((sum, row) => sum + (parseInt(row.GOOD_QTY) || 0), 0);
                    const avgYield = totalQty > 0 ? ((goodQty / totalQty) * 100).toFixed(2) : 0;
                    return { product, count: productData.length, totalQty, goodQty, avgYield };
                });

                content.innerHTML = `
                    <div class="row">
                        <div class="col-md-6">
                            <h6>工序统计</h6>
                            <div class="table-responsive">
                                <table class="table table-sm">
                                    <thead>
                                        <tr>
                                            <th>工序</th>
                                            <th>批次数</th>
                                            <th>总数量</th>
                                            <th>良品数</th>
                                            <th>平均良率</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        ${stageStats.map(stat => `
                                            <tr>
                                                <td><span class="stage-badge stage-${stat.stage.toLowerCase()}">${stat.stage}</span></td>
                                                <td>${stat.count}</td>
                                                <td>${stat.totalQty.toLocaleString()}</td>
                                                <td>${stat.goodQty.toLocaleString()}</td>
                                                <td><span class="yield-${getYieldClass(stat.avgYield)}">${stat.avgYield}%</span></td>
                                            </tr>
                                        `).join('')}
                                    </tbody>
                                </table>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <h6>产品统计 (Top 10)</h6>
                            <div class="table-responsive">
                                <table class="table table-sm">
                                    <thead>
                                        <tr>
                                            <th>产品</th>
                                            <th>批次数</th>
                                            <th>总数量</th>
                                            <th>平均良率</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        ${productStats.map(stat => `
                                            <tr>
                                                <td class="product-cell">${stat.product}</td>
                                                <td>${stat.count}</td>
                                                <td>${stat.totalQty.toLocaleString()}</td>
                                                <td><span class="yield-${getYieldClass(stat.avgYield)}">${stat.avgYield}%</span></td>
                                            </tr>
                                        `).join('')}
                                    </tbody>
                                </table>
                            </div>
                        </div>
                    </div>
                `;
            }, 1000);
        }

        // 工具函数
        function formatNumber(num) {
            if (!num) return '';
            return parseInt(num).toLocaleString();
        }

        function formatYield(yield) {
            if (!yield) return '';
            return parseFloat(yield).toFixed(2) + '%';
        }

        function getYieldClass(yield) {
            const yieldValue = parseFloat(yield) || 0;
            if (yieldValue >= 95) return 'high';
            if (yieldValue >= 85) return 'medium';
            return 'low';
        }

        function formatDateTime(dateStr) {
            if (!dateStr) return '';
            try {
                return new Date(dateStr).toLocaleString('zh-CN');
            } catch {
                return dateStr;
            }
        }

        function formatDateTimeForInput(dateStr) {
            if (!dateStr) return '';
            try {
                const date = new Date(dateStr);
                return date.toISOString().slice(0, 16);
            } catch {
                return '';
            }
        }

        function calculateCycleTime(startTime, endTime) {
            if (!startTime || !endTime) return '未知';
            try {
                const start = new Date(startTime);
                const end = new Date(endTime);
                const diffMs = end - start;
                const diffHours = Math.round(diffMs / (1000 * 60 * 60) * 10) / 10;
                return diffHours + ' 小时';
            } catch {
                return '未知';
            }
        }

        function showLoading(show) {
            document.getElementById('loadingOverlay').style.display = show ? 'flex' : 'none';
        }

        function showSuccess(message) {
            alert('✅ ' + message);
        }

        function showError(message) {
            alert('❌ ' + message);
        }
    </script>
</body>
</html>
