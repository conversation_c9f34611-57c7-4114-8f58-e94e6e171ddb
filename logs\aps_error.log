2025-06-18 08:41:39 | ERROR    | test_app | 这是一条测试错误
2025-06-18 09:13:18 | ERROR    | __main__ | 错误日志测试
2025-06-18 09:13:47 | ERROR    | test_logging | 测试错误日志记录
2025-06-18 09:52:58 | ERROR    | app.api_v2.auth.routes | 检查会话状态失败: 'User' object has no attribute 'id'
2025-06-18 09:53:25 | ERROR    | app.api_v2.auth.routes | 检查会话状态失败: 'User' object has no attribute 'id'
2025-06-18 09:58:26 | ERROR    | app.api_v2.auth.routes | 检查会话状态失败: 'User' object has no attribute 'id'
2025-06-18 11:03:37 | ERROR    | app | 清理日志失败: No module named 'app.models.unified.user_action_log'
2025-06-18 11:03:41 | ERROR    | app | 清理日志失败: No module named 'app.models.unified.user_action_log'
2025-06-18 14:14:21 | ERROR    | app.utils.email_processor | 更新邮箱配置出错: Foreign key associated with column 'email_configs.created_by' could not find table 'users' with which to generate a foreign key to target column 'username'
2025-06-18 14:14:27 | ERROR    | app.utils.email_processor | 更新邮箱配置出错: Foreign key associated with column 'email_configs.created_by' could not find table 'users' with which to generate a foreign key to target column 'username'
2025-06-18 14:29:54 | ERROR    | app.utils.email_processor | 更新邮箱配置出错: Foreign key associated with column 'email_configs.created_by' could not find table 'users' with which to generate a foreign key to target column 'username'
2025-06-18 14:29:55 | ERROR    | app.utils.email_processor | 更新邮箱配置出错: Foreign key associated with column 'email_configs.created_by' could not find table 'users' with which to generate a foreign key to target column 'username'
2025-06-18 14:36:10 | ERROR    | app.api.auth | 删除用户 123 时出错: (pymysql.err.ProgrammingError) (1064, "You have an error in your SQL syntax; check the manual that corresponds to your MySQL server version for the right syntax to use near 'PRAGMA foreign_keys = OFF' at line 1")
[SQL: PRAGMA foreign_keys = OFF]
(Background on this error at: https://sqlalche.me/e/20/f405)
2025-06-18 14:39:37 | ERROR    | app.api.auth | 删除用户 123 时出错: (pymysql.err.ProgrammingError) (1064, "You have an error in your SQL syntax; check the manual that corresponds to your MySQL server version for the right syntax to use near 'PRAGMA foreign_keys = OFF' at line 1")
[SQL: PRAGMA foreign_keys = OFF]
(Background on this error at: https://sqlalche.me/e/20/f405)
2025-06-18 14:40:22 | ERROR    | app.api.auth | 删除用户 123 时出错: (pymysql.err.ProgrammingError) (1064, "You have an error in your SQL syntax; check the manual that corresponds to your MySQL server version for the right syntax to use near 'PRAGMA foreign_keys = OFF' at line 1")
[SQL: PRAGMA foreign_keys = OFF]
(Background on this error at: https://sqlalche.me/e/20/f405)
2025-06-18 21:19:32 | ERROR    | app.services.data_source_manager | MySQL获取批次优先级配置数据失败: (1146, "Table 'aps_system.lotpriorityconfig' doesn't exist")
2025-06-18 21:20:03 | ERROR    | app.api_v2.production.routes | 处理文件失败: (pymysql.err.ProgrammingError) (1146, "Table 'aps_system.lotpriorityconfig' doesn't exist")
[SQL: SELECT count(*) AS count_1 
FROM (SELECT lotpriorityconfig.id AS lotpriorityconfig_id, lotpriorityconfig.device AS lotpriorityconfig_device, lotpriorityconfig.stage AS lotpriorityconfig_stage, lotpriorityconfig.priority AS lotpriorityconfig_priority, lotpriorityconfig.refresh_time AS lotpriorityconfig_refresh_time, lotpriorityconfig.user AS lotpriorityconfig_user, lotpriorityconfig.created_at AS lotpriorityconfig_created_at, lotpriorityconfig.updated_at AS lotpriorityconfig_updated_at 
FROM lotpriorityconfig) AS anon_1]
(Background on this error at: https://sqlalche.me/e/20/f405)
2025-06-18 21:20:32 | ERROR    | app.api_v2.production.routes | 处理文件失败: (pymysql.err.ProgrammingError) (1146, "Table 'aps_system.lotpriorityconfig' doesn't exist")
[SQL: SELECT count(*) AS count_1 
FROM (SELECT lotpriorityconfig.id AS lotpriorityconfig_id, lotpriorityconfig.device AS lotpriorityconfig_device, lotpriorityconfig.stage AS lotpriorityconfig_stage, lotpriorityconfig.priority AS lotpriorityconfig_priority, lotpriorityconfig.refresh_time AS lotpriorityconfig_refresh_time, lotpriorityconfig.user AS lotpriorityconfig_user, lotpriorityconfig.created_at AS lotpriorityconfig_created_at, lotpriorityconfig.updated_at AS lotpriorityconfig_updated_at 
FROM lotpriorityconfig) AS anon_1]
(Background on this error at: https://sqlalche.me/e/20/f405)
2025-06-18 21:22:01 | ERROR    | app.services.data_source_manager | MySQL获取批次优先级配置数据失败: (1146, "Table 'aps_system.lotpriorityconfig' doesn't exist")
2025-06-18 21:22:07 | ERROR    | app.services.data_source_manager | MySQL获取批次优先级配置数据失败: (1146, "Table 'aps_system.lotpriorityconfig' doesn't exist")
2025-06-18 21:25:16 | ERROR    | app.utils.email_processor | IMAP登录错误: b'ERR.LOGIN.REQCODE'
2025-06-18 21:25:28 | ERROR    | app.utils.email_processor | IMAP登录错误: b'ERR.LOGIN.REQCODE'
2025-06-18 21:30:14 | ERROR    | app.services.data_source_manager | MySQL获取批次优先级配置数据失败: (1146, "Table 'aps_system.lotpriorityconfig' doesn't exist")
2025-06-18 23:34:38 | ERROR    | app | Exception on / [GET]
Traceback (most recent call last):
  File "D:\Users\pc\AppData\Local\Programs\Python\Python311\Lib\site-packages\sqlalchemy\engine\base.py", line 1965, in _exec_single_context
    self.dialect.do_execute(
  File "D:\Users\pc\AppData\Local\Programs\Python\Python311\Lib\site-packages\sqlalchemy\engine\default.py", line 921, in do_execute
    cursor.execute(statement, parameters)
  File "D:\Users\pc\AppData\Local\Programs\Python\Python311\Lib\site-packages\pymysql\cursors.py", line 153, in execute
    result = self._query(query)
             ^^^^^^^^^^^^^^^^^^
  File "D:\Users\pc\AppData\Local\Programs\Python\Python311\Lib\site-packages\pymysql\cursors.py", line 322, in _query
    conn.query(q)
  File "D:\Users\pc\AppData\Local\Programs\Python\Python311\Lib\site-packages\pymysql\connections.py", line 563, in query
    self._affected_rows = self._read_query_result(unbuffered=unbuffered)
                          ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\Users\pc\AppData\Local\Programs\Python\Python311\Lib\site-packages\pymysql\connections.py", line 825, in _read_query_result
    result.read()
  File "D:\Users\pc\AppData\Local\Programs\Python\Python311\Lib\site-packages\pymysql\connections.py", line 1199, in read
    first_packet = self.connection._read_packet()
                   ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\Users\pc\AppData\Local\Programs\Python\Python311\Lib\site-packages\pymysql\connections.py", line 775, in _read_packet
    packet.raise_for_error()
  File "D:\Users\pc\AppData\Local\Programs\Python\Python311\Lib\site-packages\pymysql\protocol.py", line 219, in raise_for_error
    err.raise_mysql_exception(self._data)
  File "D:\Users\pc\AppData\Local\Programs\Python\Python311\Lib\site-packages\pymysql\err.py", line 150, in raise_mysql_exception
    raise errorclass(errno, errval)
pymysql.err.OperationalError: (1054, "Unknown column 'users.last_login' in 'field list'")

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "D:\Users\pc\AppData\Local\Programs\Python\Python311\Lib\site-packages\flask\app.py", line 2190, in wsgi_app
    response = self.full_dispatch_request()
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\Users\pc\AppData\Local\Programs\Python\Python311\Lib\site-packages\flask\app.py", line 1486, in full_dispatch_request
    rv = self.handle_user_exception(e)
         ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\Users\pc\AppData\Local\Programs\Python\Python311\Lib\site-packages\flask_cors\extension.py", line 176, in wrapped_function
    return cors_after_request(app.make_response(f(*args, **kwargs)))
                                                ^^^^^^^^^^^^^^^^^^
  File "D:\Users\pc\AppData\Local\Programs\Python\Python311\Lib\site-packages\flask\app.py", line 1484, in full_dispatch_request
    rv = self.dispatch_request()
         ^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\Users\pc\AppData\Local\Programs\Python\Python311\Lib\site-packages\flask\app.py", line 1469, in dispatch_request
    return self.ensure_sync(self.view_functions[rule.endpoint])(**view_args)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\Users\pc\AppData\Local\Programs\Python\Python311\Lib\site-packages\flask_login\utils.py", line 284, in decorated_view
    elif not current_user.is_authenticated:
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\Users\pc\AppData\Local\Programs\Python\Python311\Lib\site-packages\werkzeug\local.py", line 311, in __get__
    obj = instance._get_current_object()
          ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\Users\pc\AppData\Local\Programs\Python\Python311\Lib\site-packages\werkzeug\local.py", line 515, in _get_current_object
    return get_name(local())
                    ^^^^^^^
  File "D:\Users\pc\AppData\Local\Programs\Python\Python311\Lib\site-packages\flask_login\utils.py", line 25, in <lambda>
    current_user = LocalProxy(lambda: _get_user())
                                      ^^^^^^^^^^^
  File "D:\Users\pc\AppData\Local\Programs\Python\Python311\Lib\site-packages\flask_login\utils.py", line 370, in _get_user
    current_app.login_manager._load_user()
  File "D:\Users\pc\AppData\Local\Programs\Python\Python311\Lib\site-packages\flask_login\login_manager.py", line 364, in _load_user
    user = self._user_callback(user_id)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\Users\pc\Documents\HBuilderProjects\APS-2025.6.18\app\__init__.py", line 71, in load_user
    return User.query.get(username)
           ^^^^^^^^^^^^^^^^^^^^^^^^
  File "<string>", line 2, in get
  File "D:\Users\pc\AppData\Local\Programs\Python\Python311\Lib\site-packages\sqlalchemy\util\deprecations.py", line 386, in warned
    return fn(*args, **kwargs)  # type: ignore[no-any-return]
           ^^^^^^^^^^^^^^^^^^^
  File "D:\Users\pc\AppData\Local\Programs\Python\Python311\Lib\site-packages\sqlalchemy\orm\query.py", line 1131, in get
    return self._get_impl(ident, loading.load_on_pk_identity)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\Users\pc\AppData\Local\Programs\Python\Python311\Lib\site-packages\sqlalchemy\orm\query.py", line 1140, in _get_impl
    return self.session._get_impl(
           ^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\Users\pc\AppData\Local\Programs\Python\Python311\Lib\site-packages\sqlalchemy\orm\session.py", line 3700, in _get_impl
    return db_load_fn(
           ^^^^^^^^^^^
  File "D:\Users\pc\AppData\Local\Programs\Python\Python311\Lib\site-packages\sqlalchemy\orm\loading.py", line 666, in load_on_pk_identity
    session.execute(
  File "D:\Users\pc\AppData\Local\Programs\Python\Python311\Lib\site-packages\sqlalchemy\orm\session.py", line 2262, in execute
    return self._execute_internal(
           ^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\Users\pc\AppData\Local\Programs\Python\Python311\Lib\site-packages\sqlalchemy\orm\session.py", line 2144, in _execute_internal
    result: Result[Any] = compile_state_cls.orm_execute_statement(
                          ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\Users\pc\AppData\Local\Programs\Python\Python311\Lib\site-packages\sqlalchemy\orm\context.py", line 293, in orm_execute_statement
    result = conn.execute(
             ^^^^^^^^^^^^^
  File "D:\Users\pc\AppData\Local\Programs\Python\Python311\Lib\site-packages\sqlalchemy\engine\base.py", line 1412, in execute
    return meth(
           ^^^^^
  File "D:\Users\pc\AppData\Local\Programs\Python\Python311\Lib\site-packages\sqlalchemy\sql\elements.py", line 516, in _execute_on_connection
    return connection._execute_clauseelement(
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\Users\pc\AppData\Local\Programs\Python\Python311\Lib\site-packages\sqlalchemy\engine\base.py", line 1635, in _execute_clauseelement
    ret = self._execute_context(
          ^^^^^^^^^^^^^^^^^^^^^^
  File "D:\Users\pc\AppData\Local\Programs\Python\Python311\Lib\site-packages\sqlalchemy\engine\base.py", line 1844, in _execute_context
    return self._exec_single_context(
           ^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\Users\pc\AppData\Local\Programs\Python\Python311\Lib\site-packages\sqlalchemy\engine\base.py", line 1984, in _exec_single_context
    self._handle_dbapi_exception(
  File "D:\Users\pc\AppData\Local\Programs\Python\Python311\Lib\site-packages\sqlalchemy\engine\base.py", line 2339, in _handle_dbapi_exception
    raise sqlalchemy_exception.with_traceback(exc_info[2]) from e
  File "D:\Users\pc\AppData\Local\Programs\Python\Python311\Lib\site-packages\sqlalchemy\engine\base.py", line 1965, in _exec_single_context
    self.dialect.do_execute(
  File "D:\Users\pc\AppData\Local\Programs\Python\Python311\Lib\site-packages\sqlalchemy\engine\default.py", line 921, in do_execute
    cursor.execute(statement, parameters)
  File "D:\Users\pc\AppData\Local\Programs\Python\Python311\Lib\site-packages\pymysql\cursors.py", line 153, in execute
    result = self._query(query)
             ^^^^^^^^^^^^^^^^^^
  File "D:\Users\pc\AppData\Local\Programs\Python\Python311\Lib\site-packages\pymysql\cursors.py", line 322, in _query
    conn.query(q)
  File "D:\Users\pc\AppData\Local\Programs\Python\Python311\Lib\site-packages\pymysql\connections.py", line 563, in query
    self._affected_rows = self._read_query_result(unbuffered=unbuffered)
                          ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\Users\pc\AppData\Local\Programs\Python\Python311\Lib\site-packages\pymysql\connections.py", line 825, in _read_query_result
    result.read()
  File "D:\Users\pc\AppData\Local\Programs\Python\Python311\Lib\site-packages\pymysql\connections.py", line 1199, in read
    first_packet = self.connection._read_packet()
                   ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\Users\pc\AppData\Local\Programs\Python\Python311\Lib\site-packages\pymysql\connections.py", line 775, in _read_packet
    packet.raise_for_error()
  File "D:\Users\pc\AppData\Local\Programs\Python\Python311\Lib\site-packages\pymysql\protocol.py", line 219, in raise_for_error
    err.raise_mysql_exception(self._data)
  File "D:\Users\pc\AppData\Local\Programs\Python\Python311\Lib\site-packages\pymysql\err.py", line 150, in raise_mysql_exception
    raise errorclass(errno, errval)
sqlalchemy.exc.OperationalError: (pymysql.err.OperationalError) (1054, "Unknown column 'users.last_login' in 'field list'")
[SQL: SELECT users.username AS users_username, users.password_hash AS users_password_hash, users.`role` AS users_role, users.created_at AS users_created_at, users.updated_at AS users_updated_at, users.last_login AS users_last_login, users.is_active AS users_is_active 
FROM users 
WHERE users.username = %(pk_1)s]
[parameters: {'pk_1': 'admin'}]
(Background on this error at: https://sqlalche.me/e/20/e3q8)
2025-06-18 23:35:37 | ERROR    | app | Exception on /production/semi-auto [GET]
Traceback (most recent call last):
  File "D:\Users\pc\AppData\Local\Programs\Python\Python311\Lib\site-packages\sqlalchemy\engine\base.py", line 1965, in _exec_single_context
    self.dialect.do_execute(
  File "D:\Users\pc\AppData\Local\Programs\Python\Python311\Lib\site-packages\sqlalchemy\engine\default.py", line 921, in do_execute
    cursor.execute(statement, parameters)
  File "D:\Users\pc\AppData\Local\Programs\Python\Python311\Lib\site-packages\pymysql\cursors.py", line 153, in execute
    result = self._query(query)
             ^^^^^^^^^^^^^^^^^^
  File "D:\Users\pc\AppData\Local\Programs\Python\Python311\Lib\site-packages\pymysql\cursors.py", line 322, in _query
    conn.query(q)
  File "D:\Users\pc\AppData\Local\Programs\Python\Python311\Lib\site-packages\pymysql\connections.py", line 563, in query
    self._affected_rows = self._read_query_result(unbuffered=unbuffered)
                          ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\Users\pc\AppData\Local\Programs\Python\Python311\Lib\site-packages\pymysql\connections.py", line 825, in _read_query_result
    result.read()
  File "D:\Users\pc\AppData\Local\Programs\Python\Python311\Lib\site-packages\pymysql\connections.py", line 1199, in read
    first_packet = self.connection._read_packet()
                   ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\Users\pc\AppData\Local\Programs\Python\Python311\Lib\site-packages\pymysql\connections.py", line 775, in _read_packet
    packet.raise_for_error()
  File "D:\Users\pc\AppData\Local\Programs\Python\Python311\Lib\site-packages\pymysql\protocol.py", line 219, in raise_for_error
    err.raise_mysql_exception(self._data)
  File "D:\Users\pc\AppData\Local\Programs\Python\Python311\Lib\site-packages\pymysql\err.py", line 150, in raise_mysql_exception
    raise errorclass(errno, errval)
pymysql.err.OperationalError: (1054, "Unknown column 'users.last_login' in 'field list'")

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "D:\Users\pc\AppData\Local\Programs\Python\Python311\Lib\site-packages\flask\app.py", line 2190, in wsgi_app
    response = self.full_dispatch_request()
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\Users\pc\AppData\Local\Programs\Python\Python311\Lib\site-packages\flask\app.py", line 1486, in full_dispatch_request
    rv = self.handle_user_exception(e)
         ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\Users\pc\AppData\Local\Programs\Python\Python311\Lib\site-packages\flask_cors\extension.py", line 176, in wrapped_function
    return cors_after_request(app.make_response(f(*args, **kwargs)))
                                                ^^^^^^^^^^^^^^^^^^
  File "D:\Users\pc\AppData\Local\Programs\Python\Python311\Lib\site-packages\flask\app.py", line 1484, in full_dispatch_request
    rv = self.dispatch_request()
         ^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\Users\pc\AppData\Local\Programs\Python\Python311\Lib\site-packages\flask\app.py", line 1469, in dispatch_request
    return self.ensure_sync(self.view_functions[rule.endpoint])(**view_args)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\Users\pc\AppData\Local\Programs\Python\Python311\Lib\site-packages\flask_login\utils.py", line 284, in decorated_view
    elif not current_user.is_authenticated:
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\Users\pc\AppData\Local\Programs\Python\Python311\Lib\site-packages\werkzeug\local.py", line 311, in __get__
    obj = instance._get_current_object()
          ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\Users\pc\AppData\Local\Programs\Python\Python311\Lib\site-packages\werkzeug\local.py", line 515, in _get_current_object
    return get_name(local())
                    ^^^^^^^
  File "D:\Users\pc\AppData\Local\Programs\Python\Python311\Lib\site-packages\flask_login\utils.py", line 25, in <lambda>
    current_user = LocalProxy(lambda: _get_user())
                                      ^^^^^^^^^^^
  File "D:\Users\pc\AppData\Local\Programs\Python\Python311\Lib\site-packages\flask_login\utils.py", line 370, in _get_user
    current_app.login_manager._load_user()
  File "D:\Users\pc\AppData\Local\Programs\Python\Python311\Lib\site-packages\flask_login\login_manager.py", line 364, in _load_user
    user = self._user_callback(user_id)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\Users\pc\Documents\HBuilderProjects\APS-2025.6.18\app\__init__.py", line 71, in load_user
    return User.query.get(username)
           ^^^^^^^^^^^^^^^^^^^^^^^^
  File "<string>", line 2, in get
  File "D:\Users\pc\AppData\Local\Programs\Python\Python311\Lib\site-packages\sqlalchemy\util\deprecations.py", line 386, in warned
    return fn(*args, **kwargs)  # type: ignore[no-any-return]
           ^^^^^^^^^^^^^^^^^^^
  File "D:\Users\pc\AppData\Local\Programs\Python\Python311\Lib\site-packages\sqlalchemy\orm\query.py", line 1131, in get
    return self._get_impl(ident, loading.load_on_pk_identity)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\Users\pc\AppData\Local\Programs\Python\Python311\Lib\site-packages\sqlalchemy\orm\query.py", line 1140, in _get_impl
    return self.session._get_impl(
           ^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\Users\pc\AppData\Local\Programs\Python\Python311\Lib\site-packages\sqlalchemy\orm\session.py", line 3700, in _get_impl
    return db_load_fn(
           ^^^^^^^^^^^
  File "D:\Users\pc\AppData\Local\Programs\Python\Python311\Lib\site-packages\sqlalchemy\orm\loading.py", line 666, in load_on_pk_identity
    session.execute(
  File "D:\Users\pc\AppData\Local\Programs\Python\Python311\Lib\site-packages\sqlalchemy\orm\session.py", line 2262, in execute
    return self._execute_internal(
           ^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\Users\pc\AppData\Local\Programs\Python\Python311\Lib\site-packages\sqlalchemy\orm\session.py", line 2144, in _execute_internal
    result: Result[Any] = compile_state_cls.orm_execute_statement(
                          ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\Users\pc\AppData\Local\Programs\Python\Python311\Lib\site-packages\sqlalchemy\orm\context.py", line 293, in orm_execute_statement
    result = conn.execute(
             ^^^^^^^^^^^^^
  File "D:\Users\pc\AppData\Local\Programs\Python\Python311\Lib\site-packages\sqlalchemy\engine\base.py", line 1412, in execute
    return meth(
           ^^^^^
  File "D:\Users\pc\AppData\Local\Programs\Python\Python311\Lib\site-packages\sqlalchemy\sql\elements.py", line 516, in _execute_on_connection
    return connection._execute_clauseelement(
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\Users\pc\AppData\Local\Programs\Python\Python311\Lib\site-packages\sqlalchemy\engine\base.py", line 1635, in _execute_clauseelement
    ret = self._execute_context(
          ^^^^^^^^^^^^^^^^^^^^^^
  File "D:\Users\pc\AppData\Local\Programs\Python\Python311\Lib\site-packages\sqlalchemy\engine\base.py", line 1844, in _execute_context
    return self._exec_single_context(
           ^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\Users\pc\AppData\Local\Programs\Python\Python311\Lib\site-packages\sqlalchemy\engine\base.py", line 1984, in _exec_single_context
    self._handle_dbapi_exception(
  File "D:\Users\pc\AppData\Local\Programs\Python\Python311\Lib\site-packages\sqlalchemy\engine\base.py", line 2339, in _handle_dbapi_exception
    raise sqlalchemy_exception.with_traceback(exc_info[2]) from e
  File "D:\Users\pc\AppData\Local\Programs\Python\Python311\Lib\site-packages\sqlalchemy\engine\base.py", line 1965, in _exec_single_context
    self.dialect.do_execute(
  File "D:\Users\pc\AppData\Local\Programs\Python\Python311\Lib\site-packages\sqlalchemy\engine\default.py", line 921, in do_execute
    cursor.execute(statement, parameters)
  File "D:\Users\pc\AppData\Local\Programs\Python\Python311\Lib\site-packages\pymysql\cursors.py", line 153, in execute
    result = self._query(query)
             ^^^^^^^^^^^^^^^^^^
  File "D:\Users\pc\AppData\Local\Programs\Python\Python311\Lib\site-packages\pymysql\cursors.py", line 322, in _query
    conn.query(q)
  File "D:\Users\pc\AppData\Local\Programs\Python\Python311\Lib\site-packages\pymysql\connections.py", line 563, in query
    self._affected_rows = self._read_query_result(unbuffered=unbuffered)
                          ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\Users\pc\AppData\Local\Programs\Python\Python311\Lib\site-packages\pymysql\connections.py", line 825, in _read_query_result
    result.read()
  File "D:\Users\pc\AppData\Local\Programs\Python\Python311\Lib\site-packages\pymysql\connections.py", line 1199, in read
    first_packet = self.connection._read_packet()
                   ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\Users\pc\AppData\Local\Programs\Python\Python311\Lib\site-packages\pymysql\connections.py", line 775, in _read_packet
    packet.raise_for_error()
  File "D:\Users\pc\AppData\Local\Programs\Python\Python311\Lib\site-packages\pymysql\protocol.py", line 219, in raise_for_error
    err.raise_mysql_exception(self._data)
  File "D:\Users\pc\AppData\Local\Programs\Python\Python311\Lib\site-packages\pymysql\err.py", line 150, in raise_mysql_exception
    raise errorclass(errno, errval)
sqlalchemy.exc.OperationalError: (pymysql.err.OperationalError) (1054, "Unknown column 'users.last_login' in 'field list'")
[SQL: SELECT users.username AS users_username, users.password_hash AS users_password_hash, users.`role` AS users_role, users.created_at AS users_created_at, users.updated_at AS users_updated_at, users.last_login AS users_last_login, users.is_active AS users_is_active 
FROM users 
WHERE users.username = %(pk_1)s]
[parameters: {'pk_1': 'admin'}]
(Background on this error at: https://sqlalche.me/e/20/e3q8)
2025-06-18 23:38:11 | ERROR    | app.api.routes | 解析订单Excel文件失败: Object of type Series is not JSON serializable
2025-06-18 23:43:38 | ERROR    | app.services.order_excel_parser | 解析文件失败 downloads\email_attachments\宜欣  生产订单模板(新封装-测试-编带)2025040901  JW7106-M001.xls: The truth value of a Series is ambiguous. Use a.empty, a.bool(), a.item(), a.any() or a.all().
2025-06-18 23:43:38 | ERROR    | app.services.order_excel_parser | 解析文件失败 downloads\email_attachments\宜欣  生产订单模板(新封装-测试-编带)2025040902  LYW6560WQEFA-SJA1_TR1.xls: The truth value of a Series is ambiguous. Use a.empty, a.bool(), a.item(), a.any() or a.all().
2025-06-18 23:43:38 | ERROR    | app.services.order_excel_parser | 解析文件失败 downloads\email_attachments\宜欣  生产订单模板(新封装-测试-编带)2025040903  JWQ7843-50SOTH-D1_TR1.xls: The truth value of a Series is ambiguous. Use a.empty, a.bool(), a.item(), a.any() or a.all().
2025-06-18 23:43:38 | ERROR    | app.services.order_excel_parser | 解析文件失败 downloads\email_attachments\宜欣  生产订单模板(新封装-测试-编带)2025040904  JWQ7843-50ESOP_TR1.xls: The truth value of a Series is ambiguous. Use a.empty, a.bool(), a.item(), a.any() or a.all().
2025-06-18 23:43:38 | ERROR    | app.services.order_excel_parser | 解析文件失败 downloads\email_attachments\宜欣  生产订单模板(新封装-测试-编带)2025040906  JW5116F.xls: The truth value of a Series is ambiguous. Use a.empty, a.bool(), a.item(), a.any() or a.all().
2025-06-18 23:43:38 | ERROR    | app.services.order_excel_parser | 解析文件失败 downloads\email_attachments\宜欣  生产订单模板(新封装-测试-编带)2025040907  JWH5102CSFQFNAT_TR0.xls: The truth value of a Series is ambiguous. Use a.empty, a.bool(), a.item(), a.any() or a.all().
2025-06-18 23:43:38 | ERROR    | app.services.order_excel_parser | 解析文件失败 downloads\email_attachments\宜欣  生产订单模板(新封装-测试-编带)2025040908  LYQ69066LGAB-SJA1_TR1.xls: The truth value of a Series is ambiguous. Use a.empty, a.bool(), a.item(), a.any() or a.all().
2025-06-18 23:43:38 | ERROR    | app.services.order_excel_parser | 解析文件失败 downloads\email_attachments\宜欣  生产订单模板(新封装-测试-编带)2025040909  JW5121ESOP-J126_TR1.xls: The truth value of a Series is ambiguous. Use a.empty, a.bool(), a.item(), a.any() or a.all().
2025-06-18 23:43:38 | ERROR    | app.services.order_excel_parser | 解析文件失败 downloads\email_attachments\宜欣  生产订单模板(新封装-测试-编带)2025040910  JW9521ESOP.xls: The truth value of a Series is ambiguous. Use a.empty, a.bool(), a.item(), a.any() or a.all().
2025-06-18 23:43:38 | ERROR    | app.services.order_excel_parser | 解析文件失败 downloads\email_attachments\宜欣  生产订单模板(新封装-测试-编带)2025040911  JWH5123CHESOP_TR1.xls: The truth value of a Series is ambiguous. Use a.empty, a.bool(), a.item(), a.any() or a.all().
2025-06-18 23:43:38 | ERROR    | app.services.order_excel_parser | 解析文件失败 downloads\email_attachments\宜欣（复测）生产订单模板(新封装-测试-编带)2025040905  JW5121-C259ESOP.xls: The truth value of a Series is ambiguous. Use a.empty, a.bool(), a.item(), a.any() or a.all().
2025-06-18 23:44:08 | ERROR    | app.services.order_excel_parser | 解析文件失败 downloads\email_attachments\宜欣  生产订单模板(新封装-测试-编带)2025040901  JW7106-M001.xls: The truth value of a Series is ambiguous. Use a.empty, a.bool(), a.item(), a.any() or a.all().
2025-06-18 23:44:08 | ERROR    | app.services.order_excel_parser | 解析文件失败 downloads\email_attachments\宜欣  生产订单模板(新封装-测试-编带)2025040902  LYW6560WQEFA-SJA1_TR1.xls: The truth value of a Series is ambiguous. Use a.empty, a.bool(), a.item(), a.any() or a.all().
2025-06-18 23:44:08 | ERROR    | app.services.order_excel_parser | 解析文件失败 downloads\email_attachments\宜欣  生产订单模板(新封装-测试-编带)2025040903  JWQ7843-50SOTH-D1_TR1.xls: The truth value of a Series is ambiguous. Use a.empty, a.bool(), a.item(), a.any() or a.all().
2025-06-18 23:44:08 | ERROR    | app.services.order_excel_parser | 解析文件失败 downloads\email_attachments\宜欣  生产订单模板(新封装-测试-编带)2025040904  JWQ7843-50ESOP_TR1.xls: The truth value of a Series is ambiguous. Use a.empty, a.bool(), a.item(), a.any() or a.all().
2025-06-18 23:44:08 | ERROR    | app.services.order_excel_parser | 解析文件失败 downloads\email_attachments\宜欣  生产订单模板(新封装-测试-编带)2025040906  JW5116F.xls: The truth value of a Series is ambiguous. Use a.empty, a.bool(), a.item(), a.any() or a.all().
2025-06-18 23:44:08 | ERROR    | app.services.order_excel_parser | 解析文件失败 downloads\email_attachments\宜欣  生产订单模板(新封装-测试-编带)2025040907  JWH5102CSFQFNAT_TR0.xls: The truth value of a Series is ambiguous. Use a.empty, a.bool(), a.item(), a.any() or a.all().
2025-06-18 23:44:08 | ERROR    | app.services.order_excel_parser | 解析文件失败 downloads\email_attachments\宜欣  生产订单模板(新封装-测试-编带)2025040908  LYQ69066LGAB-SJA1_TR1.xls: The truth value of a Series is ambiguous. Use a.empty, a.bool(), a.item(), a.any() or a.all().
2025-06-18 23:44:08 | ERROR    | app.services.order_excel_parser | 解析文件失败 downloads\email_attachments\宜欣  生产订单模板(新封装-测试-编带)2025040909  JW5121ESOP-J126_TR1.xls: The truth value of a Series is ambiguous. Use a.empty, a.bool(), a.item(), a.any() or a.all().
2025-06-18 23:44:08 | ERROR    | app.services.order_excel_parser | 解析文件失败 downloads\email_attachments\宜欣  生产订单模板(新封装-测试-编带)2025040910  JW9521ESOP.xls: The truth value of a Series is ambiguous. Use a.empty, a.bool(), a.item(), a.any() or a.all().
2025-06-18 23:44:09 | ERROR    | app.services.order_excel_parser | 解析文件失败 downloads\email_attachments\宜欣  生产订单模板(新封装-测试-编带)2025040911  JWH5123CHESOP_TR1.xls: The truth value of a Series is ambiguous. Use a.empty, a.bool(), a.item(), a.any() or a.all().
2025-06-18 23:44:09 | ERROR    | app.services.order_excel_parser | 解析文件失败 downloads\email_attachments\宜欣（复测）生产订单模板(新封装-测试-编带)2025040905  JW5121-C259ESOP.xls: The truth value of a Series is ambiguous. Use a.empty, a.bool(), a.item(), a.any() or a.all().
2025-06-18 23:58:09 | ERROR    | app.api.routes | 扫描Lot Type失败: 400 Bad Request: The browser (or proxy) sent a request that this server could not understand.
2025-06-18 23:58:12 | ERROR    | app.api.routes | 扫描Lot Type失败: 400 Bad Request: The browser (or proxy) sent a request that this server could not understand.
2025-06-18 23:58:12 | ERROR    | app.api.routes | 扫描Lot Type失败: 400 Bad Request: The browser (or proxy) sent a request that this server could not understand.
2025-06-18 23:58:13 | ERROR    | app.api.routes | 扫描Lot Type失败: 400 Bad Request: The browser (or proxy) sent a request that this server could not understand.
2025-06-18 23:58:13 | ERROR    | app.api.routes | 扫描Lot Type失败: 400 Bad Request: The browser (or proxy) sent a request that this server could not understand.
2025-06-18 23:58:14 | ERROR    | app.api.routes | 扫描Lot Type失败: 400 Bad Request: The browser (or proxy) sent a request that this server could not understand.
2025-06-18 23:58:14 | ERROR    | app.api.routes | 扫描Lot Type失败: 400 Bad Request: The browser (or proxy) sent a request that this server could not understand.
2025-06-19 00:02:17 | ERROR    | app.api.routes | 扫描Lot Type失败: 400 Bad Request: The browser (or proxy) sent a request that this server could not understand.
2025-06-19 00:05:27 | ERROR    | app.api.routes | 扫描Lot Type失败: 400 Bad Request: The browser (or proxy) sent a request that this server could not understand.
2025-06-19 09:13:04 | ERROR    | app.api.routes | 系统SQLite数据库连接失败: (1305, 'FUNCTION aps_system.sqlite_version does not exist')
2025-06-19 09:13:04 | ERROR    | app.api.routes | MySQL数据库连接失败: 415 Unsupported Media Type: Did not attempt to load JSON data because the request Content-Type was not 'application/json'.
2025-06-19 09:21:22 | ERROR    | app.api.routes | 系统SQLite数据库连接失败: (1305, 'FUNCTION aps_system.sqlite_version does not exist')
2025-06-19 09:21:22 | ERROR    | app.api.routes | MySQL数据库连接失败: 415 Unsupported Media Type: Did not attempt to load JSON data because the request Content-Type was not 'application/json'.
2025-06-19 09:21:24 | ERROR    | app.api.routes | 系统SQLite数据库连接失败: (1305, 'FUNCTION aps_system.sqlite_version does not exist')
2025-06-19 09:21:24 | ERROR    | app.api.routes | MySQL数据库连接失败: 415 Unsupported Media Type: Did not attempt to load JSON data because the request Content-Type was not 'application/json'.
2025-06-19 09:39:52 | ERROR    | app | Exception on /system/settings [GET]
Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Packages\PythonSoftwareFoundation.Python.3.11_qbz5n2kfra8p0\LocalCache\local-packages\Python311\site-packages\flask\app.py", line 2190, in wsgi_app
    response = self.full_dispatch_request()
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Packages\PythonSoftwareFoundation.Python.3.11_qbz5n2kfra8p0\LocalCache\local-packages\Python311\site-packages\flask\app.py", line 1486, in full_dispatch_request
    rv = self.handle_user_exception(e)
         ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Packages\PythonSoftwareFoundation.Python.3.11_qbz5n2kfra8p0\LocalCache\local-packages\Python311\site-packages\flask_cors\extension.py", line 176, in wrapped_function
    return cors_after_request(app.make_response(f(*args, **kwargs)))
                                                ^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Packages\PythonSoftwareFoundation.Python.3.11_qbz5n2kfra8p0\LocalCache\local-packages\Python311\site-packages\flask\app.py", line 1484, in full_dispatch_request
    rv = self.dispatch_request()
         ^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Packages\PythonSoftwareFoundation.Python.3.11_qbz5n2kfra8p0\LocalCache\local-packages\Python311\site-packages\flask\app.py", line 1469, in dispatch_request
    return self.ensure_sync(self.view_functions[rule.endpoint])(**view_args)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Packages\PythonSoftwareFoundation.Python.3.11_qbz5n2kfra8p0\LocalCache\local-packages\Python311\site-packages\flask_login\utils.py", line 290, in decorated_view
    return current_app.ensure_sync(func)(*args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\1-My Work\0-On-Going\5-Q3 report\8-APS\1-APS Rev 2.0\0-ASP development\APS-2025.6.19\app\decorators.py", line 14, in decorated_function
    return f(*args, **kwargs)
           ^^^^^^^^^^^^^^^^^^
  File "D:\1-My Work\0-On-Going\5-Q3 report\8-APS\1-APS Rev 2.0\0-ASP development\APS-2025.6.19\app\main\routes.py", line 271, in system_settings
    return render_template('system/settings.html')
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Packages\PythonSoftwareFoundation.Python.3.11_qbz5n2kfra8p0\LocalCache\local-packages\Python311\site-packages\flask\templating.py", line 150, in render_template
    template = app.jinja_env.get_or_select_template(template_name_or_list)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Packages\PythonSoftwareFoundation.Python.3.11_qbz5n2kfra8p0\LocalCache\local-packages\Python311\site-packages\jinja2\environment.py", line 1081, in get_or_select_template
    return self.get_template(template_name_or_list, parent, globals)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Packages\PythonSoftwareFoundation.Python.3.11_qbz5n2kfra8p0\LocalCache\local-packages\Python311\site-packages\jinja2\environment.py", line 1010, in get_template
    return self._load_template(name, globals)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Packages\PythonSoftwareFoundation.Python.3.11_qbz5n2kfra8p0\LocalCache\local-packages\Python311\site-packages\jinja2\environment.py", line 969, in _load_template
    template = self.loader.load(self, name, self.make_globals(globals))
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Packages\PythonSoftwareFoundation.Python.3.11_qbz5n2kfra8p0\LocalCache\local-packages\Python311\site-packages\jinja2\loaders.py", line 138, in load
    code = environment.compile(source, name, filename)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Packages\PythonSoftwareFoundation.Python.3.11_qbz5n2kfra8p0\LocalCache\local-packages\Python311\site-packages\jinja2\environment.py", line 768, in compile
    self.handle_exception(source=source_hint)
  File "C:\Users\<USER>\AppData\Local\Packages\PythonSoftwareFoundation.Python.3.11_qbz5n2kfra8p0\LocalCache\local-packages\Python311\site-packages\jinja2\environment.py", line 936, in handle_exception
    raise rewrite_traceback_stack(source=source)
  File "D:\1-My Work\0-On-Going\5-Q3 report\8-APS\1-APS Rev 2.0\0-ASP development\APS-2025.6.19\app\templates\system\settings.html", line 1394, in template
    {% endblock %}
jinja2.exceptions.TemplateSyntaxError: Encountered unknown tag 'endblock'.
2025-06-19 10:38:43 | ERROR    | app.utils.email_processor | IMAP登录错误: b'ERR.LOGIN.REQCODE'
2025-06-19 10:39:01 | ERROR    | app.utils.email_processor | IMAP登录错误: b'ERR.LOGIN.REQCODE'
2025-06-19 10:48:14 | ERROR    | app.utils.email_processor | IMAP登录错误: b'ERR.LOGIN.REQCODE'
2025-06-19 10:48:17 | ERROR    | app.utils.email_processor | IMAP登录错误: b'ERR.LOGIN.REQCODE'
2025-06-19 11:09:48 | ERROR    | app.utils.scheduler | 检查全局定时任务开关失败: module 'app' has no attribute 'app_context'
2025-06-19 11:09:48 | ERROR    | app.utils.scheduler | 重新加载邮箱配置失败: module 'app' has no attribute 'app_context'
2025-06-19 11:09:48 | ERROR    | app.utils.scheduler | 重新加载邮箱配置失败: module 'app' has no attribute 'app_context'
2025-06-19 11:11:33 | ERROR    | app.utils.email_processor | 无法选择文件夹 'JW Order', 跳过此文件夹
2025-06-19 11:29:24 | ERROR    | app.utils.email_processor | 无法选择文件夹 'JW Order', 跳过此文件夹
2025-06-19 11:40:54 | ERROR    | app.utils.email_processor | 无法选择文件夹 'JW Order', 跳过此文件夹
2025-06-19 11:52:20 | ERROR    | app.utils.email_processor | 无法选择文件夹 'JW Order', 跳过此文件夹
2025-06-19 11:53:15 | ERROR    | app.utils.email_processor | 无法选择文件夹 'JW Order', 跳过此文件夹
2025-06-19 12:04:43 | ERROR    | app.utils.email_processor | 无法选择文件夹 'JW Order', 跳过此文件夹
2025-06-19 12:17:11 | ERROR    | app.utils.email_processor | 无法选择文件夹 'JW Order', 跳过此文件夹
2025-06-19 12:25:43 | ERROR    | app.utils.email_processor | 无法选择文件夹 'JW Order', 跳过此文件夹
2025-06-19 12:37:08 | ERROR    | app.utils.email_processor | 无法选择文件夹 'JW Order', 跳过此文件夹
2025-06-19 12:48:38 | ERROR    | app.utils.email_processor | 无法选择文件夹 'JW Order', 跳过此文件夹
2025-06-19 13:00:00 | ERROR    | app.utils.email_processor | 无法选择文件夹 'JW Order', 跳过此文件夹
2025-06-19 13:11:21 | ERROR    | app.utils.email_processor | 无法选择文件夹 'JW Order', 跳过此文件夹
2025-06-19 13:22:43 | ERROR    | app.utils.email_processor | 无法选择文件夹 'JW Order', 跳过此文件夹
2025-06-19 13:28:49 | ERROR    | app.api_v2.production.order_processing | 获取健康状态失败: 'TaskManager' object has no attribute 'is_healthy'
2025-06-19 13:28:53 | ERROR    | app.api_v2.production.order_processing | 获取健康状态失败: 'TaskManager' object has no attribute 'is_healthy'
2025-06-19 13:29:35 | ERROR    | app.api_v2.production.order_processing | 获取健康状态失败: 'TaskManager' object has no attribute 'is_healthy'
2025-06-19 13:29:57 | ERROR    | app.api_v2.production.order_processing | 获取健康状态失败: 'EventBus' object has no attribute 'is_healthy'
2025-06-19 13:32:57 | ERROR    | app.api_v2.production.order_processing | 获取健康状态失败: 'EventBus' object has no attribute 'is_healthy'
2025-06-19 13:32:57 | ERROR    | app.api_v2.production.order_processing | 获取健康状态失败: 'EventBus' object has no attribute 'is_healthy'
2025-06-19 13:35:31 | ERROR    | app | Exception on /orders/semi-auto [GET]
Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Packages\PythonSoftwareFoundation.Python.3.11_qbz5n2kfra8p0\LocalCache\local-packages\Python311\site-packages\flask\app.py", line 2190, in wsgi_app
    response = self.full_dispatch_request()
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Packages\PythonSoftwareFoundation.Python.3.11_qbz5n2kfra8p0\LocalCache\local-packages\Python311\site-packages\flask\app.py", line 1486, in full_dispatch_request
    rv = self.handle_user_exception(e)
         ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Packages\PythonSoftwareFoundation.Python.3.11_qbz5n2kfra8p0\LocalCache\local-packages\Python311\site-packages\flask_cors\extension.py", line 176, in wrapped_function
    return cors_after_request(app.make_response(f(*args, **kwargs)))
                                                ^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Packages\PythonSoftwareFoundation.Python.3.11_qbz5n2kfra8p0\LocalCache\local-packages\Python311\site-packages\flask\app.py", line 1484, in full_dispatch_request
    rv = self.dispatch_request()
         ^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Packages\PythonSoftwareFoundation.Python.3.11_qbz5n2kfra8p0\LocalCache\local-packages\Python311\site-packages\flask\app.py", line 1469, in dispatch_request
    return self.ensure_sync(self.view_functions[rule.endpoint])(**view_args)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Packages\PythonSoftwareFoundation.Python.3.11_qbz5n2kfra8p0\LocalCache\local-packages\Python311\site-packages\flask_login\utils.py", line 290, in decorated_view
    return current_app.ensure_sync(func)(*args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\1-My Work\0-On-Going\5-Q3 report\8-APS\1-APS Rev 2.0\0-ASP development\APS-2025.6.19\app\decorators.py", line 24, in decorated_function
    return f(*args, **kwargs)
           ^^^^^^^^^^^^^^^^^^
  File "D:\1-My Work\0-On-Going\5-Q3 report\8-APS\1-APS Rev 2.0\0-ASP development\APS-2025.6.19\app\main\routes.py", line 185, in semi_auto_orders
    return render_template('orders/orders_semi_auto.html')
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Packages\PythonSoftwareFoundation.Python.3.11_qbz5n2kfra8p0\LocalCache\local-packages\Python311\site-packages\flask\templating.py", line 150, in render_template
    template = app.jinja_env.get_or_select_template(template_name_or_list)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Packages\PythonSoftwareFoundation.Python.3.11_qbz5n2kfra8p0\LocalCache\local-packages\Python311\site-packages\jinja2\environment.py", line 1081, in get_or_select_template
    return self.get_template(template_name_or_list, parent, globals)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Packages\PythonSoftwareFoundation.Python.3.11_qbz5n2kfra8p0\LocalCache\local-packages\Python311\site-packages\jinja2\environment.py", line 1010, in get_template
    return self._load_template(name, globals)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Packages\PythonSoftwareFoundation.Python.3.11_qbz5n2kfra8p0\LocalCache\local-packages\Python311\site-packages\jinja2\environment.py", line 969, in _load_template
    template = self.loader.load(self, name, self.make_globals(globals))
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Packages\PythonSoftwareFoundation.Python.3.11_qbz5n2kfra8p0\LocalCache\local-packages\Python311\site-packages\jinja2\loaders.py", line 138, in load
    code = environment.compile(source, name, filename)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Packages\PythonSoftwareFoundation.Python.3.11_qbz5n2kfra8p0\LocalCache\local-packages\Python311\site-packages\jinja2\environment.py", line 768, in compile
    self.handle_exception(source=source_hint)
  File "C:\Users\<USER>\AppData\Local\Packages\PythonSoftwareFoundation.Python.3.11_qbz5n2kfra8p0\LocalCache\local-packages\Python311\site-packages\jinja2\environment.py", line 936, in handle_exception
    raise rewrite_traceback_stack(source=source)
  File "D:\1-My Work\0-On-Going\5-Q3 report\8-APS\1-APS Rev 2.0\0-ASP development\APS-2025.6.19\app\templates\orders\orders_semi_auto.html", line 1784, in template
    {% block extra_js %}
jinja2.exceptions.TemplateAssertionError: block 'extra_js' defined twice
2025-06-19 13:53:23 | ERROR    | app.utils.email_processor | 无法选择文件夹 'JW Order', 跳过此文件夹
2025-06-19 13:55:37 | ERROR    | app | Exception on /orders/semi-auto [GET]
Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Packages\PythonSoftwareFoundation.Python.3.11_qbz5n2kfra8p0\LocalCache\local-packages\Python311\site-packages\flask\app.py", line 2190, in wsgi_app
    response = self.full_dispatch_request()
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Packages\PythonSoftwareFoundation.Python.3.11_qbz5n2kfra8p0\LocalCache\local-packages\Python311\site-packages\flask\app.py", line 1486, in full_dispatch_request
    rv = self.handle_user_exception(e)
         ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Packages\PythonSoftwareFoundation.Python.3.11_qbz5n2kfra8p0\LocalCache\local-packages\Python311\site-packages\flask_cors\extension.py", line 176, in wrapped_function
    return cors_after_request(app.make_response(f(*args, **kwargs)))
                                                ^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Packages\PythonSoftwareFoundation.Python.3.11_qbz5n2kfra8p0\LocalCache\local-packages\Python311\site-packages\flask\app.py", line 1484, in full_dispatch_request
    rv = self.dispatch_request()
         ^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Packages\PythonSoftwareFoundation.Python.3.11_qbz5n2kfra8p0\LocalCache\local-packages\Python311\site-packages\flask\app.py", line 1469, in dispatch_request
    return self.ensure_sync(self.view_functions[rule.endpoint])(**view_args)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Packages\PythonSoftwareFoundation.Python.3.11_qbz5n2kfra8p0\LocalCache\local-packages\Python311\site-packages\flask_login\utils.py", line 290, in decorated_view
    return current_app.ensure_sync(func)(*args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\1-My Work\0-On-Going\5-Q3 report\8-APS\1-APS Rev 2.0\0-ASP development\APS-2025.6.19\app\decorators.py", line 24, in decorated_function
    return f(*args, **kwargs)
           ^^^^^^^^^^^^^^^^^^
  File "D:\1-My Work\0-On-Going\5-Q3 report\8-APS\1-APS Rev 2.0\0-ASP development\APS-2025.6.19\app\main\routes.py", line 185, in semi_auto_orders
    return render_template('orders/orders_semi_auto.html')
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Packages\PythonSoftwareFoundation.Python.3.11_qbz5n2kfra8p0\LocalCache\local-packages\Python311\site-packages\flask\templating.py", line 150, in render_template
    template = app.jinja_env.get_or_select_template(template_name_or_list)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Packages\PythonSoftwareFoundation.Python.3.11_qbz5n2kfra8p0\LocalCache\local-packages\Python311\site-packages\jinja2\environment.py", line 1081, in get_or_select_template
    return self.get_template(template_name_or_list, parent, globals)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Packages\PythonSoftwareFoundation.Python.3.11_qbz5n2kfra8p0\LocalCache\local-packages\Python311\site-packages\jinja2\environment.py", line 1010, in get_template
    return self._load_template(name, globals)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Packages\PythonSoftwareFoundation.Python.3.11_qbz5n2kfra8p0\LocalCache\local-packages\Python311\site-packages\jinja2\environment.py", line 969, in _load_template
    template = self.loader.load(self, name, self.make_globals(globals))
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Packages\PythonSoftwareFoundation.Python.3.11_qbz5n2kfra8p0\LocalCache\local-packages\Python311\site-packages\jinja2\loaders.py", line 138, in load
    code = environment.compile(source, name, filename)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Packages\PythonSoftwareFoundation.Python.3.11_qbz5n2kfra8p0\LocalCache\local-packages\Python311\site-packages\jinja2\environment.py", line 768, in compile
    self.handle_exception(source=source_hint)
  File "C:\Users\<USER>\AppData\Local\Packages\PythonSoftwareFoundation.Python.3.11_qbz5n2kfra8p0\LocalCache\local-packages\Python311\site-packages\jinja2\environment.py", line 936, in handle_exception
    raise rewrite_traceback_stack(source=source)
  File "D:\1-My Work\0-On-Going\5-Q3 report\8-APS\1-APS Rev 2.0\0-ASP development\APS-2025.6.19\app\templates\orders\orders_semi_auto.html", line 1784, in template
    {% block extra_js %}
jinja2.exceptions.TemplateAssertionError: block 'extra_js' defined twice
2025-06-19 13:58:53 | ERROR    | app | Exception on /orders/semi-auto [GET]
Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Packages\PythonSoftwareFoundation.Python.3.11_qbz5n2kfra8p0\LocalCache\local-packages\Python311\site-packages\flask\app.py", line 2190, in wsgi_app
    response = self.full_dispatch_request()
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Packages\PythonSoftwareFoundation.Python.3.11_qbz5n2kfra8p0\LocalCache\local-packages\Python311\site-packages\flask\app.py", line 1486, in full_dispatch_request
    rv = self.handle_user_exception(e)
         ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Packages\PythonSoftwareFoundation.Python.3.11_qbz5n2kfra8p0\LocalCache\local-packages\Python311\site-packages\flask_cors\extension.py", line 176, in wrapped_function
    return cors_after_request(app.make_response(f(*args, **kwargs)))
                                                ^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Packages\PythonSoftwareFoundation.Python.3.11_qbz5n2kfra8p0\LocalCache\local-packages\Python311\site-packages\flask\app.py", line 1484, in full_dispatch_request
    rv = self.dispatch_request()
         ^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Packages\PythonSoftwareFoundation.Python.3.11_qbz5n2kfra8p0\LocalCache\local-packages\Python311\site-packages\flask\app.py", line 1469, in dispatch_request
    return self.ensure_sync(self.view_functions[rule.endpoint])(**view_args)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Packages\PythonSoftwareFoundation.Python.3.11_qbz5n2kfra8p0\LocalCache\local-packages\Python311\site-packages\flask_login\utils.py", line 290, in decorated_view
    return current_app.ensure_sync(func)(*args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\1-My Work\0-On-Going\5-Q3 report\8-APS\1-APS Rev 2.0\0-ASP development\APS-2025.6.19\app\decorators.py", line 24, in decorated_function
    return f(*args, **kwargs)
           ^^^^^^^^^^^^^^^^^^
  File "D:\1-My Work\0-On-Going\5-Q3 report\8-APS\1-APS Rev 2.0\0-ASP development\APS-2025.6.19\app\main\routes.py", line 185, in semi_auto_orders
    return render_template('orders/orders_semi_auto.html')
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Packages\PythonSoftwareFoundation.Python.3.11_qbz5n2kfra8p0\LocalCache\local-packages\Python311\site-packages\flask\templating.py", line 150, in render_template
    template = app.jinja_env.get_or_select_template(template_name_or_list)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Packages\PythonSoftwareFoundation.Python.3.11_qbz5n2kfra8p0\LocalCache\local-packages\Python311\site-packages\jinja2\environment.py", line 1081, in get_or_select_template
    return self.get_template(template_name_or_list, parent, globals)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Packages\PythonSoftwareFoundation.Python.3.11_qbz5n2kfra8p0\LocalCache\local-packages\Python311\site-packages\jinja2\environment.py", line 1010, in get_template
    return self._load_template(name, globals)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Packages\PythonSoftwareFoundation.Python.3.11_qbz5n2kfra8p0\LocalCache\local-packages\Python311\site-packages\jinja2\environment.py", line 969, in _load_template
    template = self.loader.load(self, name, self.make_globals(globals))
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Packages\PythonSoftwareFoundation.Python.3.11_qbz5n2kfra8p0\LocalCache\local-packages\Python311\site-packages\jinja2\loaders.py", line 138, in load
    code = environment.compile(source, name, filename)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Packages\PythonSoftwareFoundation.Python.3.11_qbz5n2kfra8p0\LocalCache\local-packages\Python311\site-packages\jinja2\environment.py", line 768, in compile
    self.handle_exception(source=source_hint)
  File "C:\Users\<USER>\AppData\Local\Packages\PythonSoftwareFoundation.Python.3.11_qbz5n2kfra8p0\LocalCache\local-packages\Python311\site-packages\jinja2\environment.py", line 936, in handle_exception
    raise rewrite_traceback_stack(source=source)
  File "D:\1-My Work\0-On-Going\5-Q3 report\8-APS\1-APS Rev 2.0\0-ASP development\APS-2025.6.19\app\templates\orders\orders_semi_auto.html", line 6609, in template
    <script src="{{ url_for('static', filename='js/classification_fixes.js') }}"></script>
    ^^^^^^^^^^^^^^^^^^^^^^^^^
jinja2.exceptions.TemplateSyntaxError: Unexpected end of template. Jinja was looking for the following tags: 'endblock'. The innermost block that needs to be closed is 'block'.
2025-06-19 14:09:55 | ERROR    | app.utils.email_processor | 无法选择文件夹 'JW Order', 跳过此文件夹
2025-06-19 14:17:26 | ERROR    | app.services.event_bus | Redis连接失败: Error 10061 connecting to localhost:6379. 由于目标计算机积极拒绝，无法连接。.
2025-06-19 14:17:26 | ERROR    | app.services.task_manager | 任务执行失败 4c8dfa54-a633-4f3c-9e10-3caaf1f75bb3: Working outside of application context.

This typically means that you attempted to use functionality that needed
the current application. To solve this, set up an application context
with app.app_context(). See the documentation for more information.
2025-06-19 14:48:52 | ERROR    | app.utils.email_processor | 无法选择文件夹 'JW Order', 跳过此文件夹
2025-06-19 15:00:41 | ERROR    | app.services.event_bus | Redis连接失败: Error 10061 connecting to localhost:6379. 由于目标计算机积极拒绝，无法连接。.
2025-06-19 15:00:41 | ERROR    | app.services.task_manager | 任务执行失败 0e0e0fbe-101a-40d3-b965-1e0b68e8b5a4: Working outside of application context.

This typically means that you attempted to use functionality that needed
the current application. To solve this, set up an application context
with app.app_context(). See the documentation for more information.
2025-06-19 15:13:30 | ERROR    | app.services.event_bus | Redis连接失败: Error 10061 connecting to localhost:6379. 由于目标计算机积极拒绝，无法连接。.
2025-06-19 15:13:30 | ERROR    | app.utils.email_processor | 未提供邮箱配置
2025-06-19 15:13:30 | ERROR    | app.services.task_manager | 任务执行失败 8d26fa41-e029-4ffc-a58a-2237aaa1cd4b: 'str' object has no attribute 'file_path'
2025-06-19 15:18:40 | ERROR    | app.services.order_processing_service | 解析文件失败 downloads\FT工程订单汇总表.xlsx: 'ExcelProcessor' object has no attribute 'parse_excel_file'
2025-06-19 15:18:40 | ERROR    | app.services.order_processing_service | 解析文件失败 downloads\FT量产订单汇总表.xlsx: 'ExcelProcessor' object has no attribute 'parse_excel_file'
2025-06-19 15:18:40 | ERROR    | app.services.order_processing_service | 解析文件失败 downloads\生产工单信息汇总表.xlsx: 'ExcelProcessor' object has no attribute 'parse_excel_file'
2025-06-19 15:25:31 | ERROR    | app.services.event_bus | Redis连接失败: Error 10061 connecting to localhost:6379. 由于目标计算机积极拒绝，无法连接。.
2025-06-19 15:25:31 | ERROR    | app.services.order_processing_service | 解析文件失败 downloads\FT工程订单汇总表.xlsx: 'ExcelProcessor' object has no attribute 'parse_excel_file'
2025-06-19 15:25:31 | ERROR    | app.services.order_processing_service | 解析文件失败 downloads\FT量产订单汇总表.xlsx: 'ExcelProcessor' object has no attribute 'parse_excel_file'
2025-06-19 15:37:46 | ERROR    | app.utils.email_processor | 无法选择文件夹 'JW Order', 跳过此文件夹
2025-06-19 15:49:09 | ERROR    | app.utils.email_processor | 无法选择文件夹 'JW Order', 跳过此文件夹
2025-06-19 16:00:48 | ERROR    | app.utils.email_processor | 无法选择文件夹 'JW Order', 跳过此文件夹
2025-06-19 16:12:28 | ERROR    | app.utils.email_processor | 无法选择文件夹 'JW Order', 跳过此文件夹
2025-06-19 16:30:15 | ERROR    | app.utils.email_processor | 无法选择文件夹 'JW Order', 跳过此文件夹
2025-06-19 16:40:44 | ERROR    | app.utils.email_processor | 处理文件夹 INBOX 时出错: 'int' object has no attribute 'decode'
2025-06-19 16:40:45 | ERROR    | app.utils.email_processor | 无法选择文件夹 'JW Order', 跳过此文件夹
2025-06-19 16:51:28 | ERROR    | app.utils.email_processor | 处理文件夹 INBOX 时出错: 'int' object has no attribute 'decode'
2025-06-19 16:51:29 | ERROR    | app.utils.email_processor | 无法选择文件夹 'JW Order', 跳过此文件夹
2025-06-19 17:02:03 | ERROR    | app.utils.email_processor | 处理文件夹 INBOX 时出错: 'int' object has no attribute 'decode'
2025-06-19 17:02:03 | ERROR    | app.utils.email_processor | 无法选择文件夹 'JW Order', 跳过此文件夹
2025-06-19 17:12:31 | ERROR    | app.utils.email_processor | 处理文件夹 INBOX 时出错: 'int' object has no attribute 'decode'
2025-06-19 17:12:32 | ERROR    | app.utils.email_processor | 无法选择文件夹 'JW Order', 跳过此文件夹
2025-06-19 17:22:59 | ERROR    | app.utils.email_processor | 处理文件夹 INBOX 时出错: 'int' object has no attribute 'decode'
2025-06-19 17:23:00 | ERROR    | app.utils.email_processor | 无法选择文件夹 'JW Order', 跳过此文件夹
2025-06-19 17:33:27 | ERROR    | app.utils.email_processor | 处理文件夹 INBOX 时出错: 'int' object has no attribute 'decode'
2025-06-19 17:33:27 | ERROR    | app.utils.email_processor | 无法选择文件夹 'JW Order', 跳过此文件夹
2025-06-19 17:43:56 | ERROR    | app.utils.email_processor | 处理文件夹 INBOX 时出错: 'int' object has no attribute 'decode'
2025-06-19 17:43:56 | ERROR    | app.utils.email_processor | 无法选择文件夹 'JW Order', 跳过此文件夹
2025-06-19 17:54:22 | ERROR    | app.utils.email_processor | 处理文件夹 INBOX 时出错: 'int' object has no attribute 'decode'
2025-06-19 17:54:23 | ERROR    | app.utils.email_processor | 无法选择文件夹 'JW Order', 跳过此文件夹
2025-06-19 18:32:04 | ERROR    | app.services.order_processing_service | 解析文件失败 downloads\FT工程订单汇总表.xlsx: 'ExcelProcessor' object has no attribute 'parse_excel_file'
2025-06-19 18:32:04 | ERROR    | app.services.order_processing_service | 解析文件失败 downloads\FT量产订单汇总表.xlsx: 'ExcelProcessor' object has no attribute 'parse_excel_file'
2025-06-19 18:34:13 | ERROR    | app.services.event_bus | Redis连接失败: Error 10061 connecting to localhost:6379. 由于目标计算机积极拒绝，无法连接。.
2025-06-19 18:34:13 | ERROR    | app.services.order_processing_service | 解析文件失败 downloads\FT工程订单汇总表.xlsx: 'ExcelProcessor' object has no attribute 'parse_excel_file'
2025-06-19 18:34:13 | ERROR    | app.services.order_processing_service | 解析文件失败 downloads\FT量产订单汇总表.xlsx: 'ExcelProcessor' object has no attribute 'parse_excel_file'
2025-06-19 18:50:09 | ERROR    | app.services.event_bus | Redis连接失败: Error 10061 connecting to localhost:6379. 由于目标计算机积极拒绝，无法连接。.
2025-06-19 18:50:09 | ERROR    | app.services.enhanced_excel_parser | 解析文件失败 downloads\FT工程订单汇总表.xlsx: Excel xlsx file; not supported
2025-06-19 18:50:09 | ERROR    | app.services.order_excel_parser | 解析文件失败 downloads\FT工程订单汇总表.xlsx: Excel xlsx file; not supported
2025-06-19 18:50:09 | ERROR    | app.services.order_processing_service | 解析文件失败: FT工程订单汇总表.xlsx, 错误: 解析文件失败: Excel xlsx file; not supported
2025-06-19 18:50:09 | ERROR    | app.services.enhanced_excel_parser | 解析文件失败 downloads\FT量产订单汇总表.xlsx: Excel xlsx file; not supported
2025-06-19 18:50:09 | ERROR    | app.services.order_excel_parser | 解析文件失败 downloads\FT量产订单汇总表.xlsx: Excel xlsx file; not supported
2025-06-19 18:50:09 | ERROR    | app.services.order_processing_service | 解析文件失败: FT量产订单汇总表.xlsx, 错误: 解析文件失败: Excel xlsx file; not supported
2025-06-19 18:52:39 | ERROR    | app.services.event_bus | Redis连接失败: Error 10061 connecting to localhost:6379. 由于目标计算机积极拒绝，无法连接。.
2025-06-19 18:52:39 | ERROR    | app.services.enhanced_excel_parser | 解析文件失败 downloads\FT工程订单汇总表.xlsx: Excel xlsx file; not supported
2025-06-19 18:52:39 | ERROR    | app.services.order_excel_parser | 解析文件失败 downloads\FT工程订单汇总表.xlsx: Excel xlsx file; not supported
2025-06-19 18:52:39 | ERROR    | app.services.order_processing_service | 解析文件失败: FT工程订单汇总表.xlsx, 错误: 解析文件失败: Excel xlsx file; not supported
2025-06-19 18:52:39 | ERROR    | app.services.enhanced_excel_parser | 解析文件失败 downloads\FT量产订单汇总表.xlsx: Excel xlsx file; not supported
2025-06-19 18:52:39 | ERROR    | app.services.order_excel_parser | 解析文件失败 downloads\FT量产订单汇总表.xlsx: Excel xlsx file; not supported
2025-06-19 18:52:39 | ERROR    | app.services.order_processing_service | 解析文件失败: FT量产订单汇总表.xlsx, 错误: 解析文件失败: Excel xlsx file; not supported
2025-06-19 19:01:37 | ERROR    | app.services.enhanced_excel_parser | 解析文件失败 downloads\FT工程订单汇总表.xlsx: Excel xlsx file; not supported
2025-06-19 19:01:37 | ERROR    | app.services.order_excel_parser | 解析文件失败 downloads\FT工程订单汇总表.xlsx: Excel xlsx file; not supported
2025-06-19 19:01:37 | ERROR    | app.services.order_processing_service | 解析文件失败: FT工程订单汇总表.xlsx, 错误: 解析文件失败: Excel xlsx file; not supported
2025-06-19 19:01:37 | ERROR    | app.services.enhanced_excel_parser | 解析文件失败 downloads\FT量产订单汇总表.xlsx: Excel xlsx file; not supported
2025-06-19 19:01:37 | ERROR    | app.services.order_excel_parser | 解析文件失败 downloads\FT量产订单汇总表.xlsx: Excel xlsx file; not supported
2025-06-19 19:01:37 | ERROR    | app.services.order_processing_service | 解析文件失败: FT量产订单汇总表.xlsx, 错误: 解析文件失败: Excel xlsx file; not supported
2025-06-19 19:01:39 | ERROR    | app.services.order_processing_service | 执行获取并解析任务失败: 邮箱配置 oliver 不存在
2025-06-19 19:01:39 | ERROR    | app.services.task_manager | 任务执行失败 99d16b76-7d58-4ae0-8e3c-9be57c6c5ee9: TaskManager.update_task_progress() got an unexpected keyword argument 'status'
2025-06-19 19:03:58 | ERROR    | app.services.event_bus | Redis连接失败: Error 10061 connecting to localhost:6379. 由于目标计算机积极拒绝，无法连接。.
2025-06-20 09:37:12 | ERROR    | app.utils.email_processor | 无法选择文件夹 'JW Order', 跳过此文件夹
2025-06-20 09:48:35 | ERROR    | app.utils.email_processor | 无法选择文件夹 'JW Order', 跳过此文件夹
2025-06-20 10:00:26 | ERROR    | app.utils.email_processor | 无法选择文件夹 'JW Order', 跳过此文件夹
2025-06-20 10:11:56 | ERROR    | app.utils.email_processor | 无法选择文件夹 'JW Order', 跳过此文件夹
2025-06-20 10:23:16 | ERROR    | app.utils.email_processor | 无法选择文件夹 'JW Order', 跳过此文件夹
2025-06-20 10:34:46 | ERROR    | app.utils.email_processor | 无法选择文件夹 'JW Order', 跳过此文件夹
2025-06-20 10:46:19 | ERROR    | app.utils.email_processor | 无法选择文件夹 'JW Order', 跳过此文件夹
2025-06-20 10:57:55 | ERROR    | app.utils.email_processor | 无法选择文件夹 'JW Order', 跳过此文件夹
2025-06-20 11:11:33 | ERROR    | app.utils.email_processor | 处理邮件时出错: command: FETCH => socket error: EOF
2025-06-20 11:11:34 | ERROR    | app.utils.email_processor | 处理邮件时出错: socket error: EOF occurred in violation of protocol (_ssl.c:2427)
2025-06-20 11:11:34 | ERROR    | app.utils.email_processor | 处理邮件时出错: socket error: EOF occurred in violation of protocol (_ssl.c:2427)
2025-06-20 11:11:34 | ERROR    | app.utils.email_processor | 处理邮件时出错: socket error: EOF occurred in violation of protocol (_ssl.c:2427)
2025-06-20 11:11:34 | ERROR    | app.utils.email_processor | 处理邮件时出错: socket error: EOF occurred in violation of protocol (_ssl.c:2427)
2025-06-20 11:11:34 | ERROR    | app.utils.email_processor | 处理邮件时出错: socket error: EOF occurred in violation of protocol (_ssl.c:2427)
2025-06-20 11:11:34 | ERROR    | app.utils.email_processor | 处理邮件时出错: socket error: EOF occurred in violation of protocol (_ssl.c:2427)
2025-06-20 11:11:34 | ERROR    | app.utils.email_processor | 处理邮件时出错: socket error: EOF occurred in violation of protocol (_ssl.c:2427)
2025-06-20 11:11:34 | ERROR    | app.utils.email_processor | 处理邮件时出错: socket error: EOF occurred in violation of protocol (_ssl.c:2427)
2025-06-20 11:11:35 | ERROR    | app.utils.email_processor | 处理邮件时出错: socket error: EOF occurred in violation of protocol (_ssl.c:2427)
2025-06-20 11:11:35 | ERROR    | app.utils.email_processor | 处理邮件时出错: socket error: EOF occurred in violation of protocol (_ssl.c:2427)
2025-06-20 11:11:35 | ERROR    | app.utils.email_processor | 处理邮件时出错: socket error: EOF occurred in violation of protocol (_ssl.c:2427)
2025-06-20 11:11:35 | ERROR    | app.utils.email_processor | 处理邮件时出错: socket error: EOF occurred in violation of protocol (_ssl.c:2427)
2025-06-20 11:11:35 | ERROR    | app.utils.email_processor | 处理邮件时出错: socket error: EOF occurred in violation of protocol (_ssl.c:2427)
2025-06-20 11:11:35 | ERROR    | app.utils.email_processor | 处理邮件时出错: socket error: EOF occurred in violation of protocol (_ssl.c:2427)
2025-06-20 11:11:35 | ERROR    | app.utils.email_processor | 处理邮件时出错: socket error: EOF occurred in violation of protocol (_ssl.c:2427)
2025-06-20 11:11:35 | ERROR    | app.utils.email_processor | 处理邮件时出错: socket error: EOF occurred in violation of protocol (_ssl.c:2427)
2025-06-20 11:11:35 | ERROR    | app.utils.email_processor | 处理邮件时出错: socket error: EOF occurred in violation of protocol (_ssl.c:2427)
2025-06-20 11:11:35 | ERROR    | app.utils.email_processor | 处理邮件时出错: socket error: EOF occurred in violation of protocol (_ssl.c:2427)
2025-06-20 11:11:35 | ERROR    | app.utils.email_processor | 处理邮件时出错: socket error: EOF occurred in violation of protocol (_ssl.c:2427)
2025-06-20 11:11:36 | ERROR    | app.utils.email_processor | 处理邮件时出错: socket error: EOF occurred in violation of protocol (_ssl.c:2427)
2025-06-20 11:11:36 | ERROR    | app.utils.email_processor | 处理邮件时出错: socket error: EOF occurred in violation of protocol (_ssl.c:2427)
2025-06-20 11:11:36 | ERROR    | app.utils.email_processor | 处理邮件时出错: socket error: EOF occurred in violation of protocol (_ssl.c:2427)
2025-06-20 11:11:36 | ERROR    | app.utils.email_processor | 处理邮件时出错: socket error: EOF occurred in violation of protocol (_ssl.c:2427)
2025-06-20 11:11:36 | ERROR    | app.utils.email_processor | 处理邮件时出错: socket error: EOF occurred in violation of protocol (_ssl.c:2427)
2025-06-20 11:11:36 | ERROR    | app.utils.email_processor | 处理邮件时出错: socket error: EOF occurred in violation of protocol (_ssl.c:2427)
2025-06-20 11:11:36 | ERROR    | app.utils.email_processor | 处理邮件时出错: socket error: EOF occurred in violation of protocol (_ssl.c:2427)
2025-06-20 11:11:36 | ERROR    | app.utils.email_processor | 处理邮件时出错: socket error: EOF occurred in violation of protocol (_ssl.c:2427)
2025-06-20 11:11:36 | ERROR    | app.utils.email_processor | 处理邮件时出错: socket error: EOF occurred in violation of protocol (_ssl.c:2427)
2025-06-20 11:11:36 | ERROR    | app.utils.email_processor | 处理邮件时出错: socket error: EOF occurred in violation of protocol (_ssl.c:2427)
2025-06-20 11:11:36 | ERROR    | app.utils.email_processor | 处理邮件时出错: socket error: EOF occurred in violation of protocol (_ssl.c:2427)
2025-06-20 11:11:37 | ERROR    | app.utils.email_processor | 处理邮件时出错: socket error: EOF occurred in violation of protocol (_ssl.c:2427)
2025-06-20 11:11:37 | ERROR    | app.utils.email_processor | 处理邮件时出错: socket error: EOF occurred in violation of protocol (_ssl.c:2427)
2025-06-20 11:11:37 | ERROR    | app.utils.email_processor | 处理邮件时出错: socket error: EOF occurred in violation of protocol (_ssl.c:2427)
2025-06-20 11:11:37 | ERROR    | app.utils.email_processor | 处理邮件时出错: socket error: EOF occurred in violation of protocol (_ssl.c:2427)
2025-06-20 11:11:37 | ERROR    | app.utils.email_processor | 处理邮件时出错: socket error: EOF occurred in violation of protocol (_ssl.c:2427)
2025-06-20 11:11:37 | ERROR    | app.utils.email_processor | 处理邮件时出错: socket error: EOF occurred in violation of protocol (_ssl.c:2427)
2025-06-20 11:11:37 | ERROR    | app.utils.email_processor | 处理邮件时出错: socket error: EOF occurred in violation of protocol (_ssl.c:2427)
2025-06-20 11:11:37 | ERROR    | app.utils.email_processor | 处理邮件时出错: socket error: EOF occurred in violation of protocol (_ssl.c:2427)
2025-06-20 11:11:37 | ERROR    | app.utils.email_processor | 处理邮件时出错: socket error: EOF occurred in violation of protocol (_ssl.c:2427)
2025-06-20 11:11:37 | ERROR    | app.utils.email_processor | 处理邮件时出错: socket error: EOF occurred in violation of protocol (_ssl.c:2427)
2025-06-20 11:11:37 | ERROR    | app.utils.email_processor | 处理邮件时出错: socket error: EOF occurred in violation of protocol (_ssl.c:2427)
2025-06-20 11:11:38 | ERROR    | app.utils.email_processor | 处理邮件时出错: socket error: EOF occurred in violation of protocol (_ssl.c:2427)
2025-06-20 11:11:38 | ERROR    | app.utils.email_processor | 处理邮件时出错: socket error: EOF occurred in violation of protocol (_ssl.c:2427)
2025-06-20 11:11:38 | ERROR    | app.utils.email_processor | 处理邮件时出错: socket error: EOF occurred in violation of protocol (_ssl.c:2427)
2025-06-20 11:11:38 | ERROR    | app.utils.email_processor | 处理邮件时出错: socket error: EOF occurred in violation of protocol (_ssl.c:2427)
2025-06-20 11:11:38 | ERROR    | app.utils.email_processor | 处理邮件时出错: socket error: EOF occurred in violation of protocol (_ssl.c:2427)
2025-06-20 11:11:38 | ERROR    | app.utils.email_processor | 处理邮件时出错: socket error: EOF occurred in violation of protocol (_ssl.c:2427)
2025-06-20 11:11:38 | ERROR    | app.utils.email_processor | 处理邮件时出错: socket error: EOF occurred in violation of protocol (_ssl.c:2427)
2025-06-20 11:18:29 | ERROR    | app.utils.email_processor | 处理邮件时出错: socket error: EOF occurred in violation of protocol (_ssl.c:2427)
2025-06-20 11:18:29 | ERROR    | app.utils.email_processor | 处理邮件时出错: socket error: EOF occurred in violation of protocol (_ssl.c:2427)
2025-06-20 11:18:29 | ERROR    | app.utils.email_processor | 处理邮件时出错: socket error: EOF occurred in violation of protocol (_ssl.c:2427)
2025-06-20 11:18:29 | ERROR    | app.utils.email_processor | 处理邮件时出错: socket error: EOF occurred in violation of protocol (_ssl.c:2427)
2025-06-20 11:18:29 | ERROR    | app.utils.email_processor | 处理邮件时出错: socket error: EOF occurred in violation of protocol (_ssl.c:2427)
2025-06-20 11:18:29 | ERROR    | app.utils.email_processor | 处理邮件时出错: socket error: EOF occurred in violation of protocol (_ssl.c:2427)
2025-06-20 11:18:29 | ERROR    | app.utils.email_processor | 处理邮件时出错: socket error: EOF occurred in violation of protocol (_ssl.c:2427)
2025-06-20 11:18:29 | ERROR    | app.utils.email_processor | 处理邮件时出错: socket error: EOF occurred in violation of protocol (_ssl.c:2427)
2025-06-20 11:18:29 | ERROR    | app.utils.email_processor | 处理邮件时出错: socket error: EOF occurred in violation of protocol (_ssl.c:2427)
2025-06-20 11:18:29 | ERROR    | app.utils.email_processor | 处理邮件时出错: socket error: EOF occurred in violation of protocol (_ssl.c:2427)
2025-06-20 11:18:29 | ERROR    | app.utils.email_processor | 处理邮件时出错: socket error: EOF occurred in violation of protocol (_ssl.c:2427)
2025-06-20 11:18:29 | ERROR    | app.utils.email_processor | 处理邮件时出错: socket error: EOF occurred in violation of protocol (_ssl.c:2427)
2025-06-20 11:18:29 | ERROR    | app.utils.email_processor | 处理邮件时出错: socket error: EOF occurred in violation of protocol (_ssl.c:2427)
2025-06-20 11:18:30 | ERROR    | app.utils.email_processor | 处理邮件时出错: socket error: EOF occurred in violation of protocol (_ssl.c:2427)
2025-06-20 11:18:30 | ERROR    | app.utils.email_processor | 处理邮件时出错: socket error: EOF occurred in violation of protocol (_ssl.c:2427)
2025-06-20 11:18:30 | ERROR    | app.utils.email_processor | 处理邮件时出错: socket error: EOF occurred in violation of protocol (_ssl.c:2427)
2025-06-20 11:18:30 | ERROR    | app.utils.email_processor | 处理邮件时出错: socket error: EOF occurred in violation of protocol (_ssl.c:2427)
2025-06-20 11:18:30 | ERROR    | app.utils.email_processor | 处理邮件时出错: socket error: EOF occurred in violation of protocol (_ssl.c:2427)
2025-06-20 11:18:30 | ERROR    | app.utils.email_processor | 处理邮件时出错: socket error: EOF occurred in violation of protocol (_ssl.c:2427)
2025-06-20 11:18:30 | ERROR    | app.utils.email_processor | 处理邮件时出错: socket error: EOF occurred in violation of protocol (_ssl.c:2427)
2025-06-20 11:18:30 | ERROR    | app.utils.email_processor | 处理邮件时出错: socket error: EOF occurred in violation of protocol (_ssl.c:2427)
2025-06-20 11:18:30 | ERROR    | app.utils.email_processor | 处理邮件时出错: socket error: EOF occurred in violation of protocol (_ssl.c:2427)
2025-06-20 11:18:30 | ERROR    | app.utils.email_processor | 处理邮件时出错: socket error: EOF occurred in violation of protocol (_ssl.c:2427)
2025-06-20 11:18:30 | ERROR    | app.utils.email_processor | 处理邮件时出错: socket error: EOF occurred in violation of protocol (_ssl.c:2427)
2025-06-20 11:18:30 | ERROR    | app.utils.email_processor | 处理邮件时出错: socket error: EOF occurred in violation of protocol (_ssl.c:2427)
2025-06-20 11:18:30 | ERROR    | app.utils.email_processor | 处理邮件时出错: socket error: EOF occurred in violation of protocol (_ssl.c:2427)
2025-06-20 11:18:30 | ERROR    | app.utils.email_processor | 处理邮件时出错: socket error: EOF occurred in violation of protocol (_ssl.c:2427)
2025-06-20 11:18:30 | ERROR    | app.utils.email_processor | 处理邮件时出错: socket error: EOF occurred in violation of protocol (_ssl.c:2427)
2025-06-20 11:18:30 | ERROR    | app.utils.email_processor | 处理邮件时出错: socket error: EOF occurred in violation of protocol (_ssl.c:2427)
2025-06-20 11:18:30 | ERROR    | app.utils.email_processor | 处理邮件时出错: socket error: EOF occurred in violation of protocol (_ssl.c:2427)
2025-06-20 11:18:30 | ERROR    | app.utils.email_processor | 处理邮件时出错: socket error: EOF occurred in violation of protocol (_ssl.c:2427)
2025-06-20 11:18:30 | ERROR    | app.utils.email_processor | 处理邮件时出错: socket error: EOF occurred in violation of protocol (_ssl.c:2427)
2025-06-20 11:18:30 | ERROR    | app.utils.email_processor | 处理邮件时出错: socket error: EOF occurred in violation of protocol (_ssl.c:2427)
2025-06-20 11:18:30 | ERROR    | app.utils.email_processor | 处理邮件时出错: socket error: EOF occurred in violation of protocol (_ssl.c:2427)
2025-06-20 11:18:30 | ERROR    | app.utils.email_processor | 处理邮件时出错: socket error: EOF occurred in violation of protocol (_ssl.c:2427)
2025-06-20 11:18:30 | ERROR    | app.utils.email_processor | 选择文件夹 '&g0l6P3ux-' 时出错: socket error: [SSL: BAD_LENGTH] bad length (_ssl.c:2427)
2025-06-20 11:18:30 | ERROR    | app.utils.email_processor | 选择文件夹 '&XfJT0ZAB-' 时出错: socket error: [SSL: BAD_LENGTH] bad length (_ssl.c:2427)
2025-06-20 11:18:30 | ERROR    | app.utils.email_processor | 选择文件夹 '&XfJSIJZk-' 时出错: socket error: [SSL: BAD_LENGTH] bad length (_ssl.c:2427)
2025-06-20 11:18:30 | ERROR    | app.utils.email_processor | 选择文件夹 '&V4NXPpCuTvY-' 时出错: socket error: EOF occurred in violation of protocol (_ssl.c:2427)
2025-06-20 11:18:30 | ERROR    | app.utils.email_processor | 选择文件夹 '&Xn9USpCuTvY-' 时出错: socket error: EOF occurred in violation of protocol (_ssl.c:2427)
2025-06-20 11:18:30 | ERROR    | app.utils.email_processor | 选择文件夹 '&ZypUfVQNZYdO9lk5-' 时出错: socket error: EOF occurred in violation of protocol (_ssl.c:2427)
2025-06-20 11:18:30 | ERROR    | app.utils.email_processor | 选择文件夹 'JW Order' 时出错: socket error: [SSL: BAD_LENGTH] bad length (_ssl.c:2427)
2025-06-20 11:18:30 | ERROR    | app.utils.email_processor | 断开邮箱连接失败: socket error: [SSL: BAD_LENGTH] bad length (_ssl.c:2427)
2025-06-20 11:29:52 | ERROR    | app.utils.email_processor | 无法选择文件夹 'JW Order', 跳过此文件夹
2025-06-20 11:41:14 | ERROR    | app.utils.email_processor | 无法选择文件夹 'JW Order', 跳过此文件夹
2025-06-20 11:52:33 | ERROR    | app.utils.email_processor | 无法选择文件夹 'JW Order', 跳过此文件夹
2025-06-20 12:03:56 | ERROR    | app.utils.email_processor | 无法选择文件夹 'JW Order', 跳过此文件夹
2025-06-20 13:37:56 | ERROR    | app.utils.email_processor | 无法选择文件夹 'JW Order', 跳过此文件夹
2025-06-20 13:50:52 | ERROR    | app.api.dify_proxy | 测试Dify连接失败: HTTPConnectionPool(host='************', port=80): Max retries exceeded with url: /health (Caused by ConnectTimeoutError(<urllib3.connection.HTTPConnection object at 0x00000000359D3610>, 'Connection to ************ timed out. (connect timeout=5)'))
2025-06-20 13:50:56 | ERROR    | app.api.dify_proxy | 测试Dify连接失败: HTTPConnectionPool(host='************', port=80): Max retries exceeded with url: /health (Caused by ConnectTimeoutError(<urllib3.connection.HTTPConnection object at 0x0000000036375F50>, 'Connection to ************ timed out. (connect timeout=5)'))
2025-06-20 14:01:32 | ERROR    | app.api.dify_proxy | 测试Dify连接失败: HTTPConnectionPool(host='localhost', port=3000): Max retries exceeded with url: /health (Caused by NewConnectionError('<urllib3.connection.HTTPConnection object at 0x00000000368BAD90>: Failed to establish a new connection: [WinError 10061] 由于目标计算机积极拒绝，无法连接。'))
2025-06-20 14:01:33 | ERROR    | app.api.dify_proxy | 测试Dify连接失败: HTTPConnectionPool(host='localhost', port=3000): Max retries exceeded with url: /health (Caused by NewConnectionError('<urllib3.connection.HTTPConnection object at 0x0000000035985C50>: Failed to establish a new connection: [WinError 10061] 由于目标计算机积极拒绝，无法连接。'))
2025-06-20 14:15:32 | ERROR    | app.utils.email_processor | 无法选择文件夹 'JW Order', 跳过此文件夹
2025-06-20 14:26:58 | ERROR    | app.utils.email_processor | 无法选择文件夹 'JW Order', 跳过此文件夹
2025-06-20 14:38:30 | ERROR    | app.utils.email_processor | 无法选择文件夹 'JW Order', 跳过此文件夹
2025-06-20 14:50:02 | ERROR    | app.utils.email_processor | 无法选择文件夹 'JW Order', 跳过此文件夹
2025-06-20 15:01:45 | ERROR    | app.utils.email_processor | 无法选择文件夹 'JW Order', 跳过此文件夹
2025-06-20 15:29:04 | ERROR    | app.utils.email_processor | 无法选择文件夹 'JW Order', 跳过此文件夹
2025-06-20 15:40:42 | ERROR    | app.utils.email_processor | 无法选择文件夹 'JW Order', 跳过此文件夹
2025-06-20 15:52:06 | ERROR    | app.utils.email_processor | 无法选择文件夹 'JW Order', 跳过此文件夹
2025-06-20 16:03:31 | ERROR    | app.utils.email_processor | 无法选择文件夹 'JW Order', 跳过此文件夹
2025-06-21 14:21:26 | ERROR    | app.utils.email_processor | 无法选择文件夹 'JW Order', 跳过此文件夹
2025-06-22 11:09:02 | ERROR    | app | Exception on /api/user-filter-presets [GET]
Traceback (most recent call last):
  File "D:\Users\pc\AppData\Local\Programs\Python\Python311\Lib\site-packages\flask\app.py", line 2190, in wsgi_app
    response = self.full_dispatch_request()
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\Users\pc\AppData\Local\Programs\Python\Python311\Lib\site-packages\flask\app.py", line 1486, in full_dispatch_request
    rv = self.handle_user_exception(e)
         ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\Users\pc\AppData\Local\Programs\Python\Python311\Lib\site-packages\flask_cors\extension.py", line 176, in wrapped_function
    return cors_after_request(app.make_response(f(*args, **kwargs)))
                                                ^^^^^^^^^^^^^^^^^^
  File "D:\Users\pc\AppData\Local\Programs\Python\Python311\Lib\site-packages\flask\app.py", line 1484, in full_dispatch_request
    rv = self.dispatch_request()
         ^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\Users\pc\AppData\Local\Programs\Python\Python311\Lib\site-packages\flask\app.py", line 1469, in dispatch_request
    return self.ensure_sync(self.view_functions[rule.endpoint])(**view_args)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\Users\pc\AppData\Local\Programs\Python\Python311\Lib\site-packages\flask_login\utils.py", line 290, in decorated_view
    return current_app.ensure_sync(func)(*args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\Users\pc\Documents\HBuilderProjects\APS-2025.6.20\app\api\routes.py", line 1926, in user_filter_presets
    from app.api_v2.production.missing_apis import get_user_filter_presets, save_user_filter_preset
  File "D:\Users\pc\Documents\HBuilderProjects\APS-2025.6.20\app\api_v2\__init__.py", line 16, in <module>
    from .production import production_bp
  File "D:\Users\pc\Documents\HBuilderProjects\APS-2025.6.20\app\api_v2\production\__init__.py", line 7, in <module>
    from . import order_processing
  File "D:\Users\pc\Documents\HBuilderProjects\APS-2025.6.20\app\api_v2\production\order_processing.py", line 12, in <module>
    from app.services.order_processing_service import OrderProcessingService
  File "D:\Users\pc\Documents\HBuilderProjects\APS-2025.6.20\app\services\order_processing_service.py", line 19, in <module>
    from app.services.event_bus import get_event_bus
  File "D:\Users\pc\Documents\HBuilderProjects\APS-2025.6.20\app\services\event_bus.py", line 10, in <module>
    import redis
ModuleNotFoundError: No module named 'redis'
2025-06-22 11:14:49 | ERROR    | app | Exception on /api/user-filter-presets [GET]
Traceback (most recent call last):
  File "D:\Users\pc\AppData\Local\Programs\Python\Python311\Lib\site-packages\flask\app.py", line 2190, in wsgi_app
    response = self.full_dispatch_request()
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\Users\pc\AppData\Local\Programs\Python\Python311\Lib\site-packages\flask\app.py", line 1486, in full_dispatch_request
    rv = self.handle_user_exception(e)
         ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\Users\pc\AppData\Local\Programs\Python\Python311\Lib\site-packages\flask_cors\extension.py", line 176, in wrapped_function
    return cors_after_request(app.make_response(f(*args, **kwargs)))
                                                ^^^^^^^^^^^^^^^^^^
  File "D:\Users\pc\AppData\Local\Programs\Python\Python311\Lib\site-packages\flask\app.py", line 1484, in full_dispatch_request
    rv = self.dispatch_request()
         ^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\Users\pc\AppData\Local\Programs\Python\Python311\Lib\site-packages\flask\app.py", line 1469, in dispatch_request
    return self.ensure_sync(self.view_functions[rule.endpoint])(**view_args)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\Users\pc\AppData\Local\Programs\Python\Python311\Lib\site-packages\flask_login\utils.py", line 290, in decorated_view
    return current_app.ensure_sync(func)(*args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\Users\pc\Documents\HBuilderProjects\APS-2025.6.20\app\api\routes.py", line 1926, in user_filter_presets
    from app.api_v2.production.missing_apis import get_user_filter_presets, save_user_filter_preset
  File "D:\Users\pc\Documents\HBuilderProjects\APS-2025.6.20\app\api_v2\__init__.py", line 16, in <module>
    from .production import production_bp
  File "D:\Users\pc\Documents\HBuilderProjects\APS-2025.6.20\app\api_v2\production\__init__.py", line 7, in <module>
    from . import order_processing
  File "D:\Users\pc\Documents\HBuilderProjects\APS-2025.6.20\app\api_v2\production\order_processing.py", line 12, in <module>
    from app.services.order_processing_service import OrderProcessingService
  File "D:\Users\pc\Documents\HBuilderProjects\APS-2025.6.20\app\services\order_processing_service.py", line 19, in <module>
    from app.services.event_bus import get_event_bus
  File "D:\Users\pc\Documents\HBuilderProjects\APS-2025.6.20\app\services\event_bus.py", line 10, in <module>
    import redis
ModuleNotFoundError: No module named 'redis'
2025-06-22 11:15:31 | ERROR    | app | Exception on /api/user-filter-presets [GET]
Traceback (most recent call last):
  File "D:\Users\pc\AppData\Local\Programs\Python\Python311\Lib\site-packages\flask\app.py", line 2190, in wsgi_app
    response = self.full_dispatch_request()
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\Users\pc\AppData\Local\Programs\Python\Python311\Lib\site-packages\flask\app.py", line 1486, in full_dispatch_request
    rv = self.handle_user_exception(e)
         ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\Users\pc\AppData\Local\Programs\Python\Python311\Lib\site-packages\flask_cors\extension.py", line 176, in wrapped_function
    return cors_after_request(app.make_response(f(*args, **kwargs)))
                                                ^^^^^^^^^^^^^^^^^^
  File "D:\Users\pc\AppData\Local\Programs\Python\Python311\Lib\site-packages\flask\app.py", line 1484, in full_dispatch_request
    rv = self.dispatch_request()
         ^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\Users\pc\AppData\Local\Programs\Python\Python311\Lib\site-packages\flask\app.py", line 1469, in dispatch_request
    return self.ensure_sync(self.view_functions[rule.endpoint])(**view_args)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\Users\pc\AppData\Local\Programs\Python\Python311\Lib\site-packages\flask_login\utils.py", line 290, in decorated_view
    return current_app.ensure_sync(func)(*args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\Users\pc\Documents\HBuilderProjects\APS-2025.6.20\app\api\routes.py", line 1926, in user_filter_presets
    from app.api_v2.production.missing_apis import get_user_filter_presets, save_user_filter_preset
  File "D:\Users\pc\Documents\HBuilderProjects\APS-2025.6.20\app\api_v2\__init__.py", line 16, in <module>
    from .production import production_bp
  File "D:\Users\pc\Documents\HBuilderProjects\APS-2025.6.20\app\api_v2\production\__init__.py", line 7, in <module>
    from . import order_processing
  File "D:\Users\pc\Documents\HBuilderProjects\APS-2025.6.20\app\api_v2\production\order_processing.py", line 12, in <module>
    from app.services.order_processing_service import OrderProcessingService
  File "D:\Users\pc\Documents\HBuilderProjects\APS-2025.6.20\app\services\order_processing_service.py", line 19, in <module>
    from app.services.event_bus import get_event_bus
  File "D:\Users\pc\Documents\HBuilderProjects\APS-2025.6.20\app\services\event_bus.py", line 10, in <module>
    import redis
ModuleNotFoundError: No module named 'redis'
2025-06-22 11:16:55 | ERROR    | app | Exception on /api/user-filter-presets [GET]
Traceback (most recent call last):
  File "D:\Users\pc\AppData\Local\Programs\Python\Python311\Lib\site-packages\flask\app.py", line 2190, in wsgi_app
    response = self.full_dispatch_request()
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\Users\pc\AppData\Local\Programs\Python\Python311\Lib\site-packages\flask\app.py", line 1486, in full_dispatch_request
    rv = self.handle_user_exception(e)
         ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\Users\pc\AppData\Local\Programs\Python\Python311\Lib\site-packages\flask_cors\extension.py", line 176, in wrapped_function
    return cors_after_request(app.make_response(f(*args, **kwargs)))
                                                ^^^^^^^^^^^^^^^^^^
  File "D:\Users\pc\AppData\Local\Programs\Python\Python311\Lib\site-packages\flask\app.py", line 1484, in full_dispatch_request
    rv = self.dispatch_request()
         ^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\Users\pc\AppData\Local\Programs\Python\Python311\Lib\site-packages\flask\app.py", line 1469, in dispatch_request
    return self.ensure_sync(self.view_functions[rule.endpoint])(**view_args)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\Users\pc\AppData\Local\Programs\Python\Python311\Lib\site-packages\flask_login\utils.py", line 290, in decorated_view
    return current_app.ensure_sync(func)(*args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\Users\pc\Documents\HBuilderProjects\APS-2025.6.20\app\api\routes.py", line 1926, in user_filter_presets
    from app.api_v2.production.missing_apis import get_user_filter_presets, save_user_filter_preset
  File "D:\Users\pc\Documents\HBuilderProjects\APS-2025.6.20\app\api_v2\__init__.py", line 16, in <module>
    from .production import production_bp
  File "D:\Users\pc\Documents\HBuilderProjects\APS-2025.6.20\app\api_v2\production\__init__.py", line 7, in <module>
    from . import order_processing
  File "D:\Users\pc\Documents\HBuilderProjects\APS-2025.6.20\app\api_v2\production\order_processing.py", line 12, in <module>
    from app.services.order_processing_service import OrderProcessingService
  File "D:\Users\pc\Documents\HBuilderProjects\APS-2025.6.20\app\services\order_processing_service.py", line 19, in <module>
    from app.services.event_bus import get_event_bus
  File "D:\Users\pc\Documents\HBuilderProjects\APS-2025.6.20\app\services\event_bus.py", line 10, in <module>
    import redis
ModuleNotFoundError: No module named 'redis'
2025-06-22 12:13:36 | ERROR    | app.utils.email_processor | IMAP登录错误: b'ERR.LOGIN.REQCODE'
2025-06-22 12:49:00 | ERROR    | app.services.event_bus | Redis连接失败: Error 10061 connecting to localhost:6379. 由于目标计算机积极拒绝，无法连接。.
2025-06-22 13:13:03 | ERROR    | app | Exception on /orders/auto [GET]
Traceback (most recent call last):
  File "D:\Users\pc\AppData\Local\Programs\Python\Python311\Lib\site-packages\flask\app.py", line 2190, in wsgi_app
    response = self.full_dispatch_request()
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\Users\pc\AppData\Local\Programs\Python\Python311\Lib\site-packages\flask\app.py", line 1486, in full_dispatch_request
    rv = self.handle_user_exception(e)
         ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\Users\pc\AppData\Local\Programs\Python\Python311\Lib\site-packages\flask_cors\extension.py", line 176, in wrapped_function
    return cors_after_request(app.make_response(f(*args, **kwargs)))
                                                ^^^^^^^^^^^^^^^^^^
  File "D:\Users\pc\AppData\Local\Programs\Python\Python311\Lib\site-packages\flask\app.py", line 1484, in full_dispatch_request
    rv = self.dispatch_request()
         ^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\Users\pc\AppData\Local\Programs\Python\Python311\Lib\site-packages\flask\app.py", line 1469, in dispatch_request
    return self.ensure_sync(self.view_functions[rule.endpoint])(**view_args)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\Users\pc\AppData\Local\Programs\Python\Python311\Lib\site-packages\flask_login\utils.py", line 290, in decorated_view
    return current_app.ensure_sync(func)(*args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\Users\pc\Documents\HBuilderProjects\APS-2025.6.20\app\decorators.py", line 24, in decorated_function
    return f(*args, **kwargs)
           ^^^^^^^^^^^^^^^^^^
  File "D:\Users\pc\Documents\HBuilderProjects\APS-2025.6.20\app\main\routes.py", line 191, in auto_orders
    return render_template('orders/auto.html')
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\Users\pc\AppData\Local\Programs\Python\Python311\Lib\site-packages\flask\templating.py", line 150, in render_template
    template = app.jinja_env.get_or_select_template(template_name_or_list)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\Users\pc\AppData\Local\Programs\Python\Python311\Lib\site-packages\jinja2\environment.py", line 1081, in get_or_select_template
    return self.get_template(template_name_or_list, parent, globals)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\Users\pc\AppData\Local\Programs\Python\Python311\Lib\site-packages\jinja2\environment.py", line 1010, in get_template
    return self._load_template(name, globals)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\Users\pc\AppData\Local\Programs\Python\Python311\Lib\site-packages\jinja2\environment.py", line 969, in _load_template
    template = self.loader.load(self, name, self.make_globals(globals))
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\Users\pc\AppData\Local\Programs\Python\Python311\Lib\site-packages\jinja2\loaders.py", line 126, in load
    source, filename, uptodate = self.get_source(environment, name)
                                 ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\Users\pc\AppData\Local\Programs\Python\Python311\Lib\site-packages\flask\templating.py", line 64, in get_source
    return self._get_source_fast(environment, template)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\Users\pc\AppData\Local\Programs\Python\Python311\Lib\site-packages\flask\templating.py", line 98, in _get_source_fast
    raise TemplateNotFound(template)
jinja2.exceptions.TemplateNotFound: orders/auto.html
2025-06-22 13:13:08 | ERROR    | app | Exception on /orders/auto [GET]
Traceback (most recent call last):
  File "D:\Users\pc\AppData\Local\Programs\Python\Python311\Lib\site-packages\flask\app.py", line 2190, in wsgi_app
    response = self.full_dispatch_request()
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\Users\pc\AppData\Local\Programs\Python\Python311\Lib\site-packages\flask\app.py", line 1486, in full_dispatch_request
    rv = self.handle_user_exception(e)
         ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\Users\pc\AppData\Local\Programs\Python\Python311\Lib\site-packages\flask_cors\extension.py", line 176, in wrapped_function
    return cors_after_request(app.make_response(f(*args, **kwargs)))
                                                ^^^^^^^^^^^^^^^^^^
  File "D:\Users\pc\AppData\Local\Programs\Python\Python311\Lib\site-packages\flask\app.py", line 1484, in full_dispatch_request
    rv = self.dispatch_request()
         ^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\Users\pc\AppData\Local\Programs\Python\Python311\Lib\site-packages\flask\app.py", line 1469, in dispatch_request
    return self.ensure_sync(self.view_functions[rule.endpoint])(**view_args)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\Users\pc\AppData\Local\Programs\Python\Python311\Lib\site-packages\flask_login\utils.py", line 290, in decorated_view
    return current_app.ensure_sync(func)(*args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\Users\pc\Documents\HBuilderProjects\APS-2025.6.20\app\decorators.py", line 24, in decorated_function
    return f(*args, **kwargs)
           ^^^^^^^^^^^^^^^^^^
  File "D:\Users\pc\Documents\HBuilderProjects\APS-2025.6.20\app\main\routes.py", line 191, in auto_orders
    return render_template('orders/auto.html')
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\Users\pc\AppData\Local\Programs\Python\Python311\Lib\site-packages\flask\templating.py", line 150, in render_template
    template = app.jinja_env.get_or_select_template(template_name_or_list)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\Users\pc\AppData\Local\Programs\Python\Python311\Lib\site-packages\jinja2\environment.py", line 1081, in get_or_select_template
    return self.get_template(template_name_or_list, parent, globals)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\Users\pc\AppData\Local\Programs\Python\Python311\Lib\site-packages\jinja2\environment.py", line 1010, in get_template
    return self._load_template(name, globals)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\Users\pc\AppData\Local\Programs\Python\Python311\Lib\site-packages\jinja2\environment.py", line 969, in _load_template
    template = self.loader.load(self, name, self.make_globals(globals))
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\Users\pc\AppData\Local\Programs\Python\Python311\Lib\site-packages\jinja2\loaders.py", line 126, in load
    source, filename, uptodate = self.get_source(environment, name)
                                 ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\Users\pc\AppData\Local\Programs\Python\Python311\Lib\site-packages\flask\templating.py", line 64, in get_source
    return self._get_source_fast(environment, template)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\Users\pc\AppData\Local\Programs\Python\Python311\Lib\site-packages\flask\templating.py", line 98, in _get_source_fast
    raise TemplateNotFound(template)
jinja2.exceptions.TemplateNotFound: orders/auto.html
2025-06-22 13:55:45 | ERROR    | app | Exception on /orders/auto [GET]
Traceback (most recent call last):
  File "D:\Users\pc\AppData\Local\Programs\Python\Python311\Lib\site-packages\flask\app.py", line 2190, in wsgi_app
    response = self.full_dispatch_request()
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\Users\pc\AppData\Local\Programs\Python\Python311\Lib\site-packages\flask\app.py", line 1486, in full_dispatch_request
    rv = self.handle_user_exception(e)
         ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\Users\pc\AppData\Local\Programs\Python\Python311\Lib\site-packages\flask_cors\extension.py", line 176, in wrapped_function
    return cors_after_request(app.make_response(f(*args, **kwargs)))
                                                ^^^^^^^^^^^^^^^^^^
  File "D:\Users\pc\AppData\Local\Programs\Python\Python311\Lib\site-packages\flask\app.py", line 1484, in full_dispatch_request
    rv = self.dispatch_request()
         ^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\Users\pc\AppData\Local\Programs\Python\Python311\Lib\site-packages\flask\app.py", line 1469, in dispatch_request
    return self.ensure_sync(self.view_functions[rule.endpoint])(**view_args)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\Users\pc\AppData\Local\Programs\Python\Python311\Lib\site-packages\flask_login\utils.py", line 290, in decorated_view
    return current_app.ensure_sync(func)(*args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\Users\pc\Documents\HBuilderProjects\APS-2025.6.20\app\decorators.py", line 24, in decorated_function
    return f(*args, **kwargs)
           ^^^^^^^^^^^^^^^^^^
  File "D:\Users\pc\Documents\HBuilderProjects\APS-2025.6.20\app\main\routes.py", line 191, in auto_orders
    return render_template('orders/auto.html')
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\Users\pc\AppData\Local\Programs\Python\Python311\Lib\site-packages\flask\templating.py", line 150, in render_template
    template = app.jinja_env.get_or_select_template(template_name_or_list)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\Users\pc\AppData\Local\Programs\Python\Python311\Lib\site-packages\jinja2\environment.py", line 1081, in get_or_select_template
    return self.get_template(template_name_or_list, parent, globals)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\Users\pc\AppData\Local\Programs\Python\Python311\Lib\site-packages\jinja2\environment.py", line 1010, in get_template
    return self._load_template(name, globals)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\Users\pc\AppData\Local\Programs\Python\Python311\Lib\site-packages\jinja2\environment.py", line 969, in _load_template
    template = self.loader.load(self, name, self.make_globals(globals))
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\Users\pc\AppData\Local\Programs\Python\Python311\Lib\site-packages\jinja2\loaders.py", line 126, in load
    source, filename, uptodate = self.get_source(environment, name)
                                 ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\Users\pc\AppData\Local\Programs\Python\Python311\Lib\site-packages\flask\templating.py", line 64, in get_source
    return self._get_source_fast(environment, template)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\Users\pc\AppData\Local\Programs\Python\Python311\Lib\site-packages\flask\templating.py", line 98, in _get_source_fast
    raise TemplateNotFound(template)
jinja2.exceptions.TemplateNotFound: orders/auto.html
2025-06-22 14:49:04 | ERROR    | app | Exception on /orders/auto [GET]
Traceback (most recent call last):
  File "D:\Users\pc\AppData\Local\Programs\Python\Python311\Lib\site-packages\flask\app.py", line 2190, in wsgi_app
    response = self.full_dispatch_request()
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\Users\pc\AppData\Local\Programs\Python\Python311\Lib\site-packages\flask\app.py", line 1486, in full_dispatch_request
    rv = self.handle_user_exception(e)
         ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\Users\pc\AppData\Local\Programs\Python\Python311\Lib\site-packages\flask_cors\extension.py", line 176, in wrapped_function
    return cors_after_request(app.make_response(f(*args, **kwargs)))
                                                ^^^^^^^^^^^^^^^^^^
  File "D:\Users\pc\AppData\Local\Programs\Python\Python311\Lib\site-packages\flask\app.py", line 1484, in full_dispatch_request
    rv = self.dispatch_request()
         ^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\Users\pc\AppData\Local\Programs\Python\Python311\Lib\site-packages\flask\app.py", line 1469, in dispatch_request
    return self.ensure_sync(self.view_functions[rule.endpoint])(**view_args)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\Users\pc\AppData\Local\Programs\Python\Python311\Lib\site-packages\flask_login\utils.py", line 290, in decorated_view
    return current_app.ensure_sync(func)(*args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\Users\pc\Documents\HBuilderProjects\APS-2025.6.20\app\decorators.py", line 24, in decorated_function
    return f(*args, **kwargs)
           ^^^^^^^^^^^^^^^^^^
  File "D:\Users\pc\Documents\HBuilderProjects\APS-2025.6.20\app\main\routes.py", line 191, in auto_orders
    return render_template('orders/auto.html')
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\Users\pc\AppData\Local\Programs\Python\Python311\Lib\site-packages\flask\templating.py", line 150, in render_template
    template = app.jinja_env.get_or_select_template(template_name_or_list)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\Users\pc\AppData\Local\Programs\Python\Python311\Lib\site-packages\jinja2\environment.py", line 1081, in get_or_select_template
    return self.get_template(template_name_or_list, parent, globals)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\Users\pc\AppData\Local\Programs\Python\Python311\Lib\site-packages\jinja2\environment.py", line 1010, in get_template
    return self._load_template(name, globals)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\Users\pc\AppData\Local\Programs\Python\Python311\Lib\site-packages\jinja2\environment.py", line 969, in _load_template
    template = self.loader.load(self, name, self.make_globals(globals))
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\Users\pc\AppData\Local\Programs\Python\Python311\Lib\site-packages\jinja2\loaders.py", line 126, in load
    source, filename, uptodate = self.get_source(environment, name)
                                 ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\Users\pc\AppData\Local\Programs\Python\Python311\Lib\site-packages\flask\templating.py", line 64, in get_source
    return self._get_source_fast(environment, template)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\Users\pc\AppData\Local\Programs\Python\Python311\Lib\site-packages\flask\templating.py", line 98, in _get_source_fast
    raise TemplateNotFound(template)
jinja2.exceptions.TemplateNotFound: orders/auto.html
2025-06-22 15:10:48 | ERROR    | app | Exception on /orders/auto [GET]
Traceback (most recent call last):
  File "D:\Users\pc\AppData\Local\Programs\Python\Python311\Lib\site-packages\flask\app.py", line 2190, in wsgi_app
    response = self.full_dispatch_request()
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\Users\pc\AppData\Local\Programs\Python\Python311\Lib\site-packages\flask\app.py", line 1486, in full_dispatch_request
    rv = self.handle_user_exception(e)
         ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\Users\pc\AppData\Local\Programs\Python\Python311\Lib\site-packages\flask_cors\extension.py", line 176, in wrapped_function
    return cors_after_request(app.make_response(f(*args, **kwargs)))
                                                ^^^^^^^^^^^^^^^^^^
  File "D:\Users\pc\AppData\Local\Programs\Python\Python311\Lib\site-packages\flask\app.py", line 1484, in full_dispatch_request
    rv = self.dispatch_request()
         ^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\Users\pc\AppData\Local\Programs\Python\Python311\Lib\site-packages\flask\app.py", line 1469, in dispatch_request
    return self.ensure_sync(self.view_functions[rule.endpoint])(**view_args)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\Users\pc\AppData\Local\Programs\Python\Python311\Lib\site-packages\flask_login\utils.py", line 290, in decorated_view
    return current_app.ensure_sync(func)(*args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\Users\pc\Documents\HBuilderProjects\APS-2025.6.20\app\decorators.py", line 24, in decorated_function
    return f(*args, **kwargs)
           ^^^^^^^^^^^^^^^^^^
  File "D:\Users\pc\Documents\HBuilderProjects\APS-2025.6.20\app\main\routes.py", line 191, in auto_orders
    return render_template('orders/auto.html')
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\Users\pc\AppData\Local\Programs\Python\Python311\Lib\site-packages\flask\templating.py", line 150, in render_template
    template = app.jinja_env.get_or_select_template(template_name_or_list)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\Users\pc\AppData\Local\Programs\Python\Python311\Lib\site-packages\jinja2\environment.py", line 1081, in get_or_select_template
    return self.get_template(template_name_or_list, parent, globals)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\Users\pc\AppData\Local\Programs\Python\Python311\Lib\site-packages\jinja2\environment.py", line 1010, in get_template
    return self._load_template(name, globals)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\Users\pc\AppData\Local\Programs\Python\Python311\Lib\site-packages\jinja2\environment.py", line 969, in _load_template
    template = self.loader.load(self, name, self.make_globals(globals))
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\Users\pc\AppData\Local\Programs\Python\Python311\Lib\site-packages\jinja2\loaders.py", line 126, in load
    source, filename, uptodate = self.get_source(environment, name)
                                 ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\Users\pc\AppData\Local\Programs\Python\Python311\Lib\site-packages\flask\templating.py", line 64, in get_source
    return self._get_source_fast(environment, template)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\Users\pc\AppData\Local\Programs\Python\Python311\Lib\site-packages\flask\templating.py", line 98, in _get_source_fast
    raise TemplateNotFound(template)
jinja2.exceptions.TemplateNotFound: orders/auto.html
2025-06-22 16:33:52 | ERROR    | app | Exception on /orders/auto [GET]
Traceback (most recent call last):
  File "D:\Users\pc\AppData\Local\Programs\Python\Python311\Lib\site-packages\flask\app.py", line 2190, in wsgi_app
    response = self.full_dispatch_request()
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\Users\pc\AppData\Local\Programs\Python\Python311\Lib\site-packages\flask\app.py", line 1486, in full_dispatch_request
    rv = self.handle_user_exception(e)
         ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\Users\pc\AppData\Local\Programs\Python\Python311\Lib\site-packages\flask_cors\extension.py", line 176, in wrapped_function
    return cors_after_request(app.make_response(f(*args, **kwargs)))
                                                ^^^^^^^^^^^^^^^^^^
  File "D:\Users\pc\AppData\Local\Programs\Python\Python311\Lib\site-packages\flask\app.py", line 1484, in full_dispatch_request
    rv = self.dispatch_request()
         ^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\Users\pc\AppData\Local\Programs\Python\Python311\Lib\site-packages\flask\app.py", line 1469, in dispatch_request
    return self.ensure_sync(self.view_functions[rule.endpoint])(**view_args)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\Users\pc\AppData\Local\Programs\Python\Python311\Lib\site-packages\flask_login\utils.py", line 290, in decorated_view
    return current_app.ensure_sync(func)(*args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\Users\pc\Documents\HBuilderProjects\APS-2025.6.20\app\decorators.py", line 24, in decorated_function
    return f(*args, **kwargs)
           ^^^^^^^^^^^^^^^^^^
  File "D:\Users\pc\Documents\HBuilderProjects\APS-2025.6.20\app\main\routes.py", line 191, in auto_orders
    return render_template('orders/auto.html')
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\Users\pc\AppData\Local\Programs\Python\Python311\Lib\site-packages\flask\templating.py", line 150, in render_template
    template = app.jinja_env.get_or_select_template(template_name_or_list)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\Users\pc\AppData\Local\Programs\Python\Python311\Lib\site-packages\jinja2\environment.py", line 1081, in get_or_select_template
    return self.get_template(template_name_or_list, parent, globals)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\Users\pc\AppData\Local\Programs\Python\Python311\Lib\site-packages\jinja2\environment.py", line 1010, in get_template
    return self._load_template(name, globals)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\Users\pc\AppData\Local\Programs\Python\Python311\Lib\site-packages\jinja2\environment.py", line 969, in _load_template
    template = self.loader.load(self, name, self.make_globals(globals))
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\Users\pc\AppData\Local\Programs\Python\Python311\Lib\site-packages\jinja2\loaders.py", line 126, in load
    source, filename, uptodate = self.get_source(environment, name)
                                 ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\Users\pc\AppData\Local\Programs\Python\Python311\Lib\site-packages\flask\templating.py", line 64, in get_source
    return self._get_source_fast(environment, template)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\Users\pc\AppData\Local\Programs\Python\Python311\Lib\site-packages\flask\templating.py", line 98, in _get_source_fast
    raise TemplateNotFound(template)
jinja2.exceptions.TemplateNotFound: orders/auto.html
2025-06-22 16:47:22 | ERROR    | app | Exception on /orders/auto [GET]
Traceback (most recent call last):
  File "D:\Users\pc\AppData\Local\Programs\Python\Python311\Lib\site-packages\flask\app.py", line 2190, in wsgi_app
    response = self.full_dispatch_request()
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\Users\pc\AppData\Local\Programs\Python\Python311\Lib\site-packages\flask\app.py", line 1486, in full_dispatch_request
    rv = self.handle_user_exception(e)
         ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\Users\pc\AppData\Local\Programs\Python\Python311\Lib\site-packages\flask_cors\extension.py", line 176, in wrapped_function
    return cors_after_request(app.make_response(f(*args, **kwargs)))
                                                ^^^^^^^^^^^^^^^^^^
  File "D:\Users\pc\AppData\Local\Programs\Python\Python311\Lib\site-packages\flask\app.py", line 1484, in full_dispatch_request
    rv = self.dispatch_request()
         ^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\Users\pc\AppData\Local\Programs\Python\Python311\Lib\site-packages\flask\app.py", line 1469, in dispatch_request
    return self.ensure_sync(self.view_functions[rule.endpoint])(**view_args)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\Users\pc\AppData\Local\Programs\Python\Python311\Lib\site-packages\flask_login\utils.py", line 290, in decorated_view
    return current_app.ensure_sync(func)(*args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\Users\pc\Documents\HBuilderProjects\APS-2025.6.20\app\decorators.py", line 24, in decorated_function
    return f(*args, **kwargs)
           ^^^^^^^^^^^^^^^^^^
  File "D:\Users\pc\Documents\HBuilderProjects\APS-2025.6.20\app\main\routes.py", line 191, in auto_orders
    return render_template('orders/auto.html')
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\Users\pc\AppData\Local\Programs\Python\Python311\Lib\site-packages\flask\templating.py", line 150, in render_template
    template = app.jinja_env.get_or_select_template(template_name_or_list)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\Users\pc\AppData\Local\Programs\Python\Python311\Lib\site-packages\jinja2\environment.py", line 1081, in get_or_select_template
    return self.get_template(template_name_or_list, parent, globals)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\Users\pc\AppData\Local\Programs\Python\Python311\Lib\site-packages\jinja2\environment.py", line 1010, in get_template
    return self._load_template(name, globals)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\Users\pc\AppData\Local\Programs\Python\Python311\Lib\site-packages\jinja2\environment.py", line 969, in _load_template
    template = self.loader.load(self, name, self.make_globals(globals))
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\Users\pc\AppData\Local\Programs\Python\Python311\Lib\site-packages\jinja2\loaders.py", line 126, in load
    source, filename, uptodate = self.get_source(environment, name)
                                 ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\Users\pc\AppData\Local\Programs\Python\Python311\Lib\site-packages\flask\templating.py", line 64, in get_source
    return self._get_source_fast(environment, template)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\Users\pc\AppData\Local\Programs\Python\Python311\Lib\site-packages\flask\templating.py", line 98, in _get_source_fast
    raise TemplateNotFound(template)
jinja2.exceptions.TemplateNotFound: orders/auto.html
2025-06-22 16:55:05 | ERROR    | app | Exception on /orders/preview-summary [GET]
Traceback (most recent call last):
  File "D:\Users\pc\AppData\Local\Programs\Python\Python311\Lib\site-packages\flask\app.py", line 2190, in wsgi_app
    response = self.full_dispatch_request()
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\Users\pc\AppData\Local\Programs\Python\Python311\Lib\site-packages\flask\app.py", line 1486, in full_dispatch_request
    rv = self.handle_user_exception(e)
         ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\Users\pc\AppData\Local\Programs\Python\Python311\Lib\site-packages\flask_cors\extension.py", line 176, in wrapped_function
    return cors_after_request(app.make_response(f(*args, **kwargs)))
                                                ^^^^^^^^^^^^^^^^^^
  File "D:\Users\pc\AppData\Local\Programs\Python\Python311\Lib\site-packages\flask\app.py", line 1484, in full_dispatch_request
    rv = self.dispatch_request()
         ^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\Users\pc\AppData\Local\Programs\Python\Python311\Lib\site-packages\flask\app.py", line 1469, in dispatch_request
    return self.ensure_sync(self.view_functions[rule.endpoint])(**view_args)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\Users\pc\AppData\Local\Programs\Python\Python311\Lib\site-packages\flask_login\utils.py", line 290, in decorated_view
    return current_app.ensure_sync(func)(*args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\Users\pc\Documents\HBuilderProjects\APS-2025.6.20\app\routes\orders_routes.py", line 47, in preview_summary_page
    return render_template('orders/summary_preview_detail.html',
          ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\Users\pc\AppData\Local\Programs\Python\Python311\Lib\site-packages\flask\templating.py", line 151, in render_template
    return _render(app, template, context)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\Users\pc\AppData\Local\Programs\Python\Python311\Lib\site-packages\flask\templating.py", line 132, in _render
    rv = template.render(context)
         ^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\Users\pc\AppData\Local\Programs\Python\Python311\Lib\site-packages\jinja2\environment.py", line 1301, in render
    self.environment.handle_exception()
  File "D:\Users\pc\AppData\Local\Programs\Python\Python311\Lib\site-packages\jinja2\environment.py", line 936, in handle_exception
    raise rewrite_traceback_stack(source=source)
  File "D:\Users\pc\Documents\HBuilderProjects\APS-2025.6.20\app\templates\orders\summary_preview_detail.html", line 73, in top-level template code
    <i class="fas fa-calendar me-2"></i>生成时间：{{ moment().format('YYYY年MM月DD日 HH:mm:ss') }}
    ^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\Users\pc\AppData\Local\Programs\Python\Python311\Lib\site-packages\jinja2\utils.py", line 83, in from_obj
    if hasattr(obj, "jinja_pass_arg"):
       ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
jinja2.exceptions.UndefinedError: 'moment' is undefined
2025-06-22 16:55:09 | ERROR    | app | Exception on /orders/preview-summary [GET]
Traceback (most recent call last):
  File "D:\Users\pc\AppData\Local\Programs\Python\Python311\Lib\site-packages\flask\app.py", line 2190, in wsgi_app
    response = self.full_dispatch_request()
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\Users\pc\AppData\Local\Programs\Python\Python311\Lib\site-packages\flask\app.py", line 1486, in full_dispatch_request
    rv = self.handle_user_exception(e)
         ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\Users\pc\AppData\Local\Programs\Python\Python311\Lib\site-packages\flask_cors\extension.py", line 176, in wrapped_function
    return cors_after_request(app.make_response(f(*args, **kwargs)))
                                                ^^^^^^^^^^^^^^^^^^
  File "D:\Users\pc\AppData\Local\Programs\Python\Python311\Lib\site-packages\flask\app.py", line 1484, in full_dispatch_request
    rv = self.dispatch_request()
         ^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\Users\pc\AppData\Local\Programs\Python\Python311\Lib\site-packages\flask\app.py", line 1469, in dispatch_request
    return self.ensure_sync(self.view_functions[rule.endpoint])(**view_args)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\Users\pc\AppData\Local\Programs\Python\Python311\Lib\site-packages\flask_login\utils.py", line 290, in decorated_view
    return current_app.ensure_sync(func)(*args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\Users\pc\Documents\HBuilderProjects\APS-2025.6.20\app\routes\orders_routes.py", line 47, in preview_summary_page
    return render_template('orders/summary_preview_detail.html',
          ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\Users\pc\AppData\Local\Programs\Python\Python311\Lib\site-packages\flask\templating.py", line 151, in render_template
    return _render(app, template, context)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\Users\pc\AppData\Local\Programs\Python\Python311\Lib\site-packages\flask\templating.py", line 132, in _render
    rv = template.render(context)
         ^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\Users\pc\AppData\Local\Programs\Python\Python311\Lib\site-packages\jinja2\environment.py", line 1301, in render
    self.environment.handle_exception()
  File "D:\Users\pc\AppData\Local\Programs\Python\Python311\Lib\site-packages\jinja2\environment.py", line 936, in handle_exception
    raise rewrite_traceback_stack(source=source)
  File "D:\Users\pc\Documents\HBuilderProjects\APS-2025.6.20\app\templates\orders\summary_preview_detail.html", line 73, in top-level template code
    <i class="fas fa-calendar me-2"></i>生成时间：{{ moment().format('YYYY年MM月DD日 HH:mm:ss') }}
    ^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\Users\pc\AppData\Local\Programs\Python\Python311\Lib\site-packages\jinja2\utils.py", line 83, in from_obj
    if hasattr(obj, "jinja_pass_arg"):
       ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
jinja2.exceptions.UndefinedError: 'moment' is undefined
2025-06-22 16:56:18 | ERROR    | app | Exception on /orders/auto [GET]
Traceback (most recent call last):
  File "D:\Users\pc\AppData\Local\Programs\Python\Python311\Lib\site-packages\flask\app.py", line 2190, in wsgi_app
    response = self.full_dispatch_request()
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\Users\pc\AppData\Local\Programs\Python\Python311\Lib\site-packages\flask\app.py", line 1486, in full_dispatch_request
    rv = self.handle_user_exception(e)
         ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\Users\pc\AppData\Local\Programs\Python\Python311\Lib\site-packages\flask_cors\extension.py", line 176, in wrapped_function
    return cors_after_request(app.make_response(f(*args, **kwargs)))
                                                ^^^^^^^^^^^^^^^^^^
  File "D:\Users\pc\AppData\Local\Programs\Python\Python311\Lib\site-packages\flask\app.py", line 1484, in full_dispatch_request
    rv = self.dispatch_request()
         ^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\Users\pc\AppData\Local\Programs\Python\Python311\Lib\site-packages\flask\app.py", line 1469, in dispatch_request
    return self.ensure_sync(self.view_functions[rule.endpoint])(**view_args)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\Users\pc\AppData\Local\Programs\Python\Python311\Lib\site-packages\flask_login\utils.py", line 290, in decorated_view
    return current_app.ensure_sync(func)(*args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\Users\pc\Documents\HBuilderProjects\APS-2025.6.20\app\decorators.py", line 24, in decorated_function
    return f(*args, **kwargs)
           ^^^^^^^^^^^^^^^^^^
  File "D:\Users\pc\Documents\HBuilderProjects\APS-2025.6.20\app\main\routes.py", line 191, in auto_orders
    return render_template('orders/auto.html')
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\Users\pc\AppData\Local\Programs\Python\Python311\Lib\site-packages\flask\templating.py", line 150, in render_template
    template = app.jinja_env.get_or_select_template(template_name_or_list)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\Users\pc\AppData\Local\Programs\Python\Python311\Lib\site-packages\jinja2\environment.py", line 1081, in get_or_select_template
    return self.get_template(template_name_or_list, parent, globals)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\Users\pc\AppData\Local\Programs\Python\Python311\Lib\site-packages\jinja2\environment.py", line 1010, in get_template
    return self._load_template(name, globals)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\Users\pc\AppData\Local\Programs\Python\Python311\Lib\site-packages\jinja2\environment.py", line 969, in _load_template
    template = self.loader.load(self, name, self.make_globals(globals))
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\Users\pc\AppData\Local\Programs\Python\Python311\Lib\site-packages\jinja2\loaders.py", line 126, in load
    source, filename, uptodate = self.get_source(environment, name)
                                 ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\Users\pc\AppData\Local\Programs\Python\Python311\Lib\site-packages\flask\templating.py", line 64, in get_source
    return self._get_source_fast(environment, template)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\Users\pc\AppData\Local\Programs\Python\Python311\Lib\site-packages\flask\templating.py", line 98, in _get_source_fast
    raise TemplateNotFound(template)
jinja2.exceptions.TemplateNotFound: orders/auto.html
2025-06-22 17:09:40 | ERROR    | app.utils.email_processor | IMAP登录错误: b'ERR.LOGIN.REQCODE'
2025-06-22 17:09:41 | ERROR    | app.utils.email_processor | IMAP登录错误: b'ERR.LOGIN.REQCODE'
2025-06-22 17:09:41 | ERROR    | app.utils.email_processor | IMAP登录错误: b'ERR.LOGIN.REQCODE'
2025-06-22 17:09:48 | ERROR    | app.utils.email_processor | IMAP登录错误: b'ERR.LOGIN.REQCODE'
2025-06-22 17:09:48 | ERROR    | app.utils.email_processor | IMAP登录错误: b'ERR.LOGIN.REQCODE'
2025-06-22 17:09:49 | ERROR    | app.utils.email_processor | IMAP登录错误: b'ERR.LOGIN.REQCODE'
2025-06-22 17:09:51 | ERROR    | app.utils.email_processor | IMAP登录错误: b'ERR.LOGIN.REQCODE'
2025-06-22 17:09:52 | ERROR    | app.utils.email_processor | IMAP登录错误: b'ERR.LOGIN.REQCODE'
2025-06-22 17:09:55 | ERROR    | app.utils.email_processor | IMAP登录错误: b'ERR.LOGIN.REQCODE'
2025-06-22 17:14:14 | ERROR    | app.utils.email_processor | 网易企业邮箱需要使用授权码登录，请检查是否使用了正确的授权码而非登录密码: b'ERR.LOGIN.REQCODE'
2025-06-22 17:14:20 | ERROR    | app.utils.email_processor | 网易企业邮箱需要使用授权码登录，请检查是否使用了正确的授权码而非登录密码: b'ERR.LOGIN.REQCODE'
2025-06-22 17:14:23 | ERROR    | app.utils.email_processor | 网易企业邮箱需要使用授权码登录，请检查是否使用了正确的授权码而非登录密码: b'ERR.LOGIN.REQCODE'
2025-06-22 17:14:25 | ERROR    | app.utils.email_processor | 网易企业邮箱需要使用授权码登录，请检查是否使用了正确的授权码而非登录密码: b'ERR.LOGIN.REQCODE'
2025-06-22 18:23:45 | ERROR    | app.utils.email_processor | 网易企业邮箱需要使用授权码登录，请检查是否使用了正确的授权码而非登录密码: b'ERR.LOGIN.REQCODE'
2025-06-22 18:23:45 | ERROR    | app.utils.email_processor | 网易企业邮箱需要使用授权码登录，请检查是否使用了正确的授权码而非登录密码: b'ERR.LOGIN.REQCODE'
2025-06-22 18:23:51 | ERROR    | app.utils.email_processor | 网易企业邮箱需要使用授权码登录，请检查是否使用了正确的授权码而非登录密码: b'ERR.LOGIN.REQCODE'
2025-06-22 18:28:07 | ERROR    | app.utils.email_processor | 网易企业邮箱需要使用授权码登录，请检查是否使用了正确的授权码而非登录密码: b'ERR.LOGIN.REQCODE'
2025-06-22 18:28:08 | ERROR    | app.utils.email_processor | 网易企业邮箱需要使用授权码登录，请检查是否使用了正确的授权码而非登录密码: b'ERR.LOGIN.REQCODE'
2025-06-22 18:28:10 | ERROR    | app.utils.email_processor | 网易企业邮箱需要使用授权码登录，请检查是否使用了正确的授权码而非登录密码: b'ERR.LOGIN.REQCODE'
2025-06-22 18:59:39 | ERROR    | app | Exception on /orders/auto [GET]
Traceback (most recent call last):
  File "D:\Users\pc\AppData\Local\Programs\Python\Python311\Lib\site-packages\flask\app.py", line 2190, in wsgi_app
    response = self.full_dispatch_request()
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\Users\pc\AppData\Local\Programs\Python\Python311\Lib\site-packages\flask\app.py", line 1486, in full_dispatch_request
    rv = self.handle_user_exception(e)
         ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\Users\pc\AppData\Local\Programs\Python\Python311\Lib\site-packages\flask_cors\extension.py", line 176, in wrapped_function
    return cors_after_request(app.make_response(f(*args, **kwargs)))
                                                ^^^^^^^^^^^^^^^^^^
  File "D:\Users\pc\AppData\Local\Programs\Python\Python311\Lib\site-packages\flask\app.py", line 1484, in full_dispatch_request
    rv = self.dispatch_request()
         ^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\Users\pc\AppData\Local\Programs\Python\Python311\Lib\site-packages\flask\app.py", line 1469, in dispatch_request
    return self.ensure_sync(self.view_functions[rule.endpoint])(**view_args)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\Users\pc\AppData\Local\Programs\Python\Python311\Lib\site-packages\flask_login\utils.py", line 290, in decorated_view
    return current_app.ensure_sync(func)(*args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\Users\pc\Documents\HBuilderProjects\APS-2025.6.20\app\decorators.py", line 24, in decorated_function
    return f(*args, **kwargs)
           ^^^^^^^^^^^^^^^^^^
  File "D:\Users\pc\Documents\HBuilderProjects\APS-2025.6.20\app\main\routes.py", line 191, in auto_orders
    return render_template('orders/auto.html')
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\Users\pc\AppData\Local\Programs\Python\Python311\Lib\site-packages\flask\templating.py", line 150, in render_template
    template = app.jinja_env.get_or_select_template(template_name_or_list)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\Users\pc\AppData\Local\Programs\Python\Python311\Lib\site-packages\jinja2\environment.py", line 1081, in get_or_select_template
    return self.get_template(template_name_or_list, parent, globals)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\Users\pc\AppData\Local\Programs\Python\Python311\Lib\site-packages\jinja2\environment.py", line 1010, in get_template
    return self._load_template(name, globals)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\Users\pc\AppData\Local\Programs\Python\Python311\Lib\site-packages\jinja2\environment.py", line 969, in _load_template
    template = self.loader.load(self, name, self.make_globals(globals))
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\Users\pc\AppData\Local\Programs\Python\Python311\Lib\site-packages\jinja2\loaders.py", line 126, in load
    source, filename, uptodate = self.get_source(environment, name)
                                 ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\Users\pc\AppData\Local\Programs\Python\Python311\Lib\site-packages\flask\templating.py", line 64, in get_source
    return self._get_source_fast(environment, template)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\Users\pc\AppData\Local\Programs\Python\Python311\Lib\site-packages\flask\templating.py", line 98, in _get_source_fast
    raise TemplateNotFound(template)
jinja2.exceptions.TemplateNotFound: orders/auto.html
2025-06-22 19:20:52 | ERROR    | app.utils.email_processor | IMAP登录错误: LOGIN command error: BAD [b'invalid command']
2025-06-22 19:21:03 | ERROR    | app.utils.email_processor | IMAP登录错误: LOGIN command error: BAD [b'invalid command']
2025-06-22 19:21:33 | ERROR    | app.utils.email_processor | IMAP登录错误: LOGIN command error: BAD [b'invalid command']
2025-06-22 19:22:59 | ERROR    | app.utils.email_processor | IMAP登录错误: LOGIN command error: BAD [b'invalid command']
2025-06-22 19:32:45 | ERROR    | app.utils.email_processor | IMAP登录错误: LOGIN command error: BAD [b'invalid command']
2025-06-22 19:32:50 | ERROR    | app.utils.email_processor | IMAP登录错误: LOGIN command error: BAD [b'invalid command']
2025-06-22 19:34:20 | ERROR    | app.utils.email_processor | IMAP登录错误: LOGIN command error: BAD [b'invalid command']
2025-06-22 19:55:24 | ERROR    | app.utils.email_processor | 更新邮箱配置出错: 'APSchedulerService' object has no attribute 'add_config'
2025-06-22 19:56:05 | ERROR    | app | 保存全局定时任务设置失败: 'APSchedulerService' object has no attribute 'running'
Traceback (most recent call last):
  File "D:\Users\pc\Documents\HBuilderProjects\APS-2025.6.20\app\main\routes.py", line 778, in handle_global_scheduler_config
    if scheduler and scheduler.running:
                     ^^^^^^^^^^^^^^^^^
AttributeError: 'APSchedulerService' object has no attribute 'running'
2025-06-22 19:59:34 | ERROR    | app.services.scheduler_service | 设置调度器失败: (pymysql.err.ProgrammingError) (1146, "Table 'aps.scheduler_config' doesn't exist")
[SQL: SELECT scheduler_config.id AS scheduler_config_id, scheduler_config.`key` AS scheduler_config_key, scheduler_config.value AS scheduler_config_value, scheduler_config.description AS scheduler_config_description, scheduler_config.updated_at AS scheduler_config_updated_at, scheduler_config.updated_by AS scheduler_config_updated_by 
FROM scheduler_config 
WHERE scheduler_config.`key` = %(key_1)s 
 LIMIT %(param_1)s]
[parameters: {'key_1': 'timezone', 'param_1': 1}]
(Background on this error at: https://sqlalche.me/e/20/f405)
2025-06-22 19:59:34 | ERROR    | app | ❌ APScheduler统一调度器启动失败: (pymysql.err.ProgrammingError) (1146, "Table 'aps.scheduler_config' doesn't exist")
[SQL: SELECT scheduler_config.id AS scheduler_config_id, scheduler_config.`key` AS scheduler_config_key, scheduler_config.value AS scheduler_config_value, scheduler_config.description AS scheduler_config_description, scheduler_config.updated_at AS scheduler_config_updated_at, scheduler_config.updated_by AS scheduler_config_updated_by 
FROM scheduler_config 
WHERE scheduler_config.`key` = %(key_1)s 
 LIMIT %(param_1)s]
[parameters: {'key_1': 'timezone', 'param_1': 1}]
(Background on this error at: https://sqlalche.me/e/20/f405)
2025-06-22 19:59:34 | ERROR    | app | Traceback (most recent call last):
  File "D:\Users\pc\AppData\Local\Programs\Python\Python311\Lib\site-packages\sqlalchemy\engine\base.py", line 1965, in _exec_single_context
    self.dialect.do_execute(
  File "D:\Users\pc\AppData\Local\Programs\Python\Python311\Lib\site-packages\sqlalchemy\engine\default.py", line 921, in do_execute
    cursor.execute(statement, parameters)
  File "D:\Users\pc\AppData\Local\Programs\Python\Python311\Lib\site-packages\pymysql\cursors.py", line 153, in execute
    result = self._query(query)
             ^^^^^^^^^^^^^^^^^^
  File "D:\Users\pc\AppData\Local\Programs\Python\Python311\Lib\site-packages\pymysql\cursors.py", line 322, in _query
    conn.query(q)
  File "D:\Users\pc\AppData\Local\Programs\Python\Python311\Lib\site-packages\pymysql\connections.py", line 563, in query
    self._affected_rows = self._read_query_result(unbuffered=unbuffered)
                          ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\Users\pc\AppData\Local\Programs\Python\Python311\Lib\site-packages\pymysql\connections.py", line 825, in _read_query_result
    result.read()
  File "D:\Users\pc\AppData\Local\Programs\Python\Python311\Lib\site-packages\pymysql\connections.py", line 1199, in read
    first_packet = self.connection._read_packet()
                   ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\Users\pc\AppData\Local\Programs\Python\Python311\Lib\site-packages\pymysql\connections.py", line 775, in _read_packet
    packet.raise_for_error()
  File "D:\Users\pc\AppData\Local\Programs\Python\Python311\Lib\site-packages\pymysql\protocol.py", line 219, in raise_for_error
    err.raise_mysql_exception(self._data)
  File "D:\Users\pc\AppData\Local\Programs\Python\Python311\Lib\site-packages\pymysql\err.py", line 150, in raise_mysql_exception
    raise errorclass(errno, errval)
pymysql.err.ProgrammingError: (1146, "Table 'aps.scheduler_config' doesn't exist")

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "D:\Users\pc\Documents\HBuilderProjects\APS-2025.6.20\app\__init__.py", line 116, in create_app
    scheduler_service.init_app(app)
  File "D:\Users\pc\Documents\HBuilderProjects\APS-2025.6.20\app\services\scheduler_service.py", line 55, in init_app
    self._setup_scheduler()
  File "D:\Users\pc\Documents\HBuilderProjects\APS-2025.6.20\app\services\scheduler_service.py", line 61, in _setup_scheduler
    timezone = SchedulerConfig.get_config('timezone', 'Asia/Shanghai')
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\Users\pc\Documents\HBuilderProjects\APS-2025.6.20\app\models\..\models.py", line 1353, in get_config
    config = cls.query.filter_by(key=key).first()
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\Users\pc\AppData\Local\Programs\Python\Python311\Lib\site-packages\sqlalchemy\orm\query.py", line 2743, in first
    return self.limit(1)._iter().first()  # type: ignore
           ^^^^^^^^^^^^^^^^^^^^^
  File "D:\Users\pc\AppData\Local\Programs\Python\Python311\Lib\site-packages\sqlalchemy\orm\query.py", line 2842, in _iter
    result: Union[ScalarResult[_T], Result[_T]] = self.session.execute(
                                                  ^^^^^^^^^^^^^^^^^^^^^
  File "D:\Users\pc\AppData\Local\Programs\Python\Python311\Lib\site-packages\sqlalchemy\orm\session.py", line 2262, in execute
    return self._execute_internal(
           ^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\Users\pc\AppData\Local\Programs\Python\Python311\Lib\site-packages\sqlalchemy\orm\session.py", line 2144, in _execute_internal
    result: Result[Any] = compile_state_cls.orm_execute_statement(
                          ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\Users\pc\AppData\Local\Programs\Python\Python311\Lib\site-packages\sqlalchemy\orm\context.py", line 293, in orm_execute_statement
    result = conn.execute(
             ^^^^^^^^^^^^^
  File "D:\Users\pc\AppData\Local\Programs\Python\Python311\Lib\site-packages\sqlalchemy\engine\base.py", line 1412, in execute
    return meth(
           ^^^^^
  File "D:\Users\pc\AppData\Local\Programs\Python\Python311\Lib\site-packages\sqlalchemy\sql\elements.py", line 516, in _execute_on_connection
    return connection._execute_clauseelement(
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\Users\pc\AppData\Local\Programs\Python\Python311\Lib\site-packages\sqlalchemy\engine\base.py", line 1635, in _execute_clauseelement
    ret = self._execute_context(
          ^^^^^^^^^^^^^^^^^^^^^^
  File "D:\Users\pc\AppData\Local\Programs\Python\Python311\Lib\site-packages\sqlalchemy\engine\base.py", line 1844, in _execute_context
    return self._exec_single_context(
           ^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\Users\pc\AppData\Local\Programs\Python\Python311\Lib\site-packages\sqlalchemy\engine\base.py", line 1984, in _exec_single_context
    self._handle_dbapi_exception(
  File "D:\Users\pc\AppData\Local\Programs\Python\Python311\Lib\site-packages\sqlalchemy\engine\base.py", line 2339, in _handle_dbapi_exception
    raise sqlalchemy_exception.with_traceback(exc_info[2]) from e
  File "D:\Users\pc\AppData\Local\Programs\Python\Python311\Lib\site-packages\sqlalchemy\engine\base.py", line 1965, in _exec_single_context
    self.dialect.do_execute(
  File "D:\Users\pc\AppData\Local\Programs\Python\Python311\Lib\site-packages\sqlalchemy\engine\default.py", line 921, in do_execute
    cursor.execute(statement, parameters)
  File "D:\Users\pc\AppData\Local\Programs\Python\Python311\Lib\site-packages\pymysql\cursors.py", line 153, in execute
    result = self._query(query)
             ^^^^^^^^^^^^^^^^^^
  File "D:\Users\pc\AppData\Local\Programs\Python\Python311\Lib\site-packages\pymysql\cursors.py", line 322, in _query
    conn.query(q)
  File "D:\Users\pc\AppData\Local\Programs\Python\Python311\Lib\site-packages\pymysql\connections.py", line 563, in query
    self._affected_rows = self._read_query_result(unbuffered=unbuffered)
                          ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\Users\pc\AppData\Local\Programs\Python\Python311\Lib\site-packages\pymysql\connections.py", line 825, in _read_query_result
    result.read()
  File "D:\Users\pc\AppData\Local\Programs\Python\Python311\Lib\site-packages\pymysql\connections.py", line 1199, in read
    first_packet = self.connection._read_packet()
                   ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\Users\pc\AppData\Local\Programs\Python\Python311\Lib\site-packages\pymysql\connections.py", line 775, in _read_packet
    packet.raise_for_error()
  File "D:\Users\pc\AppData\Local\Programs\Python\Python311\Lib\site-packages\pymysql\protocol.py", line 219, in raise_for_error
    err.raise_mysql_exception(self._data)
  File "D:\Users\pc\AppData\Local\Programs\Python\Python311\Lib\site-packages\pymysql\err.py", line 150, in raise_mysql_exception
    raise errorclass(errno, errval)
sqlalchemy.exc.ProgrammingError: (pymysql.err.ProgrammingError) (1146, "Table 'aps.scheduler_config' doesn't exist")
[SQL: SELECT scheduler_config.id AS scheduler_config_id, scheduler_config.`key` AS scheduler_config_key, scheduler_config.value AS scheduler_config_value, scheduler_config.description AS scheduler_config_description, scheduler_config.updated_at AS scheduler_config_updated_at, scheduler_config.updated_by AS scheduler_config_updated_by 
FROM scheduler_config 
WHERE scheduler_config.`key` = %(key_1)s 
 LIMIT %(param_1)s]
[parameters: {'key_1': 'timezone', 'param_1': 1}]
(Background on this error at: https://sqlalche.me/e/20/f405)

2025-06-22 20:27:47 | ERROR    | app.utils.email_processor | 预览邮件附件时出错: 415 Unsupported Media Type: Did not attempt to load JSON data because the request Content-Type was not 'application/json'.
2025-06-22 20:37:24 | ERROR    | app | Exception on /orders/auto [GET]
Traceback (most recent call last):
  File "D:\Users\pc\AppData\Local\Programs\Python\Python311\Lib\site-packages\flask\app.py", line 2190, in wsgi_app
    response = self.full_dispatch_request()
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\Users\pc\AppData\Local\Programs\Python\Python311\Lib\site-packages\flask\app.py", line 1486, in full_dispatch_request
    rv = self.handle_user_exception(e)
         ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\Users\pc\AppData\Local\Programs\Python\Python311\Lib\site-packages\flask_cors\extension.py", line 176, in wrapped_function
    return cors_after_request(app.make_response(f(*args, **kwargs)))
                                                ^^^^^^^^^^^^^^^^^^
  File "D:\Users\pc\AppData\Local\Programs\Python\Python311\Lib\site-packages\flask\app.py", line 1484, in full_dispatch_request
    rv = self.dispatch_request()
         ^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\Users\pc\AppData\Local\Programs\Python\Python311\Lib\site-packages\flask\app.py", line 1469, in dispatch_request
    return self.ensure_sync(self.view_functions[rule.endpoint])(**view_args)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\Users\pc\AppData\Local\Programs\Python\Python311\Lib\site-packages\flask_login\utils.py", line 290, in decorated_view
    return current_app.ensure_sync(func)(*args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\Users\pc\Documents\HBuilderProjects\APS-2025.6.20\app\decorators.py", line 24, in decorated_function
    return f(*args, **kwargs)
           ^^^^^^^^^^^^^^^^^^
  File "D:\Users\pc\Documents\HBuilderProjects\APS-2025.6.20\app\main\routes.py", line 191, in auto_orders
    return render_template('orders/auto.html')
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\Users\pc\AppData\Local\Programs\Python\Python311\Lib\site-packages\flask\templating.py", line 150, in render_template
    template = app.jinja_env.get_or_select_template(template_name_or_list)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\Users\pc\AppData\Local\Programs\Python\Python311\Lib\site-packages\jinja2\environment.py", line 1081, in get_or_select_template
    return self.get_template(template_name_or_list, parent, globals)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\Users\pc\AppData\Local\Programs\Python\Python311\Lib\site-packages\jinja2\environment.py", line 1010, in get_template
    return self._load_template(name, globals)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\Users\pc\AppData\Local\Programs\Python\Python311\Lib\site-packages\jinja2\environment.py", line 969, in _load_template
    template = self.loader.load(self, name, self.make_globals(globals))
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\Users\pc\AppData\Local\Programs\Python\Python311\Lib\site-packages\jinja2\loaders.py", line 126, in load
    source, filename, uptodate = self.get_source(environment, name)
                                 ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\Users\pc\AppData\Local\Programs\Python\Python311\Lib\site-packages\flask\templating.py", line 64, in get_source
    return self._get_source_fast(environment, template)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\Users\pc\AppData\Local\Programs\Python\Python311\Lib\site-packages\flask\templating.py", line 98, in _get_source_fast
    raise TemplateNotFound(template)
jinja2.exceptions.TemplateNotFound: orders/auto.html
2025-06-22 21:02:42 | ERROR    | app | Exception on /orders/auto [GET]
Traceback (most recent call last):
  File "D:\Users\pc\AppData\Local\Programs\Python\Python311\Lib\site-packages\flask\app.py", line 2190, in wsgi_app
    response = self.full_dispatch_request()
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\Users\pc\AppData\Local\Programs\Python\Python311\Lib\site-packages\flask\app.py", line 1486, in full_dispatch_request
    rv = self.handle_user_exception(e)
         ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\Users\pc\AppData\Local\Programs\Python\Python311\Lib\site-packages\flask_cors\extension.py", line 176, in wrapped_function
    return cors_after_request(app.make_response(f(*args, **kwargs)))
                                                ^^^^^^^^^^^^^^^^^^
  File "D:\Users\pc\AppData\Local\Programs\Python\Python311\Lib\site-packages\flask\app.py", line 1484, in full_dispatch_request
    rv = self.dispatch_request()
         ^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\Users\pc\AppData\Local\Programs\Python\Python311\Lib\site-packages\flask\app.py", line 1469, in dispatch_request
    return self.ensure_sync(self.view_functions[rule.endpoint])(**view_args)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\Users\pc\AppData\Local\Programs\Python\Python311\Lib\site-packages\flask_login\utils.py", line 290, in decorated_view
    return current_app.ensure_sync(func)(*args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\Users\pc\Documents\HBuilderProjects\APS-2025.6.20\app\decorators.py", line 24, in decorated_function
    return f(*args, **kwargs)
           ^^^^^^^^^^^^^^^^^^
  File "D:\Users\pc\Documents\HBuilderProjects\APS-2025.6.20\app\main\routes.py", line 191, in auto_orders
    return render_template('orders/auto.html')
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\Users\pc\AppData\Local\Programs\Python\Python311\Lib\site-packages\flask\templating.py", line 150, in render_template
    template = app.jinja_env.get_or_select_template(template_name_or_list)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\Users\pc\AppData\Local\Programs\Python\Python311\Lib\site-packages\jinja2\environment.py", line 1081, in get_or_select_template
    return self.get_template(template_name_or_list, parent, globals)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\Users\pc\AppData\Local\Programs\Python\Python311\Lib\site-packages\jinja2\environment.py", line 1010, in get_template
    return self._load_template(name, globals)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\Users\pc\AppData\Local\Programs\Python\Python311\Lib\site-packages\jinja2\environment.py", line 969, in _load_template
    template = self.loader.load(self, name, self.make_globals(globals))
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\Users\pc\AppData\Local\Programs\Python\Python311\Lib\site-packages\jinja2\loaders.py", line 126, in load
    source, filename, uptodate = self.get_source(environment, name)
                                 ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\Users\pc\AppData\Local\Programs\Python\Python311\Lib\site-packages\flask\templating.py", line 64, in get_source
    return self._get_source_fast(environment, template)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\Users\pc\AppData\Local\Programs\Python\Python311\Lib\site-packages\flask\templating.py", line 98, in _get_source_fast
    raise TemplateNotFound(template)
jinja2.exceptions.TemplateNotFound: orders/auto.html
2025-06-22 21:04:24 | ERROR    | app | Exception on /orders/auto [GET]
Traceback (most recent call last):
  File "D:\Users\pc\AppData\Local\Programs\Python\Python311\Lib\site-packages\flask\app.py", line 2190, in wsgi_app
    response = self.full_dispatch_request()
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\Users\pc\AppData\Local\Programs\Python\Python311\Lib\site-packages\flask\app.py", line 1486, in full_dispatch_request
    rv = self.handle_user_exception(e)
         ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\Users\pc\AppData\Local\Programs\Python\Python311\Lib\site-packages\flask_cors\extension.py", line 176, in wrapped_function
    return cors_after_request(app.make_response(f(*args, **kwargs)))
                                                ^^^^^^^^^^^^^^^^^^
  File "D:\Users\pc\AppData\Local\Programs\Python\Python311\Lib\site-packages\flask\app.py", line 1484, in full_dispatch_request
    rv = self.dispatch_request()
         ^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\Users\pc\AppData\Local\Programs\Python\Python311\Lib\site-packages\flask\app.py", line 1469, in dispatch_request
    return self.ensure_sync(self.view_functions[rule.endpoint])(**view_args)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\Users\pc\AppData\Local\Programs\Python\Python311\Lib\site-packages\flask_login\utils.py", line 290, in decorated_view
    return current_app.ensure_sync(func)(*args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\Users\pc\Documents\HBuilderProjects\APS-2025.6.20\app\decorators.py", line 24, in decorated_function
    return f(*args, **kwargs)
           ^^^^^^^^^^^^^^^^^^
  File "D:\Users\pc\Documents\HBuilderProjects\APS-2025.6.20\app\main\routes.py", line 191, in auto_orders
    return render_template('orders/auto.html')
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\Users\pc\AppData\Local\Programs\Python\Python311\Lib\site-packages\flask\templating.py", line 150, in render_template
    template = app.jinja_env.get_or_select_template(template_name_or_list)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\Users\pc\AppData\Local\Programs\Python\Python311\Lib\site-packages\jinja2\environment.py", line 1081, in get_or_select_template
    return self.get_template(template_name_or_list, parent, globals)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\Users\pc\AppData\Local\Programs\Python\Python311\Lib\site-packages\jinja2\environment.py", line 1010, in get_template
    return self._load_template(name, globals)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\Users\pc\AppData\Local\Programs\Python\Python311\Lib\site-packages\jinja2\environment.py", line 969, in _load_template
    template = self.loader.load(self, name, self.make_globals(globals))
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\Users\pc\AppData\Local\Programs\Python\Python311\Lib\site-packages\jinja2\loaders.py", line 126, in load
    source, filename, uptodate = self.get_source(environment, name)
                                 ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\Users\pc\AppData\Local\Programs\Python\Python311\Lib\site-packages\flask\templating.py", line 64, in get_source
    return self._get_source_fast(environment, template)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\Users\pc\AppData\Local\Programs\Python\Python311\Lib\site-packages\flask\templating.py", line 98, in _get_source_fast
    raise TemplateNotFound(template)
jinja2.exceptions.TemplateNotFound: orders/auto.html
2025-06-22 21:55:42 | ERROR    | app | Exception on /orders/auto [GET]
Traceback (most recent call last):
  File "D:\Users\pc\AppData\Local\Programs\Python\Python311\Lib\site-packages\flask\app.py", line 2190, in wsgi_app
    response = self.full_dispatch_request()
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\Users\pc\AppData\Local\Programs\Python\Python311\Lib\site-packages\flask\app.py", line 1486, in full_dispatch_request
    rv = self.handle_user_exception(e)
         ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\Users\pc\AppData\Local\Programs\Python\Python311\Lib\site-packages\flask_cors\extension.py", line 176, in wrapped_function
    return cors_after_request(app.make_response(f(*args, **kwargs)))
                                                ^^^^^^^^^^^^^^^^^^
  File "D:\Users\pc\AppData\Local\Programs\Python\Python311\Lib\site-packages\flask\app.py", line 1484, in full_dispatch_request
    rv = self.dispatch_request()
         ^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\Users\pc\AppData\Local\Programs\Python\Python311\Lib\site-packages\flask\app.py", line 1469, in dispatch_request
    return self.ensure_sync(self.view_functions[rule.endpoint])(**view_args)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\Users\pc\AppData\Local\Programs\Python\Python311\Lib\site-packages\flask_login\utils.py", line 290, in decorated_view
    return current_app.ensure_sync(func)(*args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\Users\pc\Documents\HBuilderProjects\APS-2025.6.20\app\decorators.py", line 24, in decorated_function
    return f(*args, **kwargs)
           ^^^^^^^^^^^^^^^^^^
  File "D:\Users\pc\Documents\HBuilderProjects\APS-2025.6.20\app\main\routes.py", line 191, in auto_orders
    return render_template('orders/auto.html')
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\Users\pc\AppData\Local\Programs\Python\Python311\Lib\site-packages\flask\templating.py", line 150, in render_template
    template = app.jinja_env.get_or_select_template(template_name_or_list)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\Users\pc\AppData\Local\Programs\Python\Python311\Lib\site-packages\jinja2\environment.py", line 1081, in get_or_select_template
    return self.get_template(template_name_or_list, parent, globals)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\Users\pc\AppData\Local\Programs\Python\Python311\Lib\site-packages\jinja2\environment.py", line 1010, in get_template
    return self._load_template(name, globals)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\Users\pc\AppData\Local\Programs\Python\Python311\Lib\site-packages\jinja2\environment.py", line 969, in _load_template
    template = self.loader.load(self, name, self.make_globals(globals))
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\Users\pc\AppData\Local\Programs\Python\Python311\Lib\site-packages\jinja2\loaders.py", line 126, in load
    source, filename, uptodate = self.get_source(environment, name)
                                 ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\Users\pc\AppData\Local\Programs\Python\Python311\Lib\site-packages\flask\templating.py", line 64, in get_source
    return self._get_source_fast(environment, template)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\Users\pc\AppData\Local\Programs\Python\Python311\Lib\site-packages\flask\templating.py", line 98, in _get_source_fast
    raise TemplateNotFound(template)
jinja2.exceptions.TemplateNotFound: orders/auto.html
2025-06-22 21:55:44 | ERROR    | app | Exception on /orders/auto [GET]
Traceback (most recent call last):
  File "D:\Users\pc\AppData\Local\Programs\Python\Python311\Lib\site-packages\flask\app.py", line 2190, in wsgi_app
    response = self.full_dispatch_request()
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\Users\pc\AppData\Local\Programs\Python\Python311\Lib\site-packages\flask\app.py", line 1486, in full_dispatch_request
    rv = self.handle_user_exception(e)
         ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\Users\pc\AppData\Local\Programs\Python\Python311\Lib\site-packages\flask_cors\extension.py", line 176, in wrapped_function
    return cors_after_request(app.make_response(f(*args, **kwargs)))
                                                ^^^^^^^^^^^^^^^^^^
  File "D:\Users\pc\AppData\Local\Programs\Python\Python311\Lib\site-packages\flask\app.py", line 1484, in full_dispatch_request
    rv = self.dispatch_request()
         ^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\Users\pc\AppData\Local\Programs\Python\Python311\Lib\site-packages\flask\app.py", line 1469, in dispatch_request
    return self.ensure_sync(self.view_functions[rule.endpoint])(**view_args)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\Users\pc\AppData\Local\Programs\Python\Python311\Lib\site-packages\flask_login\utils.py", line 290, in decorated_view
    return current_app.ensure_sync(func)(*args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\Users\pc\Documents\HBuilderProjects\APS-2025.6.20\app\decorators.py", line 24, in decorated_function
    return f(*args, **kwargs)
           ^^^^^^^^^^^^^^^^^^
  File "D:\Users\pc\Documents\HBuilderProjects\APS-2025.6.20\app\main\routes.py", line 191, in auto_orders
    return render_template('orders/auto.html')
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\Users\pc\AppData\Local\Programs\Python\Python311\Lib\site-packages\flask\templating.py", line 150, in render_template
    template = app.jinja_env.get_or_select_template(template_name_or_list)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\Users\pc\AppData\Local\Programs\Python\Python311\Lib\site-packages\jinja2\environment.py", line 1081, in get_or_select_template
    return self.get_template(template_name_or_list, parent, globals)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\Users\pc\AppData\Local\Programs\Python\Python311\Lib\site-packages\jinja2\environment.py", line 1010, in get_template
    return self._load_template(name, globals)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\Users\pc\AppData\Local\Programs\Python\Python311\Lib\site-packages\jinja2\environment.py", line 969, in _load_template
    template = self.loader.load(self, name, self.make_globals(globals))
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\Users\pc\AppData\Local\Programs\Python\Python311\Lib\site-packages\jinja2\loaders.py", line 126, in load
    source, filename, uptodate = self.get_source(environment, name)
                                 ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\Users\pc\AppData\Local\Programs\Python\Python311\Lib\site-packages\flask\templating.py", line 64, in get_source
    return self._get_source_fast(environment, template)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\Users\pc\AppData\Local\Programs\Python\Python311\Lib\site-packages\flask\templating.py", line 98, in _get_source_fast
    raise TemplateNotFound(template)
jinja2.exceptions.TemplateNotFound: orders/auto.html
2025-06-22 22:03:12 | ERROR    | app | Exception on /orders/auto [GET]
Traceback (most recent call last):
  File "D:\Users\pc\AppData\Local\Programs\Python\Python311\Lib\site-packages\flask\app.py", line 2190, in wsgi_app
    response = self.full_dispatch_request()
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\Users\pc\AppData\Local\Programs\Python\Python311\Lib\site-packages\flask\app.py", line 1486, in full_dispatch_request
    rv = self.handle_user_exception(e)
         ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\Users\pc\AppData\Local\Programs\Python\Python311\Lib\site-packages\flask_cors\extension.py", line 176, in wrapped_function
    return cors_after_request(app.make_response(f(*args, **kwargs)))
                                                ^^^^^^^^^^^^^^^^^^
  File "D:\Users\pc\AppData\Local\Programs\Python\Python311\Lib\site-packages\flask\app.py", line 1484, in full_dispatch_request
    rv = self.dispatch_request()
         ^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\Users\pc\AppData\Local\Programs\Python\Python311\Lib\site-packages\flask\app.py", line 1469, in dispatch_request
    return self.ensure_sync(self.view_functions[rule.endpoint])(**view_args)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\Users\pc\AppData\Local\Programs\Python\Python311\Lib\site-packages\flask_login\utils.py", line 290, in decorated_view
    return current_app.ensure_sync(func)(*args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\Users\pc\Documents\HBuilderProjects\APS-2025.6.20\app\decorators.py", line 24, in decorated_function
    return f(*args, **kwargs)
           ^^^^^^^^^^^^^^^^^^
  File "D:\Users\pc\Documents\HBuilderProjects\APS-2025.6.20\app\main\routes.py", line 191, in auto_orders
    return render_template('orders/auto.html')
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\Users\pc\AppData\Local\Programs\Python\Python311\Lib\site-packages\flask\templating.py", line 150, in render_template
    template = app.jinja_env.get_or_select_template(template_name_or_list)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\Users\pc\AppData\Local\Programs\Python\Python311\Lib\site-packages\jinja2\environment.py", line 1081, in get_or_select_template
    return self.get_template(template_name_or_list, parent, globals)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\Users\pc\AppData\Local\Programs\Python\Python311\Lib\site-packages\jinja2\environment.py", line 1010, in get_template
    return self._load_template(name, globals)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\Users\pc\AppData\Local\Programs\Python\Python311\Lib\site-packages\jinja2\environment.py", line 969, in _load_template
    template = self.loader.load(self, name, self.make_globals(globals))
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\Users\pc\AppData\Local\Programs\Python\Python311\Lib\site-packages\jinja2\loaders.py", line 126, in load
    source, filename, uptodate = self.get_source(environment, name)
                                 ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\Users\pc\AppData\Local\Programs\Python\Python311\Lib\site-packages\flask\templating.py", line 64, in get_source
    return self._get_source_fast(environment, template)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\Users\pc\AppData\Local\Programs\Python\Python311\Lib\site-packages\flask\templating.py", line 98, in _get_source_fast
    raise TemplateNotFound(template)
jinja2.exceptions.TemplateNotFound: orders/auto.html
2025-06-22 22:03:14 | ERROR    | app | Exception on /orders/auto [GET]
Traceback (most recent call last):
  File "D:\Users\pc\AppData\Local\Programs\Python\Python311\Lib\site-packages\flask\app.py", line 2190, in wsgi_app
    response = self.full_dispatch_request()
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\Users\pc\AppData\Local\Programs\Python\Python311\Lib\site-packages\flask\app.py", line 1486, in full_dispatch_request
    rv = self.handle_user_exception(e)
         ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\Users\pc\AppData\Local\Programs\Python\Python311\Lib\site-packages\flask_cors\extension.py", line 176, in wrapped_function
    return cors_after_request(app.make_response(f(*args, **kwargs)))
                                                ^^^^^^^^^^^^^^^^^^
  File "D:\Users\pc\AppData\Local\Programs\Python\Python311\Lib\site-packages\flask\app.py", line 1484, in full_dispatch_request
    rv = self.dispatch_request()
         ^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\Users\pc\AppData\Local\Programs\Python\Python311\Lib\site-packages\flask\app.py", line 1469, in dispatch_request
    return self.ensure_sync(self.view_functions[rule.endpoint])(**view_args)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\Users\pc\AppData\Local\Programs\Python\Python311\Lib\site-packages\flask_login\utils.py", line 290, in decorated_view
    return current_app.ensure_sync(func)(*args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\Users\pc\Documents\HBuilderProjects\APS-2025.6.20\app\decorators.py", line 24, in decorated_function
    return f(*args, **kwargs)
           ^^^^^^^^^^^^^^^^^^
  File "D:\Users\pc\Documents\HBuilderProjects\APS-2025.6.20\app\main\routes.py", line 191, in auto_orders
    return render_template('orders/auto.html')
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\Users\pc\AppData\Local\Programs\Python\Python311\Lib\site-packages\flask\templating.py", line 150, in render_template
    template = app.jinja_env.get_or_select_template(template_name_or_list)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\Users\pc\AppData\Local\Programs\Python\Python311\Lib\site-packages\jinja2\environment.py", line 1081, in get_or_select_template
    return self.get_template(template_name_or_list, parent, globals)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\Users\pc\AppData\Local\Programs\Python\Python311\Lib\site-packages\jinja2\environment.py", line 1010, in get_template
    return self._load_template(name, globals)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\Users\pc\AppData\Local\Programs\Python\Python311\Lib\site-packages\jinja2\environment.py", line 969, in _load_template
    template = self.loader.load(self, name, self.make_globals(globals))
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\Users\pc\AppData\Local\Programs\Python\Python311\Lib\site-packages\jinja2\loaders.py", line 126, in load
    source, filename, uptodate = self.get_source(environment, name)
                                 ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\Users\pc\AppData\Local\Programs\Python\Python311\Lib\site-packages\flask\templating.py", line 64, in get_source
    return self._get_source_fast(environment, template)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\Users\pc\AppData\Local\Programs\Python\Python311\Lib\site-packages\flask\templating.py", line 98, in _get_source_fast
    raise TemplateNotFound(template)
jinja2.exceptions.TemplateNotFound: orders/auto.html
2025-06-22 23:14:19 | ERROR    | app.api_v2.system.routes | 邮件调度器控制失败: cannot access local variable 'db' where it is not associated with a value
2025-06-22 23:24:27 | ERROR    | app.services.scheduler_service | 从数据库加载任务失败: (pymysql.err.ProgrammingError) (1146, "Table 'aps_system.scheduler_jobs' doesn't exist")
[SQL: SELECT scheduler_jobs.id AS scheduler_jobs_id, scheduler_jobs.name AS scheduler_jobs_name, scheduler_jobs.job_type AS scheduler_jobs_job_type, scheduler_jobs.func AS scheduler_jobs_func, scheduler_jobs.args AS scheduler_jobs_args, scheduler_jobs.kwargs AS scheduler_jobs_kwargs, scheduler_jobs.`trigger` AS scheduler_jobs_trigger, scheduler_jobs.trigger_args AS scheduler_jobs_trigger_args, scheduler_jobs.enabled AS scheduler_jobs_enabled, scheduler_jobs.created_at AS scheduler_jobs_created_at, scheduler_jobs.updated_at AS scheduler_jobs_updated_at, scheduler_jobs.created_by AS scheduler_jobs_created_by, scheduler_jobs.description AS scheduler_jobs_description 
FROM scheduler_jobs 
WHERE scheduler_jobs.enabled = true]
(Background on this error at: https://sqlalche.me/e/20/f405)
2025-06-22 23:24:46 | ERROR    | app.services.scheduler_service | 从数据库加载任务失败: (pymysql.err.ProgrammingError) (1146, "Table 'aps_system.scheduler_jobs' doesn't exist")
[SQL: SELECT scheduler_jobs.id AS scheduler_jobs_id, scheduler_jobs.name AS scheduler_jobs_name, scheduler_jobs.job_type AS scheduler_jobs_job_type, scheduler_jobs.func AS scheduler_jobs_func, scheduler_jobs.args AS scheduler_jobs_args, scheduler_jobs.kwargs AS scheduler_jobs_kwargs, scheduler_jobs.`trigger` AS scheduler_jobs_trigger, scheduler_jobs.trigger_args AS scheduler_jobs_trigger_args, scheduler_jobs.enabled AS scheduler_jobs_enabled, scheduler_jobs.created_at AS scheduler_jobs_created_at, scheduler_jobs.updated_at AS scheduler_jobs_updated_at, scheduler_jobs.created_by AS scheduler_jobs_created_by, scheduler_jobs.description AS scheduler_jobs_description 
FROM scheduler_jobs 
WHERE scheduler_jobs.enabled = true]
(Background on this error at: https://sqlalche.me/e/20/f405)
2025-06-22 23:25:38 | ERROR    | app.api_v2.system.routes | 邮件调度器控制失败: 'APSchedulerService' object has no attribute 'shutdown'
2025-06-22 23:25:43 | ERROR    | app.api_v2.system.routes | 邮件调度器控制失败: 'APSchedulerService' object has no attribute 'shutdown'
2025-06-22 23:26:19 | ERROR    | app.api_v2.system.routes | 邮件调度器控制失败: 'APSchedulerService' object has no attribute 'shutdown'
2025-06-23 00:06:31 | ERROR    | app.api_v2.orders.high_concurrency_api | 高并发邮件处理出错: Working outside of application context.

This typically means that you attempted to use functionality that needed
the current application. To solve this, set up an application context
with app.app_context(). See the documentation for more information.
2025-06-23 00:06:37 | ERROR    | app.api_v2.orders.high_concurrency_api | 获取性能指标失败: Object of type method is not JSON serializable
2025-06-23 00:06:47 | ERROR    | app.api_v2.orders.high_concurrency_api | 获取性能指标失败: Object of type method is not JSON serializable
2025-06-23 00:06:57 | ERROR    | app.api_v2.orders.high_concurrency_api | 获取性能指标失败: Object of type method is not JSON serializable
2025-06-23 00:07:03 | ERROR    | app.api_v2.orders.high_concurrency_api | 高并发邮件处理出错: Working outside of application context.

This typically means that you attempted to use functionality that needed
the current application. To solve this, set up an application context
with app.app_context(). See the documentation for more information.
2025-06-23 00:07:07 | ERROR    | app.api_v2.orders.high_concurrency_api | 获取性能指标失败: Object of type method is not JSON serializable
2025-06-23 00:07:18 | ERROR    | app.api_v2.orders.high_concurrency_api | 获取性能指标失败: Object of type method is not JSON serializable
2025-06-23 00:07:27 | ERROR    | app.api_v2.orders.high_concurrency_api | 获取性能指标失败: Object of type method is not JSON serializable
2025-06-23 00:07:38 | ERROR    | app.api_v2.orders.high_concurrency_api | 获取性能指标失败: Object of type method is not JSON serializable
2025-06-23 00:07:47 | ERROR    | app.api_v2.orders.high_concurrency_api | 获取性能指标失败: Object of type method is not JSON serializable
2025-06-23 00:07:58 | ERROR    | app.api_v2.orders.high_concurrency_api | 获取性能指标失败: Object of type method is not JSON serializable
2025-06-23 00:08:07 | ERROR    | app.api_v2.orders.high_concurrency_api | 获取性能指标失败: Object of type method is not JSON serializable
2025-06-23 00:08:18 | ERROR    | app.api_v2.orders.high_concurrency_api | 获取性能指标失败: Object of type method is not JSON serializable
2025-06-23 00:08:27 | ERROR    | app.api_v2.orders.high_concurrency_api | 获取性能指标失败: Object of type method is not JSON serializable
2025-06-23 00:08:38 | ERROR    | app.api_v2.orders.high_concurrency_api | 获取性能指标失败: Object of type method is not JSON serializable
2025-06-23 00:09:39 | ERROR    | app.api_v2.orders.high_concurrency_api | 获取性能指标失败: Object of type method is not JSON serializable
2025-06-23 00:10:40 | ERROR    | app.api_v2.orders.high_concurrency_api | 获取性能指标失败: Object of type method is not JSON serializable
2025-06-23 00:11:18 | ERROR    | app.api_v2.orders.high_concurrency_api | 高并发邮件处理出错: Working outside of application context.

This typically means that you attempted to use functionality that needed
the current application. To solve this, set up an application context
with app.app_context(). See the documentation for more information.
2025-06-23 00:12:57 | ERROR    | app.api_v2.orders.high_concurrency_api | 高并发邮件处理出错: Working outside of application context.

This typically means that you attempted to use functionality that needed
the current application. To solve this, set up an application context
with app.app_context(). See the documentation for more information.
2025-06-23 00:13:24 | ERROR    | app.api_v2.orders.high_concurrency_api | 高并发邮件处理出错: Working outside of application context.

This typically means that you attempted to use functionality that needed
the current application. To solve this, set up an application context
with app.app_context(). See the documentation for more information.
2025-06-23 00:17:35 | ERROR    | app.api_v2.orders.high_concurrency_api | 高并发邮件处理出错: Working outside of application context.

This typically means that you attempted to use functionality that needed
the current application. To solve this, set up an application context
with app.app_context(). See the documentation for more information.
2025-06-23 00:17:35 | ERROR    | app.api_v2.orders.high_concurrency_api | 详细错误信息: Traceback (most recent call last):
  File "D:\Users\pc\Documents\HBuilderProjects\APS-2025.6.20\app\api_v2\orders\high_concurrency_api.py", line 85, in run_processing
    app = current_app._get_current_object()
          ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\Users\pc\AppData\Local\Programs\Python\Python311\Lib\site-packages\werkzeug\local.py", line 508, in _get_current_object
    raise RuntimeError(unbound_message) from None
RuntimeError: Working outside of application context.

This typically means that you attempted to use functionality that needed
the current application. To solve this, set up an application context
with app.app_context(). See the documentation for more information.

2025-06-23 00:17:55 | ERROR    | app.api_v2.orders.high_concurrency_api | 高并发邮件处理出错: Working outside of application context.

This typically means that you attempted to use functionality that needed
the current application. To solve this, set up an application context
with app.app_context(). See the documentation for more information.
2025-06-23 00:17:55 | ERROR    | app.api_v2.orders.high_concurrency_api | 详细错误信息: Traceback (most recent call last):
  File "D:\Users\pc\Documents\HBuilderProjects\APS-2025.6.20\app\api_v2\orders\high_concurrency_api.py", line 85, in run_processing
    app = current_app._get_current_object()
          ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\Users\pc\AppData\Local\Programs\Python\Python311\Lib\site-packages\werkzeug\local.py", line 508, in _get_current_object
    raise RuntimeError(unbound_message) from None
RuntimeError: Working outside of application context.

This typically means that you attempted to use functionality that needed
the current application. To solve this, set up an application context
with app.app_context(). See the documentation for more information.

2025-06-23 00:20:24 | ERROR    | app.services.high_concurrency_email_processor | 邮箱配置 'Oliver' 处理失败: Working outside of application context.

This typically means that you attempted to use functionality that needed
the current application. To solve this, set up an application context
with app.app_context(). See the documentation for more information.
2025-06-23 00:20:42 | ERROR    | app.services.high_concurrency_email_processor | 邮箱配置 'Oliver' 处理失败: Working outside of application context.

This typically means that you attempted to use functionality that needed
the current application. To solve this, set up an application context
with app.app_context(). See the documentation for more information.
2025-06-23 00:35:20 | ERROR    | app.utils.email_processor | 无法选择文件夹 'JW Order', 跳过此文件夹
2025-06-23 01:25:08 | ERROR    | app.services.high_concurrency_email_processor | ❌ 高并发邮件处理失败: Working outside of application context.

This typically means that you attempted to use functionality that needed
the current application. To solve this, set up an application context
with app.app_context(). See the documentation for more information.
2025-06-23 01:30:29 | ERROR    | app.utils.email_processor | IMAP登录错误: b'ERR.LOGIN.PASSERR'
2025-06-23 01:30:34 | ERROR    | app.utils.email_processor | IMAP登录错误: b'ERR.LOGIN.PASSERR'
2025-06-23 01:30:38 | ERROR    | app.utils.email_processor | IMAP登录错误: b'ERR.LOGIN.PASSERR'
2025-06-23 01:30:46 | ERROR    | app.utils.email_processor | IMAP登录错误: b'ERR.LOGIN.PASSERR'
2025-06-23 01:30:57 | ERROR    | app.utils.email_processor | IMAP登录错误: b'ERR.LOGIN.PASSERR'
2025-06-23 01:33:59 | ERROR    | app.utils.email_processor | IMAP登录错误: b'ERR.LOGIN.PASSERR'
2025-06-23 01:33:59 | ERROR    | app.utils.email_processor | 列出文件夹失败: command LIST illegal in state NONAUTH, only allowed in states AUTH, SELECTED
2025-06-23 01:33:59 | ERROR    | app.utils.email_processor | 断开邮箱连接失败: command: LOGOUT => socket error: EOF
2025-06-23 01:34:44 | ERROR    | app.utils.email_processor | IMAP登录错误: b'ERR.LOGIN.PASSERR'
2025-06-23 01:34:45 | ERROR    | app.utils.email_processor | IMAP登录错误: b'ERR.LOGIN.PASSERR'
2025-06-23 01:34:49 | ERROR    | app.utils.email_processor | IMAP登录错误: b'ERR.LOGIN.PASSERR'
2025-06-23 09:28:46 | ERROR    | app.utils.email_processor | 获取订单数据统计失败: (pymysql.err.OperationalError) (1054, "Unknown column 'order_data.document_type' in 'field list'")
[SQL: SELECT count(*) AS count_1 
FROM (SELECT order_data.id AS order_data_id, order_data.document_type AS order_data_document_type, order_data.document_number AS order_data_document_number, order_data.processing_type AS order_data_processing_type, order_data.order_date AS order_data_order_date, order_data.contractor_name AS order_data_contractor_name, order_data.contractor_contact AS order_data_contractor_contact, order_data.contractor_address AS order_data_contractor_address, order_data.contractor_phone AS order_data_contractor_phone, order_data.contractor_email AS order_data_contractor_email, order_data.client_name AS order_data_client_name, order_data.order_number AS order_data_order_number, order_data.product_name AS order_data_product_name, order_data.circuit_name AS order_data_circuit_name, order_data.chip_name AS order_data_chip_name, order_data.wafer_size AS order_data_wafer_size, order_data.package_qty AS order_data_package_qty, order_data.package_pieces AS order_data_package_pieces, order_data.diffusion_batch AS order_data_diffusion_batch, order_data.wafer_number AS order_data_wafer_number, order_data.assembly_method AS order_data_assembly_method, order_data.drawing_number AS order_data_drawing_number, order_data.package_form AS order_data_package_form, order_data.stamp_line1 AS order_data_stamp_line1, order_data.stamp_line2 AS order_data_stamp_line2, order_data.stamp_line3 AS order_data_stamp_line3, order_data.other_notes AS order_data_other_notes, order_data.delivery_date AS order_data_delivery_date, order_data.env_requirement AS order_data_env_requirement, order_data.msl_requirement AS order_data_msl_requirement, order_data.reliability_requirement AS order_data_reliability_requirement, order_data.print_pin_dot AS order_data_print_pin_dot, order_data.pin_dot_position AS order_data_pin_dot_position, order_data.item_code AS order_data_item_code, order_data.shipping_address AS order_data_shipping_address, order_data.wafer_lot AS order_data_wafer_lot, order_data.order_attribute AS order_data_order_attribute, order_data.lot_type AS order_data_lot_type, order_data.classification AS order_data_classification, order_data.wafer_id AS order_data_wafer_id, order_data.customer AS order_data_customer, order_data.product_code AS order_data_product_code, order_data.quantity AS order_data_quantity, order_data.unit_price AS order_data_unit_price, order_data.total_price AS order_data_total_price, order_data.status AS order_data_status, order_data.urgent AS order_data_urgent, order_data.owner AS order_data_owner, order_data.note AS order_data_note, order_data.source_file AS order_data_source_file, order_data.raw_data AS order_data_raw_data, order_data.horizontal_data AS order_data_horizontal_data, order_data.vertical_data AS order_data_vertical_data, order_data.data_row_number AS order_data_data_row_number, order_data.extraction_method AS order_data_extraction_method, order_data.extraction_info AS order_data_extraction_info, order_data.created_by AS order_data_created_by, order_data.created_at AS order_data_created_at, order_data.updated_at AS order_data_updated_at, order_data.imported_at AS order_data_imported_at, order_data.processed_at AS order_data_processed_at 
FROM order_data) AS anon_1]
(Background on this error at: https://sqlalche.me/e/20/e3q8)
2025-06-23 09:45:57 | ERROR    | app.utils.email_processor | 获取订单数据统计失败: (pymysql.err.OperationalError) (1054, "Unknown column 'order_data.document_type' in 'field list'")
[SQL: SELECT count(*) AS count_1 
FROM (SELECT order_data.id AS order_data_id, order_data.document_type AS order_data_document_type, order_data.document_number AS order_data_document_number, order_data.processing_type AS order_data_processing_type, order_data.order_date AS order_data_order_date, order_data.contractor_name AS order_data_contractor_name, order_data.contractor_contact AS order_data_contractor_contact, order_data.contractor_address AS order_data_contractor_address, order_data.contractor_phone AS order_data_contractor_phone, order_data.contractor_email AS order_data_contractor_email, order_data.client_name AS order_data_client_name, order_data.order_number AS order_data_order_number, order_data.product_name AS order_data_product_name, order_data.circuit_name AS order_data_circuit_name, order_data.chip_name AS order_data_chip_name, order_data.wafer_size AS order_data_wafer_size, order_data.package_qty AS order_data_package_qty, order_data.package_pieces AS order_data_package_pieces, order_data.diffusion_batch AS order_data_diffusion_batch, order_data.wafer_number AS order_data_wafer_number, order_data.assembly_method AS order_data_assembly_method, order_data.drawing_number AS order_data_drawing_number, order_data.package_form AS order_data_package_form, order_data.stamp_line1 AS order_data_stamp_line1, order_data.stamp_line2 AS order_data_stamp_line2, order_data.stamp_line3 AS order_data_stamp_line3, order_data.other_notes AS order_data_other_notes, order_data.delivery_date AS order_data_delivery_date, order_data.env_requirement AS order_data_env_requirement, order_data.msl_requirement AS order_data_msl_requirement, order_data.reliability_requirement AS order_data_reliability_requirement, order_data.print_pin_dot AS order_data_print_pin_dot, order_data.pin_dot_position AS order_data_pin_dot_position, order_data.item_code AS order_data_item_code, order_data.shipping_address AS order_data_shipping_address, order_data.wafer_lot AS order_data_wafer_lot, order_data.order_attribute AS order_data_order_attribute, order_data.lot_type AS order_data_lot_type, order_data.classification AS order_data_classification, order_data.wafer_id AS order_data_wafer_id, order_data.customer AS order_data_customer, order_data.product_code AS order_data_product_code, order_data.quantity AS order_data_quantity, order_data.unit_price AS order_data_unit_price, order_data.total_price AS order_data_total_price, order_data.status AS order_data_status, order_data.urgent AS order_data_urgent, order_data.owner AS order_data_owner, order_data.note AS order_data_note, order_data.source_file AS order_data_source_file, order_data.raw_data AS order_data_raw_data, order_data.horizontal_data AS order_data_horizontal_data, order_data.vertical_data AS order_data_vertical_data, order_data.data_row_number AS order_data_data_row_number, order_data.extraction_method AS order_data_extraction_method, order_data.extraction_info AS order_data_extraction_info, order_data.created_by AS order_data_created_by, order_data.created_at AS order_data_created_at, order_data.updated_at AS order_data_updated_at, order_data.imported_at AS order_data_imported_at, order_data.processed_at AS order_data_processed_at 
FROM order_data) AS anon_1]
(Background on this error at: https://sqlalche.me/e/20/e3q8)
2025-06-23 09:48:32 | ERROR    | app.utils.email_processor | 获取订单数据统计失败: (pymysql.err.OperationalError) (1054, "Unknown column 'order_data.document_type' in 'field list'")
[SQL: SELECT count(*) AS count_1 
FROM (SELECT order_data.id AS order_data_id, order_data.document_type AS order_data_document_type, order_data.document_number AS order_data_document_number, order_data.processing_type AS order_data_processing_type, order_data.order_date AS order_data_order_date, order_data.contractor_name AS order_data_contractor_name, order_data.contractor_contact AS order_data_contractor_contact, order_data.contractor_address AS order_data_contractor_address, order_data.contractor_phone AS order_data_contractor_phone, order_data.contractor_email AS order_data_contractor_email, order_data.client_name AS order_data_client_name, order_data.order_number AS order_data_order_number, order_data.product_name AS order_data_product_name, order_data.circuit_name AS order_data_circuit_name, order_data.chip_name AS order_data_chip_name, order_data.wafer_size AS order_data_wafer_size, order_data.package_qty AS order_data_package_qty, order_data.package_pieces AS order_data_package_pieces, order_data.diffusion_batch AS order_data_diffusion_batch, order_data.wafer_number AS order_data_wafer_number, order_data.assembly_method AS order_data_assembly_method, order_data.drawing_number AS order_data_drawing_number, order_data.package_form AS order_data_package_form, order_data.stamp_line1 AS order_data_stamp_line1, order_data.stamp_line2 AS order_data_stamp_line2, order_data.stamp_line3 AS order_data_stamp_line3, order_data.other_notes AS order_data_other_notes, order_data.delivery_date AS order_data_delivery_date, order_data.env_requirement AS order_data_env_requirement, order_data.msl_requirement AS order_data_msl_requirement, order_data.reliability_requirement AS order_data_reliability_requirement, order_data.print_pin_dot AS order_data_print_pin_dot, order_data.pin_dot_position AS order_data_pin_dot_position, order_data.item_code AS order_data_item_code, order_data.shipping_address AS order_data_shipping_address, order_data.wafer_lot AS order_data_wafer_lot, order_data.order_attribute AS order_data_order_attribute, order_data.lot_type AS order_data_lot_type, order_data.classification AS order_data_classification, order_data.wafer_id AS order_data_wafer_id, order_data.customer AS order_data_customer, order_data.product_code AS order_data_product_code, order_data.quantity AS order_data_quantity, order_data.unit_price AS order_data_unit_price, order_data.total_price AS order_data_total_price, order_data.status AS order_data_status, order_data.urgent AS order_data_urgent, order_data.owner AS order_data_owner, order_data.note AS order_data_note, order_data.source_file AS order_data_source_file, order_data.raw_data AS order_data_raw_data, order_data.horizontal_data AS order_data_horizontal_data, order_data.vertical_data AS order_data_vertical_data, order_data.data_row_number AS order_data_data_row_number, order_data.extraction_method AS order_data_extraction_method, order_data.extraction_info AS order_data_extraction_info, order_data.created_by AS order_data_created_by, order_data.created_at AS order_data_created_at, order_data.updated_at AS order_data_updated_at, order_data.imported_at AS order_data_imported_at, order_data.processed_at AS order_data_processed_at 
FROM order_data) AS anon_1]
(Background on this error at: https://sqlalche.me/e/20/e3q8)
2025-06-23 10:08:21 | ERROR    | app.utils.email_processor | IMAP登录错误: b'ERR.LOGIN.PASSERR'
2025-06-23 10:08:22 | ERROR    | app.utils.email_processor | IMAP登录错误: b'ERR.LOGIN.PASSERR'
2025-06-23 10:08:49 | ERROR    | app.utils.email_processor | IMAP登录错误: b'ERR.LOGIN.PASSERR'
2025-06-23 10:08:55 | ERROR    | app.utils.email_processor | IMAP登录错误: b'ERR.LOGIN.PASSERR'
2025-06-23 10:09:08 | ERROR    | app.utils.email_processor | IMAP登录错误: b'ERR.LOGIN.PASSERR'
2025-06-23 10:09:09 | ERROR    | app.utils.email_processor | IMAP登录错误: b'ERR.LOGIN.PASSERR'
2025-06-23 10:12:00 | ERROR    | app.utils.email_processor | IMAP登录错误: b'ERR.LOGIN.PASSERR'
2025-06-23 10:13:25 | ERROR    | app.utils.email_processor | IMAP登录错误: b'ERR.LOGIN.PASSERR'
2025-06-23 10:33:37 | ERROR    | app.api.order_processing_api | 订单处理任务执行出错: Working outside of application context.

This typically means that you attempted to use functionality that needed
the current application. To solve this, set up an application context
with app.app_context(). See the documentation for more information.
2025-06-23 10:33:58 | ERROR    | app.api.order_processing_api | 订单处理任务执行出错: Working outside of application context.

This typically means that you attempted to use functionality that needed
the current application. To solve this, set up an application context
with app.app_context(). See the documentation for more information.
2025-06-23 10:37:19 | ERROR    | app.api.order_processing_api | 订单处理任务执行出错: Working outside of application context.

This typically means that you attempted to use functionality that needed
the current application. To solve this, set up an application context
with app.app_context(). See the documentation for more information.
2025-06-23 10:40:42 | ERROR    | app.api.order_processing_api | 处理邮箱配置出错: 'EmailProcessor' object has no attribute 'test_connection'
2025-06-23 11:23:53 | ERROR    | app.utils.optimized_email_processor | 连接邮箱失败: 'EmailConfig' object has no attribute 'enable_ssl'
2025-06-23 11:25:20 | ERROR    | app.api.order_processing_api | 解析文件出错 宜欣  生产订单模板(新封装-测试-编带)2025061601  JWH72964AVQEGA_TR1.xls: 'EnhancedExcelParser' object has no attribute 'parse_file'
2025-06-23 11:25:20 | ERROR    | app.api.order_processing_api | 解析文件出错 宜欣  生产订单模板(新封装-测试-编带)2025061602  JWH72964AVQEGA-M001_TR1.xls: 'EnhancedExcelParser' object has no attribute 'parse_file'
2025-06-23 11:25:20 | ERROR    | app.api.order_processing_api | 解析文件出错 宜欣　生产订单模板(新封装-测试-编带)2025061603　JWQ85213-C244QFNA-SJA1_TR1.xls: 'EnhancedExcelParser' object has no attribute 'parse_file'
2025-06-23 11:25:20 | ERROR    | app.api.order_processing_api | 解析文件出错 宜欣　生产订单模板(新封装-测试-编带)2025061604　DMHV-1400-B-M001.xls: 'EnhancedExcelParser' object has no attribute 'parse_file'
2025-06-23 11:25:20 | ERROR    | app.api.order_processing_api | 解析文件出错 宜欣　生产订单模板(新封装-测试-编带)2025061605　JW7106.xls: 'EnhancedExcelParser' object has no attribute 'parse_file'
2025-06-23 11:25:20 | ERROR    | app.api.order_processing_api | 解析文件出错 宜欣　生产订单模板(新封装-测试-编带)2025061606　JWS29002VDGBA_TR1.xls: 'EnhancedExcelParser' object has no attribute 'parse_file'
2025-06-23 11:25:20 | ERROR    | app.api.order_processing_api | 解析文件出错 宜欣　生产订单模板(新封装-测试-编带)2025061607　JW7106-M001.xls: 'EnhancedExcelParser' object has no attribute 'parse_file'
2025-06-23 11:49:41 | ERROR    | app.api.order_processing_api | 解析文件出错 宜欣  生产订单模板(新封装-测试-编带)2025061601  JWH72964AVQEGA_TR1.xls: 'EnhancedExcelParser' object has no attribute 'parse_file'
2025-06-23 11:49:41 | ERROR    | app.api.order_processing_api | 解析文件出错 宜欣  生产订单模板(新封装-测试-编带)2025061602  JWH72964AVQEGA-M001_TR1.xls: 'EnhancedExcelParser' object has no attribute 'parse_file'
2025-06-23 11:49:41 | ERROR    | app.api.order_processing_api | 解析文件出错 宜欣　生产订单模板(新封装-测试-编带)2025061603　JWQ85213-C244QFNA-SJA1_TR1.xls: 'EnhancedExcelParser' object has no attribute 'parse_file'
2025-06-23 11:49:41 | ERROR    | app.api.order_processing_api | 解析文件出错 宜欣　生产订单模板(新封装-测试-编带)2025061604　DMHV-1400-B-M001.xls: 'EnhancedExcelParser' object has no attribute 'parse_file'
2025-06-23 11:49:41 | ERROR    | app.api.order_processing_api | 解析文件出错 宜欣　生产订单模板(新封装-测试-编带)2025061605　JW7106.xls: 'EnhancedExcelParser' object has no attribute 'parse_file'
2025-06-23 11:49:41 | ERROR    | app.api.order_processing_api | 解析文件出错 宜欣　生产订单模板(新封装-测试-编带)2025061606　JWS29002VDGBA_TR1.xls: 'EnhancedExcelParser' object has no attribute 'parse_file'
2025-06-23 11:49:41 | ERROR    | app.api.order_processing_api | 解析文件出错 宜欣　生产订单模板(新封装-测试-编带)2025061607　JW7106-M001.xls: 'EnhancedExcelParser' object has no attribute 'parse_file'
2025-06-23 11:53:19 | ERROR    | app.utils.high_performance_email_processor | ❌ 连接失败: 'EmailConfig' object has no attribute 'imap_server'
2025-06-23 11:54:05 | ERROR    | app.utils.high_performance_email_processor | 处理单邮件失败: 'EmailConfig' object has no attribute 'attachment_keywords'
2025-06-23 11:54:05 | ERROR    | app.utils.high_performance_email_processor | 处理单邮件失败: 'EmailConfig' object has no attribute 'attachment_keywords'
2025-06-23 11:54:06 | ERROR    | app.utils.high_performance_email_processor | 处理单邮件失败: 'EmailConfig' object has no attribute 'attachment_keywords'
2025-06-23 11:54:06 | ERROR    | app.utils.high_performance_email_processor | 处理单邮件失败: 'EmailConfig' object has no attribute 'attachment_keywords'
2025-06-23 11:54:06 | ERROR    | app.utils.high_performance_email_processor | 处理单邮件失败: 'EmailConfig' object has no attribute 'attachment_keywords'
2025-06-23 11:54:07 | ERROR    | app.utils.high_performance_email_processor | 处理单邮件失败: 'EmailConfig' object has no attribute 'attachment_keywords'
2025-06-23 11:54:07 | ERROR    | app.utils.high_performance_email_processor | 处理单邮件失败: 'EmailConfig' object has no attribute 'attachment_keywords'
2025-06-23 11:54:07 | ERROR    | app.utils.high_performance_email_processor | 处理单邮件失败: 'EmailConfig' object has no attribute 'attachment_keywords'
2025-06-23 11:54:08 | ERROR    | app.utils.high_performance_email_processor | 处理单邮件失败: 'EmailConfig' object has no attribute 'attachment_keywords'
2025-06-23 11:54:08 | ERROR    | app.utils.high_performance_email_processor | 处理单邮件失败: 'EmailConfig' object has no attribute 'attachment_keywords'
2025-06-23 11:54:09 | ERROR    | app.utils.high_performance_email_processor | 处理单邮件失败: 'EmailConfig' object has no attribute 'attachment_keywords'
2025-06-23 11:54:09 | ERROR    | app.utils.high_performance_email_processor | 处理单邮件失败: 'EmailConfig' object has no attribute 'attachment_keywords'
2025-06-23 11:54:10 | ERROR    | app.utils.high_performance_email_processor | 处理单邮件失败: 'EmailConfig' object has no attribute 'attachment_keywords'
2025-06-23 11:54:10 | ERROR    | app.utils.high_performance_email_processor | 处理单邮件失败: 'EmailConfig' object has no attribute 'attachment_keywords'
2025-06-23 11:54:10 | ERROR    | app.utils.high_performance_email_processor | 处理单邮件失败: 'EmailConfig' object has no attribute 'attachment_keywords'
2025-06-23 11:54:11 | ERROR    | app.utils.high_performance_email_processor | 处理单邮件失败: 'EmailConfig' object has no attribute 'attachment_keywords'
2025-06-23 11:54:12 | ERROR    | app.utils.high_performance_email_processor | 处理单邮件失败: 'EmailConfig' object has no attribute 'attachment_keywords'
2025-06-23 13:59:55 | ERROR    | app.services.universal_excel_parser | ❌ 解析文件失败 downloads/email_attachments\20250616\宜欣  生产订单模板(新封装-测试-编带)2025061601  JWH72964AVQEGA_TR1_1beb6cf9.xls: 'OrderExcelParser' object has no attribute 'parse_excel_file'
2025-06-23 13:59:55 | ERROR    | app.services.universal_excel_parser | ❌ 解析文件失败 downloads/email_attachments\20250616\宜欣  生产订单模板(新封装-测试-编带)2025061602  JWH72964AVQEGA-M001_TR1_908499d2.xls: 'OrderExcelParser' object has no attribute 'parse_excel_file'
2025-06-23 13:59:55 | ERROR    | app.services.universal_excel_parser | ❌ 解析文件失败 downloads/email_attachments\20250616\宜欣  生产订单模板(新封装-测试-编带)2025061613  LYW6119Ea5QFND-SDA1_TR1_1b722961.xls: 'OrderExcelParser' object has no attribute 'parse_excel_file'
2025-06-23 13:59:55 | ERROR    | app.services.universal_excel_parser | ❌ 解析文件失败 downloads/email_attachments\20250616\宜欣　生产订单模板(新封装-测试-编带)2025061603　JWQ85213-C244QFNA-SJA1_TR1_ce8ab928.xls: 'OrderExcelParser' object has no attribute 'parse_excel_file'
2025-06-23 13:59:55 | ERROR    | app.services.universal_excel_parser | ❌ 解析文件失败 downloads/email_attachments\20250616\宜欣　生产订单模板(新封装-测试-编带)2025061604　DMHV-1400-B-M001_0f479d0b.xls: 'OrderExcelParser' object has no attribute 'parse_excel_file'
2025-06-23 13:59:55 | ERROR    | app.services.universal_excel_parser | ❌ 解析文件失败 downloads/email_attachments\20250616\宜欣　生产订单模板(新封装-测试-编带)2025061605　JW7106_f546ab77.xls: 'OrderExcelParser' object has no attribute 'parse_excel_file'
2025-06-23 13:59:55 | ERROR    | app.services.universal_excel_parser | ❌ 解析文件失败 downloads/email_attachments\20250616\宜欣　生产订单模板(新封装-测试-编带)2025061606　JWS29002VDGBA_TR1_bc99283e.xls: 'OrderExcelParser' object has no attribute 'parse_excel_file'
2025-06-23 13:59:55 | ERROR    | app.services.universal_excel_parser | ❌ 解析文件失败 downloads/email_attachments\20250616\宜欣　生产订单模板(新封装-测试-编带)2025061607　JW7106-M001_ce7af3dc.xls: 'OrderExcelParser' object has no attribute 'parse_excel_file'
2025-06-23 13:59:55 | ERROR    | app.services.universal_excel_parser | ❌ 解析文件失败 downloads/email_attachments\20250616\宜欣　生产订单模板(新封装-测试-编带)2025061608　JW5116F_7bc9f479.xls: 'OrderExcelParser' object has no attribute 'parse_excel_file'
2025-06-23 13:59:55 | ERROR    | app.services.universal_excel_parser | ❌ 解析文件失败 downloads/email_attachments\20250616\宜欣　生产订单模板(新封装-测试-编带)2025061609　JWQ79818LQFPRT_TR1_a65eb211.xls: 'OrderExcelParser' object has no attribute 'parse_excel_file'
2025-06-23 13:59:55 | ERROR    | app.services.universal_excel_parser | ❌ 解析文件失败 downloads/email_attachments\20250616\宜欣　生产订单模板(新封装-测试-编带)2025061610　JW5190C-C272WQDCX_TR1_77691609.xls: 'OrderExcelParser' object has no attribute 'parse_excel_file'
2025-06-23 13:59:55 | ERROR    | app.services.universal_excel_parser | ❌ 解析文件失败 downloads/email_attachments\20250616\宜欣　生产订单模板(新封装-测试-编带)2025061611　JWH5103ASQFNAT_TR1_0cf856cd.xls: 'OrderExcelParser' object has no attribute 'parse_excel_file'
2025-06-23 13:59:55 | ERROR    | app.services.universal_excel_parser | ❌ 解析文件失败 downloads/email_attachments\20250616\宜欣　生产订单模板(新封装-测试-编带)2025061612　JWQ5103ASQFNAT-J102_TR1_dd9bb7ea.xls: 'OrderExcelParser' object has no attribute 'parse_excel_file'
2025-06-23 13:59:55 | ERROR    | app.services.universal_excel_parser | ❌ 解析文件失败 downloads/email_attachments\20250616\宜欣　生产订单模板(新封装-测试-编带)2025061614　JWM9123ECLGAF_TR1_99276dd2.xls: 'OrderExcelParser' object has no attribute 'parse_excel_file'
2025-06-23 13:59:55 | ERROR    | app.services.universal_excel_parser | ❌ 解析文件失败 downloads/email_attachments\20250616\宜欣　生产订单模板(新封装-测试-编带)2025061615　JWQ78291EWDGBB-M001_TR1_d42fd6ce.xls: 'OrderExcelParser' object has no attribute 'parse_excel_file'
2025-06-23 13:59:55 | ERROR    | app.services.universal_excel_parser | ❌ 解析文件失败 downloads/email_attachments\20250616\宜欣　生产订单模板(新封装-测试-编带)2025061616　JWQ78291EWDGBB-M001_TR1_20b0d4f2.xls: 'OrderExcelParser' object has no attribute 'parse_excel_file'
2025-06-23 13:59:55 | ERROR    | app.services.universal_excel_parser | ❌ 解析文件失败 downloads/email_attachments\20250616\宜欣　生产订单模板(新封装-测试-编带)2025061617　JW5190C2-C272WQDCX_TR1_54bb44c0.xls: 'OrderExcelParser' object has no attribute 'parse_excel_file'
2025-06-23 13:59:55 | ERROR    | app.services.universal_excel_parser | ❌ 解析文件失败 downloads/email_attachments\20250617\宜欣  生产订单模板(新封装-测试-编带)2025061701  JW5116F_e9a46ce8.xls: 'OrderExcelParser' object has no attribute 'parse_excel_file'
2025-06-23 13:59:55 | ERROR    | app.services.universal_excel_parser | ❌ 解析文件失败 downloads/email_attachments\20250617\宜欣  生产订单模板(新封装-测试-编带)2025061702  JW5190C-C272WQDCX_TR1_1321ec6e.xls: 'OrderExcelParser' object has no attribute 'parse_excel_file'
2025-06-23 13:59:55 | ERROR    | app.services.universal_excel_parser | ❌ 解析文件失败 downloads/email_attachments\20250617\宜欣  生产订单模板(新封装-测试-编带)2025061703  JW5190CWQDCX_TR1_bb8f7af0.xls: 'OrderExcelParser' object has no attribute 'parse_excel_file'
2025-06-23 13:59:55 | ERROR    | app.services.universal_excel_parser | ❌ 解析文件失败 downloads/email_attachments\20250617\宜欣  生产订单模板(新封装-测试-编带)2025061704  JWH5123S_5a645f0e.xls: 'OrderExcelParser' object has no attribute 'parse_excel_file'
2025-06-23 13:59:55 | ERROR    | app.services.universal_excel_parser | ❌ 解析文件失败 downloads/email_attachments\20250617\宜欣  生产订单模板(新封装-测试-编带)2025061705  JWQ85213-C244QFNA-SJA1_TR1_a3176400.xls: 'OrderExcelParser' object has no attribute 'parse_excel_file'
2025-06-23 13:59:55 | ERROR    | app.services.universal_excel_parser | ❌ 解析文件失败 downloads/email_attachments\20250617\宜欣  生产订单模板(新封装-测试-编带)2025061706  JWH5103ASQFNAT_TR1_cd78a46b.xls: 'OrderExcelParser' object has no attribute 'parse_excel_file'
2025-06-23 13:59:55 | ERROR    | app.services.universal_excel_parser | ❌ 解析文件失败 downloads/email_attachments\20250617\宜欣  生产订单模板(新封装-测试-编带)2025061707  JWH6374-XXXXVQFNEB_TA0_1a57daca.xls: 'OrderExcelParser' object has no attribute 'parse_excel_file'
2025-06-23 13:59:55 | ERROR    | app.services.universal_excel_parser | ❌ 解析文件失败 downloads/email_attachments\20250617\宜欣  生产订单模板(新封装-测试-编带)2025061708  JWQ52993UDHBA_TR1_cb6c6113.xls: 'OrderExcelParser' object has no attribute 'parse_excel_file'
2025-06-23 13:59:55 | ERROR    | app.services.universal_excel_parser | ❌ 解析文件失败 downloads/email_attachments\20250617\宜欣  生产订单模板(新封装-测试-编带)2025061709  JWQ5513QFNAV_TR1_8ec128fb.xls: 'OrderExcelParser' object has no attribute 'parse_excel_file'
2025-06-23 13:59:55 | ERROR    | app.services.universal_excel_parser | ❌ 解析文件失败 downloads/email_attachments\20250617\宜欣  生产订单模板(新封装-测试-编带)2025061710  LYW6119Ea5QFND-SDA1_TR1_f674d321.xls: 'OrderExcelParser' object has no attribute 'parse_excel_file'
2025-06-23 13:59:55 | ERROR    | app.services.universal_excel_parser | ❌ 解析文件失败 downloads/email_attachments\20250617\宜欣（复测）生产订单模板(新封装-测试-编带)2025061711  JWQ5103CSFQFNAT-J103_TR2_a98d209c.xls: 'OrderExcelParser' object has no attribute 'parse_excel_file'
2025-06-23 13:59:55 | ERROR    | app.services.universal_excel_parser | ❌ 解析文件失败 downloads/email_attachments\20250618\宜欣  生产订单模板(新封装-测试-编带)2025061801  JW1386VQDFA_TR1_ccb69f55.xls: 'OrderExcelParser' object has no attribute 'parse_excel_file'
2025-06-23 13:59:55 | ERROR    | app.services.universal_excel_parser | ❌ 解析文件失败 downloads/email_attachments\20250618\宜欣  生产订单模板(新封装-测试-编带)2025061802  JW7106-M001_2e2cc60a.xls: 'OrderExcelParser' object has no attribute 'parse_excel_file'
2025-06-23 13:59:55 | ERROR    | app.services.universal_excel_parser | ❌ 解析文件失败 downloads/email_attachments\20250618\宜欣  生产订单模板(新封装-测试-编带)2025061803  JWQ52992UDHBA_TR1_ede8e501.xls: 'OrderExcelParser' object has no attribute 'parse_excel_file'
2025-06-23 13:59:55 | ERROR    | app.services.universal_excel_parser | ❌ 解析文件失败 downloads/email_attachments\20250618\宜欣  生产订单模板(新封装-测试-编带)2025061805  JWH6396-XXXXWQFNWK_TA0_db799f4b.xls: 'OrderExcelParser' object has no attribute 'parse_excel_file'
2025-06-23 13:59:55 | ERROR    | app.services.universal_excel_parser | ❌ 解析文件失败 downloads/email_attachments\20250618\宜欣  生产订单模板(新封装-测试-编带)2025061806  JWQ85213-C244QFNA-SJA1_TR1_2c3dff5e.xls: 'OrderExcelParser' object has no attribute 'parse_excel_file'
2025-06-23 13:59:55 | ERROR    | app.services.universal_excel_parser | ❌ 解析文件失败 downloads/email_attachments\20250618\宜欣  生产订单模板(新封装-测试-编带)2025061807  JWH6374-XXXXVQFNEB_TA0_83d767af.xls: 'OrderExcelParser' object has no attribute 'parse_excel_file'
2025-06-23 13:59:55 | ERROR    | app.services.universal_excel_parser | ❌ 解析文件失败 downloads/email_attachments\20250618\宜欣  生产订单模板(新封装-测试-编带)2025061808  JWM9203CLLGAG_TR1_56111fe1.xls: 'OrderExcelParser' object has no attribute 'parse_excel_file'
2025-06-23 13:59:55 | ERROR    | app.services.universal_excel_parser | ❌ 解析文件失败 downloads/email_attachments\20250618\宜欣  生产订单模板(新封装-测试-编带)2025061809  JWM9203FCLLGAG_TR1_9b9fa715.xls: 'OrderExcelParser' object has no attribute 'parse_excel_file'
2025-06-23 13:59:55 | ERROR    | app.services.universal_excel_parser | ❌ 解析文件失败 downloads/email_attachments\20250618\宜欣  生产订单模板(新封装-测试-编带)2025061810  LYW6119Ea5QFND-SDA1_TR1_184fd2a7.xls: 'OrderExcelParser' object has no attribute 'parse_excel_file'
2025-06-23 13:59:55 | ERROR    | app.services.universal_excel_parser | ❌ 解析文件失败 downloads/email_attachments\20250618\宜欣  生产订单模板(新封装-测试-编带)20250618104  JWH5087AQFNAG-M001_TR1_fbabd113.xls: 'OrderExcelParser' object has no attribute 'parse_excel_file'
2025-06-23 13:59:55 | ERROR    | app.services.universal_excel_parser | ❌ 解析文件失败 downloads/email_attachments\20250618\宜欣  生产订单模板(新封装-测试-编带)2025061811  DMHV-1400-B-M001_96370af8.xls: 'OrderExcelParser' object has no attribute 'parse_excel_file'
2025-06-23 13:59:55 | ERROR    | app.services.universal_excel_parser | ❌ 解析文件失败 downloads/email_attachments\20250618\宜欣  生产订单模板(新封装-测试-编带)2025061812  JW7106_7cff3898.xls: 'OrderExcelParser' object has no attribute 'parse_excel_file'
2025-06-23 13:59:55 | ERROR    | app.services.universal_excel_parser | ❌ 解析文件失败 downloads/email_attachments\20250618\宜欣  生产订单模板(新封装-测试-编带)2025061814  JWH6396-0036WQFNWK_TR0_b9c7a931.xls: 'OrderExcelParser' object has no attribute 'parse_excel_file'
2025-06-23 13:59:55 | ERROR    | app.services.universal_excel_parser | ❌ 解析文件失败 downloads/email_attachments\20250618\宜欣  生产订单模板(新封装-测试-编带)2025061815  JWM94644B3BRW-M002_TR1_91caf44f.xls: 'OrderExcelParser' object has no attribute 'parse_excel_file'
2025-06-23 13:59:55 | ERROR    | app.services.universal_excel_parser | ❌ 解析文件失败 downloads/email_attachments\20250618\宜欣  生产订单模板(新封装-测试-编带)2025061816  JW3119E-D0201QFND_TR2_37f6e49c.xls: 'OrderExcelParser' object has no attribute 'parse_excel_file'
2025-06-23 13:59:55 | ERROR    | app.services.universal_excel_parser | ❌ 解析文件失败 downloads/email_attachments\20250618\宜欣  生产订单模板(新封装-测试-编带)202506181613  JWH6396-0038WQFNWK_TR0_2f261af1.xls: 'OrderExcelParser' object has no attribute 'parse_excel_file'
2025-06-23 13:59:55 | ERROR    | app.services.universal_excel_parser | ❌ 解析文件失败 downloads/email_attachments\20250618\宜欣(复测）生产订单模板(新封装-测试-编带)2025061814  JWQ3760EVQFNES_TR2_3e46f2c0.xls: 'OrderExcelParser' object has no attribute 'parse_excel_file'
2025-06-23 13:59:55 | ERROR    | app.services.universal_excel_parser | ❌ 解析文件失败 downloads/email_attachments\20250619\宜欣  生产订单模板(新封装-测试-编带)2025061901  JW5116F_08acf73b.xls: 'OrderExcelParser' object has no attribute 'parse_excel_file'
2025-06-23 13:59:55 | ERROR    | app.services.universal_excel_parser | ❌ 解析文件失败 downloads/email_attachments\20250619\宜欣  生产订单模板(新封装-测试-编带)2025061902  JWQ5123ESOP-J091_bbb732c7.xls: 'OrderExcelParser' object has no attribute 'parse_excel_file'
2025-06-23 13:59:55 | ERROR    | app.services.universal_excel_parser | ❌ 解析文件失败 downloads/email_attachments\20250619\宜欣  生产订单模板(新封装-测试-编带)2025061903  JW1386VQDFA_TR1_50d59d92.xls: 'OrderExcelParser' object has no attribute 'parse_excel_file'
2025-06-23 13:59:55 | ERROR    | app.services.universal_excel_parser | ❌ 解析文件失败 downloads/email_attachments\20250619\宜欣  生产订单模板(新封装-测试-编带)2025061904  JWH5123S_88e1a59f.xls: 'OrderExcelParser' object has no attribute 'parse_excel_file'
2025-06-23 13:59:55 | ERROR    | app.services.universal_excel_parser | ❌ 解析文件失败 downloads/email_attachments\20250619\宜欣  生产订单模板(新封装-测试-编带)2025061905  JWQ85213-C244QFNA-SJA1_TR1_4577d3b9.xls: 'OrderExcelParser' object has no attribute 'parse_excel_file'
2025-06-23 13:59:55 | ERROR    | app.services.universal_excel_parser | ❌ 解析文件失败 downloads/email_attachments\20250619\宜欣  生产订单模板(新封装-测试-编带)2025061906  JWH6374-XXXXVQFNEB_TA0_306e40de.xls: 'OrderExcelParser' object has no attribute 'parse_excel_file'
2025-06-23 13:59:55 | ERROR    | app.services.universal_excel_parser | ❌ 解析文件失败 downloads/email_attachments\20250619\宜欣  生产订单模板(新封装-测试-编带)2025061907  LYQ6081CEVQDFA-SJA1_TR1_f1c90086.xls: 'OrderExcelParser' object has no attribute 'parse_excel_file'
2025-06-23 13:59:56 | ERROR    | app.services.universal_excel_parser | ❌ 解析文件失败 downloads/email_attachments\20250619\宜欣  生产订单模板(新封装-测试-编带)2025061908  JWH71522HWQBEA_TR1_d121f1af.xls: 'OrderExcelParser' object has no attribute 'parse_excel_file'
2025-06-23 13:59:56 | ERROR    | app.services.universal_excel_parser | ❌ 解析文件失败 downloads/email_attachments\20250619\宜欣  生产订单模板(新封装-测试-编带)2025061909  JWH71522LWQBEA_TR1_1a1eb5f4.xls: 'OrderExcelParser' object has no attribute 'parse_excel_file'
2025-06-23 13:59:56 | ERROR    | app.services.universal_excel_parser | ❌ 解析文件失败 downloads/email_attachments\20250619\宜欣  生产订单模板(新封装-测试-编带)2025061910  WD67069TLGAA_TR1_76546ea1.xls: 'OrderExcelParser' object has no attribute 'parse_excel_file'
2025-06-23 13:59:56 | ERROR    | app.services.universal_excel_parser | ❌ 解析文件失败 downloads/email_attachments\20250619\宜欣（复测）生产订单模板(新封装-测试-编带)2025061911  BB81802SSOTB_TR1_231b58ed.xls: 'OrderExcelParser' object has no attribute 'parse_excel_file'
2025-06-23 13:59:56 | ERROR    | app.services.universal_excel_parser | ❌ 解析文件失败 downloads/email_attachments\20250620\宜欣  生产订单模板(新封装-测试-编带)2025060614  JWH71522HWQBEA_TR1_2b300030.xls: 'OrderExcelParser' object has no attribute 'parse_excel_file'
2025-06-23 13:59:56 | ERROR    | app.services.universal_excel_parser | ❌ 解析文件失败 downloads/email_attachments\20250620\宜欣  生产订单模板(新封装-测试-编带)2025060615  JWH71522HWQBEA_TR1_4e88cdba.xls: 'OrderExcelParser' object has no attribute 'parse_excel_file'
2025-06-23 13:59:56 | ERROR    | app.services.universal_excel_parser | ❌ 解析文件失败 downloads/email_attachments\20250620\宜欣  生产订单模板(新封装-测试-编带)2025061908  JWH71522HWQBEA_TR1_f5b14c58.xls: 'OrderExcelParser' object has no attribute 'parse_excel_file'
2025-06-23 13:59:56 | ERROR    | app.services.universal_excel_parser | ❌ 解析文件失败 downloads/email_attachments\20250620\宜欣  生产订单模板(新封装-测试-编带)2025062001  JW5116F_1443c4a0.xls: 'OrderExcelParser' object has no attribute 'parse_excel_file'
2025-06-23 13:59:56 | ERROR    | app.services.universal_excel_parser | ❌ 解析文件失败 downloads/email_attachments\20250620\宜欣  生产订单模板(新封装-测试-编带)2025062002  JWM94644B3BRW-M002_TR1_b3873b0f.xls: 'OrderExcelParser' object has no attribute 'parse_excel_file'
2025-06-23 13:59:56 | ERROR    | app.services.universal_excel_parser | ❌ 解析文件失败 downloads/email_attachments\20250620\宜欣  生产订单模板(新封装-测试-编带)2025062003  JW1386VQDFA_TR1_a36a6c14.xls: 'OrderExcelParser' object has no attribute 'parse_excel_file'
2025-06-23 13:59:56 | ERROR    | app.services.universal_excel_parser | ❌ 解析文件失败 downloads/email_attachments\20250620\宜欣  生产订单模板(新封装-测试-编带)2025062004  JWQ5156EVQFNFA_TR1_04d89c74.xls: 'OrderExcelParser' object has no attribute 'parse_excel_file'
2025-06-23 13:59:56 | ERROR    | app.services.universal_excel_parser | ❌ 解析文件失败 downloads/email_attachments\20250620\宜欣  生产订单模板(新封装-测试-编带)2025062005  JW9520ESOP_4b40c2c1.xls: 'OrderExcelParser' object has no attribute 'parse_excel_file'
2025-06-23 13:59:56 | ERROR    | app.services.universal_excel_parser | ❌ 解析文件失败 downloads/email_attachments\20250620\宜欣  生产订单模板(新封装-测试-编带)2025062006  JWH5583MSOP_TR1_e23728c0.xls: 'OrderExcelParser' object has no attribute 'parse_excel_file'
2025-06-23 13:59:56 | ERROR    | app.services.universal_excel_parser | ❌ 解析文件失败 downloads/email_attachments\20250620\宜欣  生产订单模板(新封装-测试-编带)2025062007  JWQ85213-C244QFNA-SJA1_TR1_532d678c.xls: 'OrderExcelParser' object has no attribute 'parse_excel_file'
2025-06-23 13:59:56 | ERROR    | app.services.universal_excel_parser | ❌ 解析文件失败 downloads/email_attachments\20250620\宜欣  生产订单模板(新封装-测试-编带)2025062008  JWQ3510SOTA_TR1_0699b84f.xls: 'OrderExcelParser' object has no attribute 'parse_excel_file'
2025-06-23 13:59:56 | ERROR    | app.services.universal_excel_parser | ❌ 解析文件失败 downloads/email_attachments\20250620\宜欣  生产订单模板(新封装-测试-编带)2025062009  JWQ950132DFNA_TR1_fbc913da.xls: 'OrderExcelParser' object has no attribute 'parse_excel_file'
2025-06-23 13:59:56 | ERROR    | app.services.universal_excel_parser | ❌ 解析文件失败 downloads/email_attachments\20250620\宜欣  生产订单模板(新封装-测试-编带)2025062010  JWS24226MSOP_TR1_0f042560.xls: 'OrderExcelParser' object has no attribute 'parse_excel_file'
2025-06-23 13:59:56 | ERROR    | app.services.universal_excel_parser | ❌ 解析文件失败 downloads/email_attachments\20250620\宜欣  生产订单模板(新封装-测试-编带)2025062011  LYW6119Ea5QFND-SDA1_TR1_becf7067.xls: 'OrderExcelParser' object has no attribute 'parse_excel_file'
2025-06-23 13:59:56 | ERROR    | app.services.universal_excel_parser | ❌ 解析文件失败 downloads/email_attachments\20250620\宜欣  生产订单模板(新封装-测试-编带)2025062012  DMHV-1400-B-M001_351038fe.xls: 'OrderExcelParser' object has no attribute 'parse_excel_file'
2025-06-23 13:59:56 | ERROR    | app.services.universal_excel_parser | ❌ 解析文件失败 downloads/email_attachments\20250620\宜欣  生产订单模板(新封装-测试-编带)2025062013  JW7106_e4481559.xls: 'OrderExcelParser' object has no attribute 'parse_excel_file'
2025-06-23 13:59:56 | ERROR    | app.services.universal_excel_parser | ❌ 解析文件失败 downloads/email_attachments\20250620\宜欣  生产订单模板(新封装-测试-编带)2025062014  JW7106-M001_9e069324.xls: 'OrderExcelParser' object has no attribute 'parse_excel_file'
2025-06-23 13:59:56 | ERROR    | app.services.universal_excel_parser | ❌ 解析文件失败 downloads/email_attachments\20250620\宜欣  生产订单模板(新封装-测试-编带)2025062015  JWH7030QFNAZ_7c04a6af.xls: 'OrderExcelParser' object has no attribute 'parse_excel_file'
2025-06-23 13:59:56 | ERROR    | app.services.universal_excel_parser | ❌ 解析文件失败 downloads/email_attachments\20250623\宜欣  生产订单模板(新封装-测试-编带)2025062006  JWH5583MSOP_TR1_2398c643.xls: 'OrderExcelParser' object has no attribute 'parse_excel_file'
2025-06-23 13:59:56 | ERROR    | app.services.universal_excel_parser | ❌ 解析文件失败 downloads/email_attachments\20250623\宜欣  生产订单模板(新封装-测试-编带)2025062301  JW1386VQDFA_TR1_ce1240e6.xls: 'OrderExcelParser' object has no attribute 'parse_excel_file'
2025-06-23 13:59:56 | ERROR    | app.services.universal_excel_parser | ❌ 解析文件失败 downloads/email_attachments\20250623\宜欣  生产订单模板(新封装-测试-编带)2025062302  JWH6374-XXXXVQFNEB_TA0_3a10f9ab.xls: 'OrderExcelParser' object has no attribute 'parse_excel_file'
2025-06-23 13:59:56 | ERROR    | app.services.universal_excel_parser | ❌ 解析文件失败 downloads/email_attachments\20250623\宜欣  生产订单模板(新封装-测试-编带)2025062303  JWQ85213-C244QFNA-SJA1_TR1_b015f6f9.xls: 'OrderExcelParser' object has no attribute 'parse_excel_file'
2025-06-23 13:59:56 | ERROR    | app.services.universal_excel_parser | ❌ 解析文件失败 downloads/email_attachments\20250623\宜欣  生产订单模板(新封装-测试-编带)2025062304  JWM94644B3BRW-M002_TR1_32512f3f.xls: 'OrderExcelParser' object has no attribute 'parse_excel_file'
2025-06-23 15:25:49 | ERROR    | app.services.summary_data_saver | CP订单批量保存失败: (pymysql.err.DataError) (1366, "Incorrect integer value: '' for column 'processing_pieces' at row 1")
[SQL: INSERT INTO cp_order_summary (processing_type, contractor_name, contractor_contact, contractor_address, contractor_phone, contractor_fax, client_name, client_contact, client_location, client_phone, client_fax, order_number, product_name, chip_name, chip_batch, processing_pieces, finished_model, wafer_numbers, cp_mapping, package_method, process_step, shipping_address, source_file, created_at, updated_at) VALUES (%(processing_type)s, %(contractor_name)s, %(contractor_contact)s, %(contractor_address)s, %(contractor_phone)s, %(contractor_fax)s, %(client_name)s, %(client_contact)s, %(client_location)s, %(client_phone)s, %(client_fax)s, %(order_number)s, %(product_name)s, %(chip_name)s, %(chip_batch)s, %(processing_pieces)s, %(finished_model)s, %(wafer_numbers)s, %(cp_mapping)s, %(package_method)s, %(process_step)s, %(shipping_address)s, %(source_file)s, %(created_at)s, %(updated_at)s)]
[parameters: {'processing_type': 'CP1', 'contractor_name': '无锡市宜欣科技有限公司', 'contractor_contact': '陈九嘉', 'contractor_address': None, 'contractor_phone': None, 'contractor_fax': None, 'client_name': None, 'client_contact': None, 'client_location': None, 'client_phone': None, 'client_fax': None, 'order_number': '签名：            （盖章）', 'product_name': '', 'chip_name': '', 'chip_batch': '', 'processing_pieces': '', 'finished_model': '', 'wafer_numbers': '填单:', 'cp_mapping': None, 'package_method': '审核:', 'process_step': '', 'shipping_address': '批准:', 'source_file': '宜欣  生产订单模板(CP)_EXCEL2025062301  JP16701C_P00R_4ba15b77.xls', 'created_at': datetime.datetime(2025, 6, 23, 7, 25, 49, 401446), 'updated_at': datetime.datetime(2025, 6, 23, 7, 25, 49, 401446)}]
(Background on this error at: https://sqlalche.me/e/20/9h9h)
2025-06-23 15:25:51 | ERROR    | app.services.summary_data_saver | CP订单批量保存失败: (pymysql.err.DataError) (1366, "Incorrect integer value: '' for column 'processing_pieces' at row 1")
[SQL: INSERT INTO cp_order_summary (processing_type, contractor_name, contractor_contact, contractor_address, contractor_phone, contractor_fax, client_name, client_contact, client_location, client_phone, client_fax, order_number, product_name, chip_name, chip_batch, processing_pieces, finished_model, wafer_numbers, cp_mapping, package_method, process_step, shipping_address, source_file, created_at, updated_at) VALUES (%(processing_type)s, %(contractor_name)s, %(contractor_contact)s, %(contractor_address)s, %(contractor_phone)s, %(contractor_fax)s, %(client_name)s, %(client_contact)s, %(client_location)s, %(client_phone)s, %(client_fax)s, %(order_number)s, %(product_name)s, %(chip_name)s, %(chip_batch)s, %(processing_pieces)s, %(finished_model)s, %(wafer_numbers)s, %(cp_mapping)s, %(package_method)s, %(process_step)s, %(shipping_address)s, %(source_file)s, %(created_at)s, %(updated_at)s)]
[parameters: {'processing_type': 'CP(全测)', 'contractor_name': '无锡市宜欣科技有限公司', 'contractor_contact': '陈九嘉', 'contractor_address': None, 'contractor_phone': None, 'contractor_fax': None, 'client_name': None, 'client_contact': None, 'client_location': None, 'client_phone': None, 'client_fax': None, 'order_number': '签名：            （盖章）', 'product_name': '', 'chip_name': '', 'chip_batch': '', 'processing_pieces': '', 'finished_model': '', 'wafer_numbers': '填单:', 'cp_mapping': None, 'package_method': '审核:', 'process_step': '', 'shipping_address': '批准:', 'source_file': '宜欣  生产订单模板(CP)_EXCEL2025061802  JP22608D_SP2_P0KR_53df9d8c.xls', 'created_at': datetime.datetime(2025, 6, 23, 7, 25, 51, 42625), 'updated_at': datetime.datetime(2025, 6, 23, 7, 25, 51, 42625)}]
(Background on this error at: https://sqlalche.me/e/20/9h9h)
2025-06-23 15:25:51 | ERROR    | app.services.summary_data_saver | CP订单批量保存失败: (pymysql.err.DataError) (1366, "Incorrect integer value: '' for column 'processing_pieces' at row 1")
[SQL: INSERT INTO cp_order_summary (processing_type, contractor_name, contractor_contact, contractor_address, contractor_phone, contractor_fax, client_name, client_contact, client_location, client_phone, client_fax, order_number, product_name, chip_name, chip_batch, processing_pieces, finished_model, wafer_numbers, cp_mapping, package_method, process_step, shipping_address, source_file, created_at, updated_at) VALUES (%(processing_type)s, %(contractor_name)s, %(contractor_contact)s, %(contractor_address)s, %(contractor_phone)s, %(contractor_fax)s, %(client_name)s, %(client_contact)s, %(client_location)s, %(client_phone)s, %(client_fax)s, %(order_number)s, %(product_name)s, %(chip_name)s, %(chip_batch)s, %(processing_pieces)s, %(finished_model)s, %(wafer_numbers)s, %(cp_mapping)s, %(package_method)s, %(process_step)s, %(shipping_address)s, %(source_file)s, %(created_at)s, %(updated_at)s)]
[parameters: {'processing_type': 'CP(全测)', 'contractor_name': '无锡市宜欣科技有限公司', 'contractor_contact': '陈九嘉', 'contractor_address': None, 'contractor_phone': None, 'contractor_fax': None, 'client_name': None, 'client_contact': None, 'client_location': None, 'client_phone': None, 'client_fax': None, 'order_number': '签名：            （盖章）', 'product_name': '', 'chip_name': '', 'chip_batch': '', 'processing_pieces': '', 'finished_model': '', 'wafer_numbers': '填单:', 'cp_mapping': None, 'package_method': '审核:', 'process_step': '', 'shipping_address': '批准:', 'source_file': '宜欣  生产订单模板(CP)_EXCEL2025061801  JP13B03L_a9d9455b.xls', 'created_at': datetime.datetime(2025, 6, 23, 7, 25, 51, 667679), 'updated_at': datetime.datetime(2025, 6, 23, 7, 25, 51, 667679)}]
(Background on this error at: https://sqlalche.me/e/20/9h9h)
2025-06-23 15:25:53 | ERROR    | app.services.summary_data_saver | CP订单批量保存失败: (pymysql.err.DataError) (1366, "Incorrect integer value: '' for column 'processing_pieces' at row 1")
[SQL: INSERT INTO cp_order_summary (processing_type, contractor_name, contractor_contact, contractor_address, contractor_phone, contractor_fax, client_name, client_contact, client_location, client_phone, client_fax, order_number, product_name, chip_name, chip_batch, processing_pieces, finished_model, wafer_numbers, cp_mapping, package_method, process_step, shipping_address, source_file, created_at, updated_at) VALUES (%(processing_type)s, %(contractor_name)s, %(contractor_contact)s, %(contractor_address)s, %(contractor_phone)s, %(contractor_fax)s, %(client_name)s, %(client_contact)s, %(client_location)s, %(client_phone)s, %(client_fax)s, %(order_number)s, %(product_name)s, %(chip_name)s, %(chip_batch)s, %(processing_pieces)s, %(finished_model)s, %(wafer_numbers)s, %(cp_mapping)s, %(package_method)s, %(process_step)s, %(shipping_address)s, %(source_file)s, %(created_at)s, %(updated_at)s)]
[parameters: {'processing_type': 'CP(全测)', 'contractor_name': '无锡市宜欣科技有限公司', 'contractor_contact': '陈九嘉', 'contractor_address': None, 'contractor_phone': None, 'contractor_fax': None, 'client_name': None, 'client_contact': None, 'client_location': None, 'client_phone': None, 'client_fax': None, 'order_number': '签名：            （盖章）', 'product_name': '', 'chip_name': '', 'chip_batch': '', 'processing_pieces': '', 'finished_model': '', 'wafer_numbers': '填单:', 'cp_mapping': None, 'package_method': '审核:', 'process_step': '', 'shipping_address': '批准:', 'source_file': '宜欣  生产订单模板(CP)_EXCEL2025061801  JP13B03L_fcdc4118.xls', 'created_at': datetime.datetime(2025, 6, 23, 7, 25, 53, 411721), 'updated_at': datetime.datetime(2025, 6, 23, 7, 25, 53, 411721)}]
(Background on this error at: https://sqlalche.me/e/20/9h9h)
2025-06-23 15:26:01 | ERROR    | app.services.summary_data_saver | CP订单批量保存失败: (pymysql.err.DataError) (1366, "Incorrect integer value: '' for column 'processing_pieces' at row 1")
[SQL: INSERT INTO cp_order_summary (processing_type, contractor_name, contractor_contact, contractor_address, contractor_phone, contractor_fax, client_name, client_contact, client_location, client_phone, client_fax, order_number, product_name, chip_name, chip_batch, processing_pieces, finished_model, wafer_numbers, cp_mapping, package_method, process_step, shipping_address, source_file, created_at, updated_at) VALUES (%(processing_type)s, %(contractor_name)s, %(contractor_contact)s, %(contractor_address)s, %(contractor_phone)s, %(contractor_fax)s, %(client_name)s, %(client_contact)s, %(client_location)s, %(client_phone)s, %(client_fax)s, %(order_number)s, %(product_name)s, %(chip_name)s, %(chip_batch)s, %(processing_pieces)s, %(finished_model)s, %(wafer_numbers)s, %(cp_mapping)s, %(package_method)s, %(process_step)s, %(shipping_address)s, %(source_file)s, %(created_at)s, %(updated_at)s)]
[parameters: {'processing_type': 'CP1', 'contractor_name': '无锡市宜欣科技有限公司', 'contractor_contact': '陈九嘉', 'contractor_address': None, 'contractor_phone': None, 'contractor_fax': None, 'client_name': None, 'client_contact': None, 'client_location': None, 'client_phone': None, 'client_fax': None, 'order_number': '签名：            （盖章）', 'product_name': '', 'chip_name': '', 'chip_batch': '', 'processing_pieces': '', 'finished_model': '', 'wafer_numbers': '填单:', 'cp_mapping': None, 'package_method': '审核:', 'process_step': '', 'shipping_address': '批准:', 'source_file': '宜欣  生产订单模板(CP)_EXCEL2025062301  JP16701C_P00R_4ba15b77.xls', 'created_at': datetime.datetime(2025, 6, 23, 7, 26, 1, 98954), 'updated_at': datetime.datetime(2025, 6, 23, 7, 26, 1, 98954)}]
(Background on this error at: https://sqlalche.me/e/20/9h9h)
2025-06-23 15:26:02 | ERROR    | app.services.summary_data_saver | CP订单批量保存失败: (pymysql.err.DataError) (1366, "Incorrect integer value: '' for column 'processing_pieces' at row 1")
[SQL: INSERT INTO cp_order_summary (processing_type, contractor_name, contractor_contact, contractor_address, contractor_phone, contractor_fax, client_name, client_contact, client_location, client_phone, client_fax, order_number, product_name, chip_name, chip_batch, processing_pieces, finished_model, wafer_numbers, cp_mapping, package_method, process_step, shipping_address, source_file, created_at, updated_at) VALUES (%(processing_type)s, %(contractor_name)s, %(contractor_contact)s, %(contractor_address)s, %(contractor_phone)s, %(contractor_fax)s, %(client_name)s, %(client_contact)s, %(client_location)s, %(client_phone)s, %(client_fax)s, %(order_number)s, %(product_name)s, %(chip_name)s, %(chip_batch)s, %(processing_pieces)s, %(finished_model)s, %(wafer_numbers)s, %(cp_mapping)s, %(package_method)s, %(process_step)s, %(shipping_address)s, %(source_file)s, %(created_at)s, %(updated_at)s)]
[parameters: {'processing_type': 'CP(全测)', 'contractor_name': '无锡市宜欣科技有限公司', 'contractor_contact': '陈九嘉', 'contractor_address': None, 'contractor_phone': None, 'contractor_fax': None, 'client_name': None, 'client_contact': None, 'client_location': None, 'client_phone': None, 'client_fax': None, 'order_number': '签名：            （盖章）', 'product_name': '', 'chip_name': '', 'chip_batch': '', 'processing_pieces': '', 'finished_model': '', 'wafer_numbers': '填单:', 'cp_mapping': None, 'package_method': '审核:', 'process_step': '', 'shipping_address': '批准:', 'source_file': '宜欣  生产订单模板(CP)_EXCEL2025061802  JP22608D_SP2_P0KR_53df9d8c.xls', 'created_at': datetime.datetime(2025, 6, 23, 7, 26, 2, 379480), 'updated_at': datetime.datetime(2025, 6, 23, 7, 26, 2, 379480)}]
(Background on this error at: https://sqlalche.me/e/20/9h9h)
2025-06-23 15:26:02 | ERROR    | app.services.summary_data_saver | CP订单批量保存失败: (pymysql.err.DataError) (1366, "Incorrect integer value: '' for column 'processing_pieces' at row 1")
[SQL: INSERT INTO cp_order_summary (processing_type, contractor_name, contractor_contact, contractor_address, contractor_phone, contractor_fax, client_name, client_contact, client_location, client_phone, client_fax, order_number, product_name, chip_name, chip_batch, processing_pieces, finished_model, wafer_numbers, cp_mapping, package_method, process_step, shipping_address, source_file, created_at, updated_at) VALUES (%(processing_type)s, %(contractor_name)s, %(contractor_contact)s, %(contractor_address)s, %(contractor_phone)s, %(contractor_fax)s, %(client_name)s, %(client_contact)s, %(client_location)s, %(client_phone)s, %(client_fax)s, %(order_number)s, %(product_name)s, %(chip_name)s, %(chip_batch)s, %(processing_pieces)s, %(finished_model)s, %(wafer_numbers)s, %(cp_mapping)s, %(package_method)s, %(process_step)s, %(shipping_address)s, %(source_file)s, %(created_at)s, %(updated_at)s)]
[parameters: {'processing_type': 'CP(全测)', 'contractor_name': '无锡市宜欣科技有限公司', 'contractor_contact': '陈九嘉', 'contractor_address': None, 'contractor_phone': None, 'contractor_fax': None, 'client_name': None, 'client_contact': None, 'client_location': None, 'client_phone': None, 'client_fax': None, 'order_number': '签名：            （盖章）', 'product_name': '', 'chip_name': '', 'chip_batch': '', 'processing_pieces': '', 'finished_model': '', 'wafer_numbers': '填单:', 'cp_mapping': None, 'package_method': '审核:', 'process_step': '', 'shipping_address': '批准:', 'source_file': '宜欣  生产订单模板(CP)_EXCEL2025061801  JP13B03L_a9d9455b.xls', 'created_at': datetime.datetime(2025, 6, 23, 7, 26, 2, 938012), 'updated_at': datetime.datetime(2025, 6, 23, 7, 26, 2, 938012)}]
(Background on this error at: https://sqlalche.me/e/20/9h9h)
2025-06-23 15:26:04 | ERROR    | app.services.summary_data_saver | CP订单批量保存失败: (pymysql.err.DataError) (1366, "Incorrect integer value: '' for column 'processing_pieces' at row 1")
[SQL: INSERT INTO cp_order_summary (processing_type, contractor_name, contractor_contact, contractor_address, contractor_phone, contractor_fax, client_name, client_contact, client_location, client_phone, client_fax, order_number, product_name, chip_name, chip_batch, processing_pieces, finished_model, wafer_numbers, cp_mapping, package_method, process_step, shipping_address, source_file, created_at, updated_at) VALUES (%(processing_type)s, %(contractor_name)s, %(contractor_contact)s, %(contractor_address)s, %(contractor_phone)s, %(contractor_fax)s, %(client_name)s, %(client_contact)s, %(client_location)s, %(client_phone)s, %(client_fax)s, %(order_number)s, %(product_name)s, %(chip_name)s, %(chip_batch)s, %(processing_pieces)s, %(finished_model)s, %(wafer_numbers)s, %(cp_mapping)s, %(package_method)s, %(process_step)s, %(shipping_address)s, %(source_file)s, %(created_at)s, %(updated_at)s)]
[parameters: {'processing_type': 'CP(全测)', 'contractor_name': '无锡市宜欣科技有限公司', 'contractor_contact': '陈九嘉', 'contractor_address': None, 'contractor_phone': None, 'contractor_fax': None, 'client_name': None, 'client_contact': None, 'client_location': None, 'client_phone': None, 'client_fax': None, 'order_number': '签名：            （盖章）', 'product_name': '', 'chip_name': '', 'chip_batch': '', 'processing_pieces': '', 'finished_model': '', 'wafer_numbers': '填单:', 'cp_mapping': None, 'package_method': '审核:', 'process_step': '', 'shipping_address': '批准:', 'source_file': '宜欣  生产订单模板(CP)_EXCEL2025061801  JP13B03L_fcdc4118.xls', 'created_at': datetime.datetime(2025, 6, 23, 7, 26, 4, 223879), 'updated_at': datetime.datetime(2025, 6, 23, 7, 26, 4, 223879)}]
(Background on this error at: https://sqlalche.me/e/20/9h9h)
2025-06-23 16:20:33 | ERROR    | app.services.summary_data_saver | CP订单批量保存失败: (pymysql.err.DataError) (1366, "Incorrect integer value: '' for column 'processing_pieces' at row 1")
[SQL: INSERT INTO cp_order_summary (processing_type, contractor_name, contractor_contact, contractor_address, contractor_phone, contractor_fax, client_name, client_contact, client_location, client_phone, client_fax, order_number, product_name, chip_name, chip_batch, processing_pieces, finished_model, wafer_numbers, cp_mapping, package_method, process_step, shipping_address, source_file, created_at, updated_at) VALUES (%(processing_type)s, %(contractor_name)s, %(contractor_contact)s, %(contractor_address)s, %(contractor_phone)s, %(contractor_fax)s, %(client_name)s, %(client_contact)s, %(client_location)s, %(client_phone)s, %(client_fax)s, %(order_number)s, %(product_name)s, %(chip_name)s, %(chip_batch)s, %(processing_pieces)s, %(finished_model)s, %(wafer_numbers)s, %(cp_mapping)s, %(package_method)s, %(process_step)s, %(shipping_address)s, %(source_file)s, %(created_at)s, %(updated_at)s)]
[parameters: {'processing_type': 'CP1', 'contractor_name': '无锡市宜欣科技有限公司', 'contractor_contact': None, 'contractor_address': None, 'contractor_phone': None, 'contractor_fax': None, 'client_name': None, 'client_contact': None, 'client_location': None, 'client_phone': None, 'client_fax': None, 'order_number': '签名：            （盖章）', 'product_name': '', 'chip_name': '', 'chip_batch': '', 'processing_pieces': '', 'finished_model': '', 'wafer_numbers': '填单:', 'cp_mapping': None, 'package_method': '审核:', 'process_step': '', 'shipping_address': '批准:', 'source_file': '宜欣  生产订单模板(CP)_EXCEL2025062301  JP16701C_P00R_4ba15b77.xls', 'created_at': datetime.datetime(2025, 6, 23, 8, 20, 33, 625074), 'updated_at': datetime.datetime(2025, 6, 23, 8, 20, 33, 625074)}]
(Background on this error at: https://sqlalche.me/e/20/9h9h)
2025-06-23 16:20:35 | ERROR    | app.services.summary_data_saver | CP订单批量保存失败: (pymysql.err.DataError) (1366, "Incorrect integer value: '' for column 'processing_pieces' at row 1")
[SQL: INSERT INTO cp_order_summary (processing_type, contractor_name, contractor_contact, contractor_address, contractor_phone, contractor_fax, client_name, client_contact, client_location, client_phone, client_fax, order_number, product_name, chip_name, chip_batch, processing_pieces, finished_model, wafer_numbers, cp_mapping, package_method, process_step, shipping_address, source_file, created_at, updated_at) VALUES (%(processing_type)s, %(contractor_name)s, %(contractor_contact)s, %(contractor_address)s, %(contractor_phone)s, %(contractor_fax)s, %(client_name)s, %(client_contact)s, %(client_location)s, %(client_phone)s, %(client_fax)s, %(order_number)s, %(product_name)s, %(chip_name)s, %(chip_batch)s, %(processing_pieces)s, %(finished_model)s, %(wafer_numbers)s, %(cp_mapping)s, %(package_method)s, %(process_step)s, %(shipping_address)s, %(source_file)s, %(created_at)s, %(updated_at)s)]
[parameters: {'processing_type': 'CP(全测)', 'contractor_name': '无锡市宜欣科技有限公司', 'contractor_contact': None, 'contractor_address': None, 'contractor_phone': None, 'contractor_fax': None, 'client_name': None, 'client_contact': None, 'client_location': None, 'client_phone': None, 'client_fax': None, 'order_number': '签名：            （盖章）', 'product_name': '', 'chip_name': '', 'chip_batch': '', 'processing_pieces': '', 'finished_model': '', 'wafer_numbers': '填单:', 'cp_mapping': None, 'package_method': '审核:', 'process_step': '', 'shipping_address': '批准:', 'source_file': '宜欣  生产订单模板(CP)_EXCEL2025061802  JP22608D_SP2_P0KR_53df9d8c.xls', 'created_at': datetime.datetime(2025, 6, 23, 8, 20, 35, 616141), 'updated_at': datetime.datetime(2025, 6, 23, 8, 20, 35, 616141)}]
(Background on this error at: https://sqlalche.me/e/20/9h9h)
2025-06-23 16:20:36 | ERROR    | app.services.summary_data_saver | CP订单批量保存失败: (pymysql.err.DataError) (1366, "Incorrect integer value: '' for column 'processing_pieces' at row 1")
[SQL: INSERT INTO cp_order_summary (processing_type, contractor_name, contractor_contact, contractor_address, contractor_phone, contractor_fax, client_name, client_contact, client_location, client_phone, client_fax, order_number, product_name, chip_name, chip_batch, processing_pieces, finished_model, wafer_numbers, cp_mapping, package_method, process_step, shipping_address, source_file, created_at, updated_at) VALUES (%(processing_type)s, %(contractor_name)s, %(contractor_contact)s, %(contractor_address)s, %(contractor_phone)s, %(contractor_fax)s, %(client_name)s, %(client_contact)s, %(client_location)s, %(client_phone)s, %(client_fax)s, %(order_number)s, %(product_name)s, %(chip_name)s, %(chip_batch)s, %(processing_pieces)s, %(finished_model)s, %(wafer_numbers)s, %(cp_mapping)s, %(package_method)s, %(process_step)s, %(shipping_address)s, %(source_file)s, %(created_at)s, %(updated_at)s)]
[parameters: {'processing_type': 'CP(全测)', 'contractor_name': '无锡市宜欣科技有限公司', 'contractor_contact': None, 'contractor_address': None, 'contractor_phone': None, 'contractor_fax': None, 'client_name': None, 'client_contact': None, 'client_location': None, 'client_phone': None, 'client_fax': None, 'order_number': '签名：            （盖章）', 'product_name': '', 'chip_name': '', 'chip_batch': '', 'processing_pieces': '', 'finished_model': '', 'wafer_numbers': '填单:', 'cp_mapping': None, 'package_method': '审核:', 'process_step': '', 'shipping_address': '批准:', 'source_file': '宜欣  生产订单模板(CP)_EXCEL2025061801  JP13B03L_a9d9455b.xls', 'created_at': datetime.datetime(2025, 6, 23, 8, 20, 36, 432439), 'updated_at': datetime.datetime(2025, 6, 23, 8, 20, 36, 432439)}]
(Background on this error at: https://sqlalche.me/e/20/9h9h)
2025-06-23 16:20:38 | ERROR    | app.services.summary_data_saver | CP订单批量保存失败: (pymysql.err.DataError) (1366, "Incorrect integer value: '' for column 'processing_pieces' at row 1")
[SQL: INSERT INTO cp_order_summary (processing_type, contractor_name, contractor_contact, contractor_address, contractor_phone, contractor_fax, client_name, client_contact, client_location, client_phone, client_fax, order_number, product_name, chip_name, chip_batch, processing_pieces, finished_model, wafer_numbers, cp_mapping, package_method, process_step, shipping_address, source_file, created_at, updated_at) VALUES (%(processing_type)s, %(contractor_name)s, %(contractor_contact)s, %(contractor_address)s, %(contractor_phone)s, %(contractor_fax)s, %(client_name)s, %(client_contact)s, %(client_location)s, %(client_phone)s, %(client_fax)s, %(order_number)s, %(product_name)s, %(chip_name)s, %(chip_batch)s, %(processing_pieces)s, %(finished_model)s, %(wafer_numbers)s, %(cp_mapping)s, %(package_method)s, %(process_step)s, %(shipping_address)s, %(source_file)s, %(created_at)s, %(updated_at)s)]
[parameters: {'processing_type': 'CP(全测)', 'contractor_name': '无锡市宜欣科技有限公司', 'contractor_contact': None, 'contractor_address': None, 'contractor_phone': None, 'contractor_fax': None, 'client_name': None, 'client_contact': None, 'client_location': None, 'client_phone': None, 'client_fax': None, 'order_number': '签名：            （盖章）', 'product_name': '', 'chip_name': '', 'chip_batch': '', 'processing_pieces': '', 'finished_model': '', 'wafer_numbers': '填单:', 'cp_mapping': None, 'package_method': '审核:', 'process_step': '', 'shipping_address': '批准:', 'source_file': '宜欣  生产订单模板(CP)_EXCEL2025061801  JP13B03L_fcdc4118.xls', 'created_at': datetime.datetime(2025, 6, 23, 8, 20, 38, 248467), 'updated_at': datetime.datetime(2025, 6, 23, 8, 20, 38, 248467)}]
(Background on this error at: https://sqlalche.me/e/20/9h9h)
2025-06-23 17:03:32 | ERROR    | app.services.summary_data_saver | CP订单批量保存失败: (pymysql.err.DataError) (1366, "Incorrect integer value: '' for column 'processing_pieces' at row 1")
[SQL: INSERT INTO cp_order_summary (processing_type, contractor_name, contractor_contact, contractor_address, contractor_phone, contractor_fax, client_name, client_contact, client_location, client_phone, client_fax, order_number, product_name, chip_name, chip_batch, processing_pieces, finished_model, wafer_numbers, cp_mapping, package_method, process_step, shipping_address, source_file, created_at, updated_at) VALUES (%(processing_type)s, %(contractor_name)s, %(contractor_contact)s, %(contractor_address)s, %(contractor_phone)s, %(contractor_fax)s, %(client_name)s, %(client_contact)s, %(client_location)s, %(client_phone)s, %(client_fax)s, %(order_number)s, %(product_name)s, %(chip_name)s, %(chip_batch)s, %(processing_pieces)s, %(finished_model)s, %(wafer_numbers)s, %(cp_mapping)s, %(package_method)s, %(process_step)s, %(shipping_address)s, %(source_file)s, %(created_at)s, %(updated_at)s)]
[parameters: {'processing_type': 'CP1', 'contractor_name': '无锡市宜欣科技有限公司', 'contractor_contact': None, 'contractor_address': None, 'contractor_phone': None, 'contractor_fax': None, 'client_name': None, 'client_contact': None, 'client_location': None, 'client_phone': None, 'client_fax': None, 'order_number': '签名：            （盖章）', 'product_name': '', 'chip_name': '', 'chip_batch': '', 'processing_pieces': '', 'finished_model': '', 'wafer_numbers': '填单:', 'cp_mapping': None, 'package_method': '审核:', 'process_step': '', 'shipping_address': '批准:', 'source_file': '宜欣  生产订单模板(CP)_EXCEL2025062301  JP16701C_P00R_4ba15b77.xls', 'created_at': datetime.datetime(2025, 6, 23, 9, 3, 32, 23135), 'updated_at': datetime.datetime(2025, 6, 23, 9, 3, 32, 23135)}]
(Background on this error at: https://sqlalche.me/e/20/9h9h)
2025-06-23 17:03:33 | ERROR    | app.services.summary_data_saver | CP订单批量保存失败: (pymysql.err.DataError) (1366, "Incorrect integer value: '' for column 'processing_pieces' at row 1")
[SQL: INSERT INTO cp_order_summary (processing_type, contractor_name, contractor_contact, contractor_address, contractor_phone, contractor_fax, client_name, client_contact, client_location, client_phone, client_fax, order_number, product_name, chip_name, chip_batch, processing_pieces, finished_model, wafer_numbers, cp_mapping, package_method, process_step, shipping_address, source_file, created_at, updated_at) VALUES (%(processing_type)s, %(contractor_name)s, %(contractor_contact)s, %(contractor_address)s, %(contractor_phone)s, %(contractor_fax)s, %(client_name)s, %(client_contact)s, %(client_location)s, %(client_phone)s, %(client_fax)s, %(order_number)s, %(product_name)s, %(chip_name)s, %(chip_batch)s, %(processing_pieces)s, %(finished_model)s, %(wafer_numbers)s, %(cp_mapping)s, %(package_method)s, %(process_step)s, %(shipping_address)s, %(source_file)s, %(created_at)s, %(updated_at)s)]
[parameters: {'processing_type': 'CP(全测)', 'contractor_name': '无锡市宜欣科技有限公司', 'contractor_contact': None, 'contractor_address': None, 'contractor_phone': None, 'contractor_fax': None, 'client_name': None, 'client_contact': None, 'client_location': None, 'client_phone': None, 'client_fax': None, 'order_number': '签名：            （盖章）', 'product_name': '', 'chip_name': '', 'chip_batch': '', 'processing_pieces': '', 'finished_model': '', 'wafer_numbers': '填单:', 'cp_mapping': None, 'package_method': '审核:', 'process_step': '', 'shipping_address': '批准:', 'source_file': '宜欣  生产订单模板(CP)_EXCEL2025061802  JP22608D_SP2_P0KR_53df9d8c.xls', 'created_at': datetime.datetime(2025, 6, 23, 9, 3, 33, 126428), 'updated_at': datetime.datetime(2025, 6, 23, 9, 3, 33, 126428)}]
(Background on this error at: https://sqlalche.me/e/20/9h9h)
2025-06-23 17:03:33 | ERROR    | app.services.summary_data_saver | CP订单批量保存失败: (pymysql.err.DataError) (1366, "Incorrect integer value: '' for column 'processing_pieces' at row 1")
[SQL: INSERT INTO cp_order_summary (processing_type, contractor_name, contractor_contact, contractor_address, contractor_phone, contractor_fax, client_name, client_contact, client_location, client_phone, client_fax, order_number, product_name, chip_name, chip_batch, processing_pieces, finished_model, wafer_numbers, cp_mapping, package_method, process_step, shipping_address, source_file, created_at, updated_at) VALUES (%(processing_type)s, %(contractor_name)s, %(contractor_contact)s, %(contractor_address)s, %(contractor_phone)s, %(contractor_fax)s, %(client_name)s, %(client_contact)s, %(client_location)s, %(client_phone)s, %(client_fax)s, %(order_number)s, %(product_name)s, %(chip_name)s, %(chip_batch)s, %(processing_pieces)s, %(finished_model)s, %(wafer_numbers)s, %(cp_mapping)s, %(package_method)s, %(process_step)s, %(shipping_address)s, %(source_file)s, %(created_at)s, %(updated_at)s)]
[parameters: {'processing_type': 'CP(全测)', 'contractor_name': '无锡市宜欣科技有限公司', 'contractor_contact': None, 'contractor_address': None, 'contractor_phone': None, 'contractor_fax': None, 'client_name': None, 'client_contact': None, 'client_location': None, 'client_phone': None, 'client_fax': None, 'order_number': '签名：            （盖章）', 'product_name': '', 'chip_name': '', 'chip_batch': '', 'processing_pieces': '', 'finished_model': '', 'wafer_numbers': '填单:', 'cp_mapping': None, 'package_method': '审核:', 'process_step': '', 'shipping_address': '批准:', 'source_file': '宜欣  生产订单模板(CP)_EXCEL2025061801  JP13B03L_a9d9455b.xls', 'created_at': datetime.datetime(2025, 6, 23, 9, 3, 33, 444522), 'updated_at': datetime.datetime(2025, 6, 23, 9, 3, 33, 444522)}]
(Background on this error at: https://sqlalche.me/e/20/9h9h)
2025-06-23 17:03:34 | ERROR    | app.services.summary_data_saver | CP订单批量保存失败: (pymysql.err.DataError) (1366, "Incorrect integer value: '' for column 'processing_pieces' at row 1")
[SQL: INSERT INTO cp_order_summary (processing_type, contractor_name, contractor_contact, contractor_address, contractor_phone, contractor_fax, client_name, client_contact, client_location, client_phone, client_fax, order_number, product_name, chip_name, chip_batch, processing_pieces, finished_model, wafer_numbers, cp_mapping, package_method, process_step, shipping_address, source_file, created_at, updated_at) VALUES (%(processing_type)s, %(contractor_name)s, %(contractor_contact)s, %(contractor_address)s, %(contractor_phone)s, %(contractor_fax)s, %(client_name)s, %(client_contact)s, %(client_location)s, %(client_phone)s, %(client_fax)s, %(order_number)s, %(product_name)s, %(chip_name)s, %(chip_batch)s, %(processing_pieces)s, %(finished_model)s, %(wafer_numbers)s, %(cp_mapping)s, %(package_method)s, %(process_step)s, %(shipping_address)s, %(source_file)s, %(created_at)s, %(updated_at)s)]
[parameters: {'processing_type': 'CP(全测)', 'contractor_name': '无锡市宜欣科技有限公司', 'contractor_contact': None, 'contractor_address': None, 'contractor_phone': None, 'contractor_fax': None, 'client_name': None, 'client_contact': None, 'client_location': None, 'client_phone': None, 'client_fax': None, 'order_number': '签名：            （盖章）', 'product_name': '', 'chip_name': '', 'chip_batch': '', 'processing_pieces': '', 'finished_model': '', 'wafer_numbers': '填单:', 'cp_mapping': None, 'package_method': '审核:', 'process_step': '', 'shipping_address': '批准:', 'source_file': '宜欣  生产订单模板(CP)_EXCEL2025061801  JP13B03L_fcdc4118.xls', 'created_at': datetime.datetime(2025, 6, 23, 9, 3, 34, 324468), 'updated_at': datetime.datetime(2025, 6, 23, 9, 3, 34, 324468)}]
(Background on this error at: https://sqlalche.me/e/20/9h9h)
2025-06-23 17:03:34 | ERROR    | app.api_v2.orders.order_data_api | 获取FT订单数据失败: 0
2025-06-23 17:04:53 | ERROR    | app.services.summary_data_saver | CP订单批量保存失败: (pymysql.err.DataError) (1366, "Incorrect integer value: '' for column 'processing_pieces' at row 1")
[SQL: INSERT INTO cp_order_summary (processing_type, contractor_name, contractor_contact, contractor_address, contractor_phone, contractor_fax, client_name, client_contact, client_location, client_phone, client_fax, order_number, product_name, chip_name, chip_batch, processing_pieces, finished_model, wafer_numbers, cp_mapping, package_method, process_step, shipping_address, source_file, created_at, updated_at) VALUES (%(processing_type)s, %(contractor_name)s, %(contractor_contact)s, %(contractor_address)s, %(contractor_phone)s, %(contractor_fax)s, %(client_name)s, %(client_contact)s, %(client_location)s, %(client_phone)s, %(client_fax)s, %(order_number)s, %(product_name)s, %(chip_name)s, %(chip_batch)s, %(processing_pieces)s, %(finished_model)s, %(wafer_numbers)s, %(cp_mapping)s, %(package_method)s, %(process_step)s, %(shipping_address)s, %(source_file)s, %(created_at)s, %(updated_at)s)]
[parameters: {'processing_type': 'CP1', 'contractor_name': '无锡市宜欣科技有限公司', 'contractor_contact': None, 'contractor_address': None, 'contractor_phone': None, 'contractor_fax': None, 'client_name': None, 'client_contact': None, 'client_location': None, 'client_phone': None, 'client_fax': None, 'order_number': '签名：            （盖章）', 'product_name': '', 'chip_name': '', 'chip_batch': '', 'processing_pieces': '', 'finished_model': '', 'wafer_numbers': '填单:', 'cp_mapping': None, 'package_method': '审核:', 'process_step': '', 'shipping_address': '批准:', 'source_file': '宜欣  生产订单模板(CP)_EXCEL2025062301  JP16701C_P00R_4ba15b77.xls', 'created_at': datetime.datetime(2025, 6, 23, 9, 4, 53, 969285), 'updated_at': datetime.datetime(2025, 6, 23, 9, 4, 53, 969285)}]
(Background on this error at: https://sqlalche.me/e/20/9h9h)
2025-06-23 17:04:54 | ERROR    | app.services.summary_data_saver | CP订单批量保存失败: (pymysql.err.DataError) (1366, "Incorrect integer value: '' for column 'processing_pieces' at row 1")
[SQL: INSERT INTO cp_order_summary (processing_type, contractor_name, contractor_contact, contractor_address, contractor_phone, contractor_fax, client_name, client_contact, client_location, client_phone, client_fax, order_number, product_name, chip_name, chip_batch, processing_pieces, finished_model, wafer_numbers, cp_mapping, package_method, process_step, shipping_address, source_file, created_at, updated_at) VALUES (%(processing_type)s, %(contractor_name)s, %(contractor_contact)s, %(contractor_address)s, %(contractor_phone)s, %(contractor_fax)s, %(client_name)s, %(client_contact)s, %(client_location)s, %(client_phone)s, %(client_fax)s, %(order_number)s, %(product_name)s, %(chip_name)s, %(chip_batch)s, %(processing_pieces)s, %(finished_model)s, %(wafer_numbers)s, %(cp_mapping)s, %(package_method)s, %(process_step)s, %(shipping_address)s, %(source_file)s, %(created_at)s, %(updated_at)s)]
[parameters: {'processing_type': 'CP(全测)', 'contractor_name': '无锡市宜欣科技有限公司', 'contractor_contact': None, 'contractor_address': None, 'contractor_phone': None, 'contractor_fax': None, 'client_name': None, 'client_contact': None, 'client_location': None, 'client_phone': None, 'client_fax': None, 'order_number': '签名：            （盖章）', 'product_name': '', 'chip_name': '', 'chip_batch': '', 'processing_pieces': '', 'finished_model': '', 'wafer_numbers': '填单:', 'cp_mapping': None, 'package_method': '审核:', 'process_step': '', 'shipping_address': '批准:', 'source_file': '宜欣  生产订单模板(CP)_EXCEL2025061802  JP22608D_SP2_P0KR_53df9d8c.xls', 'created_at': datetime.datetime(2025, 6, 23, 9, 4, 54, 939389), 'updated_at': datetime.datetime(2025, 6, 23, 9, 4, 54, 939389)}]
(Background on this error at: https://sqlalche.me/e/20/9h9h)
2025-06-23 17:04:55 | ERROR    | app.services.summary_data_saver | CP订单批量保存失败: (pymysql.err.DataError) (1366, "Incorrect integer value: '' for column 'processing_pieces' at row 1")
[SQL: INSERT INTO cp_order_summary (processing_type, contractor_name, contractor_contact, contractor_address, contractor_phone, contractor_fax, client_name, client_contact, client_location, client_phone, client_fax, order_number, product_name, chip_name, chip_batch, processing_pieces, finished_model, wafer_numbers, cp_mapping, package_method, process_step, shipping_address, source_file, created_at, updated_at) VALUES (%(processing_type)s, %(contractor_name)s, %(contractor_contact)s, %(contractor_address)s, %(contractor_phone)s, %(contractor_fax)s, %(client_name)s, %(client_contact)s, %(client_location)s, %(client_phone)s, %(client_fax)s, %(order_number)s, %(product_name)s, %(chip_name)s, %(chip_batch)s, %(processing_pieces)s, %(finished_model)s, %(wafer_numbers)s, %(cp_mapping)s, %(package_method)s, %(process_step)s, %(shipping_address)s, %(source_file)s, %(created_at)s, %(updated_at)s)]
[parameters: {'processing_type': 'CP(全测)', 'contractor_name': '无锡市宜欣科技有限公司', 'contractor_contact': None, 'contractor_address': None, 'contractor_phone': None, 'contractor_fax': None, 'client_name': None, 'client_contact': None, 'client_location': None, 'client_phone': None, 'client_fax': None, 'order_number': '签名：            （盖章）', 'product_name': '', 'chip_name': '', 'chip_batch': '', 'processing_pieces': '', 'finished_model': '', 'wafer_numbers': '填单:', 'cp_mapping': None, 'package_method': '审核:', 'process_step': '', 'shipping_address': '批准:', 'source_file': '宜欣  生产订单模板(CP)_EXCEL2025061801  JP13B03L_a9d9455b.xls', 'created_at': datetime.datetime(2025, 6, 23, 9, 4, 55, 223172), 'updated_at': datetime.datetime(2025, 6, 23, 9, 4, 55, 223172)}]
(Background on this error at: https://sqlalche.me/e/20/9h9h)
2025-06-23 17:04:56 | ERROR    | app.services.summary_data_saver | CP订单批量保存失败: (pymysql.err.DataError) (1366, "Incorrect integer value: '' for column 'processing_pieces' at row 1")
[SQL: INSERT INTO cp_order_summary (processing_type, contractor_name, contractor_contact, contractor_address, contractor_phone, contractor_fax, client_name, client_contact, client_location, client_phone, client_fax, order_number, product_name, chip_name, chip_batch, processing_pieces, finished_model, wafer_numbers, cp_mapping, package_method, process_step, shipping_address, source_file, created_at, updated_at) VALUES (%(processing_type)s, %(contractor_name)s, %(contractor_contact)s, %(contractor_address)s, %(contractor_phone)s, %(contractor_fax)s, %(client_name)s, %(client_contact)s, %(client_location)s, %(client_phone)s, %(client_fax)s, %(order_number)s, %(product_name)s, %(chip_name)s, %(chip_batch)s, %(processing_pieces)s, %(finished_model)s, %(wafer_numbers)s, %(cp_mapping)s, %(package_method)s, %(process_step)s, %(shipping_address)s, %(source_file)s, %(created_at)s, %(updated_at)s)]
[parameters: {'processing_type': 'CP(全测)', 'contractor_name': '无锡市宜欣科技有限公司', 'contractor_contact': None, 'contractor_address': None, 'contractor_phone': None, 'contractor_fax': None, 'client_name': None, 'client_contact': None, 'client_location': None, 'client_phone': None, 'client_fax': None, 'order_number': '签名：            （盖章）', 'product_name': '', 'chip_name': '', 'chip_batch': '', 'processing_pieces': '', 'finished_model': '', 'wafer_numbers': '填单:', 'cp_mapping': None, 'package_method': '审核:', 'process_step': '', 'shipping_address': '批准:', 'source_file': '宜欣  生产订单模板(CP)_EXCEL2025061801  JP13B03L_fcdc4118.xls', 'created_at': datetime.datetime(2025, 6, 23, 9, 4, 56, 117111), 'updated_at': datetime.datetime(2025, 6, 23, 9, 4, 56, 117111)}]
(Background on this error at: https://sqlalche.me/e/20/9h9h)
2025-06-23 17:04:56 | ERROR    | app.api_v2.orders.order_data_api | 获取FT订单数据失败: 0
2025-06-23 17:05:00 | ERROR    | app.api_v2.orders.order_data_api | 获取CP订单数据失败: 0
2025-06-23 17:05:01 | ERROR    | app.api_v2.orders.order_data_api | 获取FT订单数据失败: 0
2025-06-23 17:05:15 | ERROR    | app.api_v2.orders.order_data_api | 获取FT订单数据失败: 0
2025-06-23 17:11:33 | ERROR    | app.api_v2.orders.order_data_api | 获取FT订单数据失败: 0
2025-06-23 17:11:33 | ERROR    | app.api_v2.orders.order_data_api | 获取CP订单数据失败: 0
2025-06-23 17:12:02 | ERROR    | app.api_v2.orders.order_data_api | 获取FT订单数据失败: 0
2025-06-23 17:12:02 | ERROR    | app.api_v2.orders.order_data_api | 获取CP订单数据失败: 0
2025-06-23 17:15:16 | ERROR    | app.api_v2.orders.order_data_api | 获取FT订单数据失败: 0
2025-06-23 17:15:16 | ERROR    | app.api_v2.orders.order_data_api | 获取CP订单数据失败: 0
2025-06-23 17:16:04 | ERROR    | app.api_v2.orders.order_data_api | 获取FT订单数据失败: 0
2025-06-23 17:16:04 | ERROR    | app.api_v2.orders.order_data_api | 获取CP订单数据失败: 0
2025-06-23 17:17:55 | ERROR    | app.services.summary_data_saver | CP订单批量保存失败: (pymysql.err.DataError) (1366, "Incorrect integer value: '' for column 'processing_pieces' at row 1")
[SQL: INSERT INTO cp_order_summary (processing_type, contractor_name, contractor_contact, contractor_address, contractor_phone, contractor_fax, client_name, client_contact, client_location, client_phone, client_fax, order_number, product_name, chip_name, chip_batch, processing_pieces, finished_model, wafer_numbers, cp_mapping, package_method, process_step, shipping_address, source_file, created_at, updated_at) VALUES (%(processing_type)s, %(contractor_name)s, %(contractor_contact)s, %(contractor_address)s, %(contractor_phone)s, %(contractor_fax)s, %(client_name)s, %(client_contact)s, %(client_location)s, %(client_phone)s, %(client_fax)s, %(order_number)s, %(product_name)s, %(chip_name)s, %(chip_batch)s, %(processing_pieces)s, %(finished_model)s, %(wafer_numbers)s, %(cp_mapping)s, %(package_method)s, %(process_step)s, %(shipping_address)s, %(source_file)s, %(created_at)s, %(updated_at)s)]
[parameters: {'processing_type': 'CP1', 'contractor_name': '无锡市宜欣科技有限公司', 'contractor_contact': None, 'contractor_address': None, 'contractor_phone': None, 'contractor_fax': None, 'client_name': None, 'client_contact': None, 'client_location': None, 'client_phone': None, 'client_fax': None, 'order_number': '签名：            （盖章）', 'product_name': '', 'chip_name': '', 'chip_batch': '', 'processing_pieces': '', 'finished_model': '', 'wafer_numbers': '填单:', 'cp_mapping': None, 'package_method': '审核:', 'process_step': '', 'shipping_address': '批准:', 'source_file': '宜欣  生产订单模板(CP)_EXCEL2025062301  JP16701C_P00R_4ba15b77.xls', 'created_at': datetime.datetime(2025, 6, 23, 9, 17, 55, 784890), 'updated_at': datetime.datetime(2025, 6, 23, 9, 17, 55, 784890)}]
(Background on this error at: https://sqlalche.me/e/20/9h9h)
2025-06-23 17:17:57 | ERROR    | app.services.summary_data_saver | CP订单批量保存失败: (pymysql.err.DataError) (1366, "Incorrect integer value: '' for column 'processing_pieces' at row 1")
[SQL: INSERT INTO cp_order_summary (processing_type, contractor_name, contractor_contact, contractor_address, contractor_phone, contractor_fax, client_name, client_contact, client_location, client_phone, client_fax, order_number, product_name, chip_name, chip_batch, processing_pieces, finished_model, wafer_numbers, cp_mapping, package_method, process_step, shipping_address, source_file, created_at, updated_at) VALUES (%(processing_type)s, %(contractor_name)s, %(contractor_contact)s, %(contractor_address)s, %(contractor_phone)s, %(contractor_fax)s, %(client_name)s, %(client_contact)s, %(client_location)s, %(client_phone)s, %(client_fax)s, %(order_number)s, %(product_name)s, %(chip_name)s, %(chip_batch)s, %(processing_pieces)s, %(finished_model)s, %(wafer_numbers)s, %(cp_mapping)s, %(package_method)s, %(process_step)s, %(shipping_address)s, %(source_file)s, %(created_at)s, %(updated_at)s)]
[parameters: {'processing_type': 'CP(全测)', 'contractor_name': '无锡市宜欣科技有限公司', 'contractor_contact': None, 'contractor_address': None, 'contractor_phone': None, 'contractor_fax': None, 'client_name': None, 'client_contact': None, 'client_location': None, 'client_phone': None, 'client_fax': None, 'order_number': '签名：            （盖章）', 'product_name': '', 'chip_name': '', 'chip_batch': '', 'processing_pieces': '', 'finished_model': '', 'wafer_numbers': '填单:', 'cp_mapping': None, 'package_method': '审核:', 'process_step': '', 'shipping_address': '批准:', 'source_file': '宜欣  生产订单模板(CP)_EXCEL2025061802  JP22608D_SP2_P0KR_53df9d8c.xls', 'created_at': datetime.datetime(2025, 6, 23, 9, 17, 57, 466179), 'updated_at': datetime.datetime(2025, 6, 23, 9, 17, 57, 466179)}]
(Background on this error at: https://sqlalche.me/e/20/9h9h)
2025-06-23 17:17:58 | ERROR    | app.services.summary_data_saver | CP订单批量保存失败: (pymysql.err.DataError) (1366, "Incorrect integer value: '' for column 'processing_pieces' at row 1")
[SQL: INSERT INTO cp_order_summary (processing_type, contractor_name, contractor_contact, contractor_address, contractor_phone, contractor_fax, client_name, client_contact, client_location, client_phone, client_fax, order_number, product_name, chip_name, chip_batch, processing_pieces, finished_model, wafer_numbers, cp_mapping, package_method, process_step, shipping_address, source_file, created_at, updated_at) VALUES (%(processing_type)s, %(contractor_name)s, %(contractor_contact)s, %(contractor_address)s, %(contractor_phone)s, %(contractor_fax)s, %(client_name)s, %(client_contact)s, %(client_location)s, %(client_phone)s, %(client_fax)s, %(order_number)s, %(product_name)s, %(chip_name)s, %(chip_batch)s, %(processing_pieces)s, %(finished_model)s, %(wafer_numbers)s, %(cp_mapping)s, %(package_method)s, %(process_step)s, %(shipping_address)s, %(source_file)s, %(created_at)s, %(updated_at)s)]
[parameters: {'processing_type': 'CP(全测)', 'contractor_name': '无锡市宜欣科技有限公司', 'contractor_contact': None, 'contractor_address': None, 'contractor_phone': None, 'contractor_fax': None, 'client_name': None, 'client_contact': None, 'client_location': None, 'client_phone': None, 'client_fax': None, 'order_number': '签名：            （盖章）', 'product_name': '', 'chip_name': '', 'chip_batch': '', 'processing_pieces': '', 'finished_model': '', 'wafer_numbers': '填单:', 'cp_mapping': None, 'package_method': '审核:', 'process_step': '', 'shipping_address': '批准:', 'source_file': '宜欣  生产订单模板(CP)_EXCEL2025061801  JP13B03L_a9d9455b.xls', 'created_at': datetime.datetime(2025, 6, 23, 9, 17, 58, 84214), 'updated_at': datetime.datetime(2025, 6, 23, 9, 17, 58, 84214)}]
(Background on this error at: https://sqlalche.me/e/20/9h9h)
2025-06-23 17:17:59 | ERROR    | app.services.summary_data_saver | CP订单批量保存失败: (pymysql.err.DataError) (1366, "Incorrect integer value: '' for column 'processing_pieces' at row 1")
[SQL: INSERT INTO cp_order_summary (processing_type, contractor_name, contractor_contact, contractor_address, contractor_phone, contractor_fax, client_name, client_contact, client_location, client_phone, client_fax, order_number, product_name, chip_name, chip_batch, processing_pieces, finished_model, wafer_numbers, cp_mapping, package_method, process_step, shipping_address, source_file, created_at, updated_at) VALUES (%(processing_type)s, %(contractor_name)s, %(contractor_contact)s, %(contractor_address)s, %(contractor_phone)s, %(contractor_fax)s, %(client_name)s, %(client_contact)s, %(client_location)s, %(client_phone)s, %(client_fax)s, %(order_number)s, %(product_name)s, %(chip_name)s, %(chip_batch)s, %(processing_pieces)s, %(finished_model)s, %(wafer_numbers)s, %(cp_mapping)s, %(package_method)s, %(process_step)s, %(shipping_address)s, %(source_file)s, %(created_at)s, %(updated_at)s)]
[parameters: {'processing_type': 'CP(全测)', 'contractor_name': '无锡市宜欣科技有限公司', 'contractor_contact': None, 'contractor_address': None, 'contractor_phone': None, 'contractor_fax': None, 'client_name': None, 'client_contact': None, 'client_location': None, 'client_phone': None, 'client_fax': None, 'order_number': '签名：            （盖章）', 'product_name': '', 'chip_name': '', 'chip_batch': '', 'processing_pieces': '', 'finished_model': '', 'wafer_numbers': '填单:', 'cp_mapping': None, 'package_method': '审核:', 'process_step': '', 'shipping_address': '批准:', 'source_file': '宜欣  生产订单模板(CP)_EXCEL2025061801  JP13B03L_fcdc4118.xls', 'created_at': datetime.datetime(2025, 6, 23, 9, 17, 59, 600926), 'updated_at': datetime.datetime(2025, 6, 23, 9, 17, 59, 600926)}]
(Background on this error at: https://sqlalche.me/e/20/9h9h)
2025-06-23 17:44:18 | ERROR    | app.services.summary_data_saver | CP订单批量保存失败: (pymysql.err.DataError) (1366, "Incorrect integer value: '' for column 'processing_pieces' at row 1")
[SQL: INSERT INTO cp_order_summary (processing_type, contractor_name, contractor_contact, contractor_address, contractor_phone, contractor_fax, client_name, client_contact, client_location, client_phone, client_fax, order_number, product_name, chip_name, chip_batch, processing_pieces, finished_model, wafer_numbers, cp_mapping, package_method, process_step, shipping_address, source_file, created_at, updated_at) VALUES (%(processing_type)s, %(contractor_name)s, %(contractor_contact)s, %(contractor_address)s, %(contractor_phone)s, %(contractor_fax)s, %(client_name)s, %(client_contact)s, %(client_location)s, %(client_phone)s, %(client_fax)s, %(order_number)s, %(product_name)s, %(chip_name)s, %(chip_batch)s, %(processing_pieces)s, %(finished_model)s, %(wafer_numbers)s, %(cp_mapping)s, %(package_method)s, %(process_step)s, %(shipping_address)s, %(source_file)s, %(created_at)s, %(updated_at)s)]
[parameters: {'processing_type': 'CP1', 'contractor_name': '无锡市宜欣科技有限公司', 'contractor_contact': None, 'contractor_address': None, 'contractor_phone': None, 'contractor_fax': None, 'client_name': None, 'client_contact': None, 'client_location': None, 'client_phone': None, 'client_fax': None, 'order_number': '签名：            （盖章）', 'product_name': '', 'chip_name': '', 'chip_batch': '', 'processing_pieces': '', 'finished_model': '', 'wafer_numbers': '填单:', 'cp_mapping': None, 'package_method': '审核:', 'process_step': '', 'shipping_address': '批准:', 'source_file': '宜欣  生产订单模板(CP)_EXCEL2025062301  JP16701C_P00R_4ba15b77.xls', 'created_at': datetime.datetime(2025, 6, 23, 9, 44, 18, 825559), 'updated_at': datetime.datetime(2025, 6, 23, 9, 44, 18, 825559)}]
(Background on this error at: https://sqlalche.me/e/20/9h9h)
2025-06-23 17:44:19 | ERROR    | app.services.summary_data_saver | CP订单批量保存失败: (pymysql.err.DataError) (1366, "Incorrect integer value: '' for column 'processing_pieces' at row 1")
[SQL: INSERT INTO cp_order_summary (processing_type, contractor_name, contractor_contact, contractor_address, contractor_phone, contractor_fax, client_name, client_contact, client_location, client_phone, client_fax, order_number, product_name, chip_name, chip_batch, processing_pieces, finished_model, wafer_numbers, cp_mapping, package_method, process_step, shipping_address, source_file, created_at, updated_at) VALUES (%(processing_type)s, %(contractor_name)s, %(contractor_contact)s, %(contractor_address)s, %(contractor_phone)s, %(contractor_fax)s, %(client_name)s, %(client_contact)s, %(client_location)s, %(client_phone)s, %(client_fax)s, %(order_number)s, %(product_name)s, %(chip_name)s, %(chip_batch)s, %(processing_pieces)s, %(finished_model)s, %(wafer_numbers)s, %(cp_mapping)s, %(package_method)s, %(process_step)s, %(shipping_address)s, %(source_file)s, %(created_at)s, %(updated_at)s)]
[parameters: {'processing_type': 'CP(全测)', 'contractor_name': '无锡市宜欣科技有限公司', 'contractor_contact': None, 'contractor_address': None, 'contractor_phone': None, 'contractor_fax': None, 'client_name': None, 'client_contact': None, 'client_location': None, 'client_phone': None, 'client_fax': None, 'order_number': '签名：            （盖章）', 'product_name': '', 'chip_name': '', 'chip_batch': '', 'processing_pieces': '', 'finished_model': '', 'wafer_numbers': '填单:', 'cp_mapping': None, 'package_method': '审核:', 'process_step': '', 'shipping_address': '批准:', 'source_file': '宜欣  生产订单模板(CP)_EXCEL2025061802  JP22608D_SP2_P0KR_53df9d8c.xls', 'created_at': datetime.datetime(2025, 6, 23, 9, 44, 19, 396767), 'updated_at': datetime.datetime(2025, 6, 23, 9, 44, 19, 396767)}]
(Background on this error at: https://sqlalche.me/e/20/9h9h)
2025-06-23 17:44:19 | ERROR    | app.services.summary_data_saver | CP订单批量保存失败: (pymysql.err.DataError) (1366, "Incorrect integer value: '' for column 'processing_pieces' at row 1")
[SQL: INSERT INTO cp_order_summary (processing_type, contractor_name, contractor_contact, contractor_address, contractor_phone, contractor_fax, client_name, client_contact, client_location, client_phone, client_fax, order_number, product_name, chip_name, chip_batch, processing_pieces, finished_model, wafer_numbers, cp_mapping, package_method, process_step, shipping_address, source_file, created_at, updated_at) VALUES (%(processing_type)s, %(contractor_name)s, %(contractor_contact)s, %(contractor_address)s, %(contractor_phone)s, %(contractor_fax)s, %(client_name)s, %(client_contact)s, %(client_location)s, %(client_phone)s, %(client_fax)s, %(order_number)s, %(product_name)s, %(chip_name)s, %(chip_batch)s, %(processing_pieces)s, %(finished_model)s, %(wafer_numbers)s, %(cp_mapping)s, %(package_method)s, %(process_step)s, %(shipping_address)s, %(source_file)s, %(created_at)s, %(updated_at)s)]
[parameters: {'processing_type': 'CP(全测)', 'contractor_name': '无锡市宜欣科技有限公司', 'contractor_contact': None, 'contractor_address': None, 'contractor_phone': None, 'contractor_fax': None, 'client_name': None, 'client_contact': None, 'client_location': None, 'client_phone': None, 'client_fax': None, 'order_number': '签名：            （盖章）', 'product_name': '', 'chip_name': '', 'chip_batch': '', 'processing_pieces': '', 'finished_model': '', 'wafer_numbers': '填单:', 'cp_mapping': None, 'package_method': '审核:', 'process_step': '', 'shipping_address': '批准:', 'source_file': '宜欣  生产订单模板(CP)_EXCEL2025061801  JP13B03L_a9d9455b.xls', 'created_at': datetime.datetime(2025, 6, 23, 9, 44, 19, 594312), 'updated_at': datetime.datetime(2025, 6, 23, 9, 44, 19, 594312)}]
(Background on this error at: https://sqlalche.me/e/20/9h9h)
2025-06-23 17:44:20 | ERROR    | app.services.summary_data_saver | CP订单批量保存失败: (pymysql.err.DataError) (1366, "Incorrect integer value: '' for column 'processing_pieces' at row 1")
[SQL: INSERT INTO cp_order_summary (processing_type, contractor_name, contractor_contact, contractor_address, contractor_phone, contractor_fax, client_name, client_contact, client_location, client_phone, client_fax, order_number, product_name, chip_name, chip_batch, processing_pieces, finished_model, wafer_numbers, cp_mapping, package_method, process_step, shipping_address, source_file, created_at, updated_at) VALUES (%(processing_type)s, %(contractor_name)s, %(contractor_contact)s, %(contractor_address)s, %(contractor_phone)s, %(contractor_fax)s, %(client_name)s, %(client_contact)s, %(client_location)s, %(client_phone)s, %(client_fax)s, %(order_number)s, %(product_name)s, %(chip_name)s, %(chip_batch)s, %(processing_pieces)s, %(finished_model)s, %(wafer_numbers)s, %(cp_mapping)s, %(package_method)s, %(process_step)s, %(shipping_address)s, %(source_file)s, %(created_at)s, %(updated_at)s)]
[parameters: {'processing_type': 'CP(全测)', 'contractor_name': '无锡市宜欣科技有限公司', 'contractor_contact': None, 'contractor_address': None, 'contractor_phone': None, 'contractor_fax': None, 'client_name': None, 'client_contact': None, 'client_location': None, 'client_phone': None, 'client_fax': None, 'order_number': '签名：            （盖章）', 'product_name': '', 'chip_name': '', 'chip_batch': '', 'processing_pieces': '', 'finished_model': '', 'wafer_numbers': '填单:', 'cp_mapping': None, 'package_method': '审核:', 'process_step': '', 'shipping_address': '批准:', 'source_file': '宜欣  生产订单模板(CP)_EXCEL2025061801  JP13B03L_fcdc4118.xls', 'created_at': datetime.datetime(2025, 6, 23, 9, 44, 20, 42379), 'updated_at': datetime.datetime(2025, 6, 23, 9, 44, 20, 42379)}]
(Background on this error at: https://sqlalche.me/e/20/9h9h)
2025-06-23 20:57:33 | ERROR    | app.services.data_source_manager | MySQL获取已排产批次数据失败: (1054, "Unknown column 'scheduled_time' in 'order clause'")
2025-06-24 10:38:38 | ERROR    | app | 获取状态失败: (pymysql.err.ProgrammingError) (1146, "Table 'aps.cp_order_data' doesn't exist")
[SQL: SELECT count(*) AS count_1 
FROM (SELECT cp_order_data.id AS cp_order_data_id, cp_order_data.document_number AS cp_order_data_document_number, cp_order_data.processing_type AS cp_order_data_processing_type, cp_order_data.order_date AS cp_order_data_order_date, cp_order_data.contractor_name AS cp_order_data_contractor_name, cp_order_data.contractor_contact AS cp_order_data_contractor_contact, cp_order_data.contractor_address AS cp_order_data_contractor_address, cp_order_data.contractor_phone AS cp_order_data_contractor_phone, cp_order_data.client_name AS cp_order_data_client_name, cp_order_data.client_contact AS cp_order_data_client_contact, cp_order_data.client_address AS cp_order_data_client_address, cp_order_data.client_phone AS cp_order_data_client_phone, cp_order_data.order_number AS cp_order_data_order_number, cp_order_data.product_name AS cp_order_data_product_name, cp_order_data.chip_name AS cp_order_data_chip_name, cp_order_data.chip_batch AS cp_order_data_chip_batch, cp_order_data.processing_pieces AS cp_order_data_processing_pieces, cp_order_data.finished_model AS cp_order_data_finished_model, cp_order_data.wafer_numbers AS cp_order_data_wafer_numbers, cp_order_data.cp_mapping AS cp_order_data_cp_mapping, cp_order_data.package_method AS cp_order_data_package_method, cp_order_data.process_step AS cp_order_data_process_step, cp_order_data.shipping_address AS cp_order_data_shipping_address, cp_order_data.classification AS cp_order_data_classification, cp_order_data.wafer_count AS cp_order_data_wafer_count, cp_order_data.customer AS cp_order_data_customer, cp_order_data.product_code AS cp_order_data_product_code, cp_order_data.quantity AS cp_order_data_quantity, cp_order_data.unit_price AS cp_order_data_unit_price, cp_order_data.total_price AS cp_order_data_total_price, cp_order_data.status AS cp_order_data_status, cp_order_data.urgent AS cp_order_data_urgent, cp_order_data.owner AS cp_order_data_owner, cp_order_data.note AS cp_order_data_note, cp_order_data.source_file AS cp_order_data_source_file, cp_order_data.raw_data AS cp_order_data_raw_data, cp_order_data.horizontal_data AS cp_order_data_horizontal_data, cp_order_data.vertical_data AS cp_order_data_vertical_data, cp_order_data.data_row_number AS cp_order_data_data_row_number, cp_order_data.extraction_method AS cp_order_data_extraction_method, cp_order_data.extraction_info AS cp_order_data_extraction_info, cp_order_data.created_by AS cp_order_data_created_by, cp_order_data.created_at AS cp_order_data_created_at, cp_order_data.updated_at AS cp_order_data_updated_at, cp_order_data.imported_at AS cp_order_data_imported_at, cp_order_data.processed_at AS cp_order_data_processed_at 
FROM cp_order_data) AS anon_1]
(Background on this error at: https://sqlalche.me/e/20/f405)
2025-06-24 10:40:56 | ERROR    | app | 获取状态失败: (pymysql.err.ProgrammingError) (1146, "Table 'aps.cp_order_data' doesn't exist")
[SQL: SELECT count(*) AS count_1 
FROM (SELECT cp_order_data.id AS cp_order_data_id, cp_order_data.document_number AS cp_order_data_document_number, cp_order_data.processing_type AS cp_order_data_processing_type, cp_order_data.order_date AS cp_order_data_order_date, cp_order_data.contractor_name AS cp_order_data_contractor_name, cp_order_data.contractor_contact AS cp_order_data_contractor_contact, cp_order_data.contractor_address AS cp_order_data_contractor_address, cp_order_data.contractor_phone AS cp_order_data_contractor_phone, cp_order_data.client_name AS cp_order_data_client_name, cp_order_data.client_contact AS cp_order_data_client_contact, cp_order_data.client_address AS cp_order_data_client_address, cp_order_data.client_phone AS cp_order_data_client_phone, cp_order_data.order_number AS cp_order_data_order_number, cp_order_data.product_name AS cp_order_data_product_name, cp_order_data.chip_name AS cp_order_data_chip_name, cp_order_data.chip_batch AS cp_order_data_chip_batch, cp_order_data.processing_pieces AS cp_order_data_processing_pieces, cp_order_data.finished_model AS cp_order_data_finished_model, cp_order_data.wafer_numbers AS cp_order_data_wafer_numbers, cp_order_data.cp_mapping AS cp_order_data_cp_mapping, cp_order_data.package_method AS cp_order_data_package_method, cp_order_data.process_step AS cp_order_data_process_step, cp_order_data.shipping_address AS cp_order_data_shipping_address, cp_order_data.classification AS cp_order_data_classification, cp_order_data.wafer_count AS cp_order_data_wafer_count, cp_order_data.customer AS cp_order_data_customer, cp_order_data.product_code AS cp_order_data_product_code, cp_order_data.quantity AS cp_order_data_quantity, cp_order_data.unit_price AS cp_order_data_unit_price, cp_order_data.total_price AS cp_order_data_total_price, cp_order_data.status AS cp_order_data_status, cp_order_data.urgent AS cp_order_data_urgent, cp_order_data.owner AS cp_order_data_owner, cp_order_data.note AS cp_order_data_note, cp_order_data.source_file AS cp_order_data_source_file, cp_order_data.raw_data AS cp_order_data_raw_data, cp_order_data.horizontal_data AS cp_order_data_horizontal_data, cp_order_data.vertical_data AS cp_order_data_vertical_data, cp_order_data.data_row_number AS cp_order_data_data_row_number, cp_order_data.extraction_method AS cp_order_data_extraction_method, cp_order_data.extraction_info AS cp_order_data_extraction_info, cp_order_data.created_by AS cp_order_data_created_by, cp_order_data.created_at AS cp_order_data_created_at, cp_order_data.updated_at AS cp_order_data_updated_at, cp_order_data.imported_at AS cp_order_data_imported_at, cp_order_data.processed_at AS cp_order_data_processed_at 
FROM cp_order_data) AS anon_1]
(Background on this error at: https://sqlalche.me/e/20/f405)
2025-06-24 10:47:42 | ERROR    | app.services.summary_data_saver | CP订单批量保存失败: (pymysql.err.DataError) (1366, "Incorrect integer value: '' for column 'processing_pieces' at row 1")
[SQL: INSERT INTO cp_order_summary (processing_type, contractor_name, contractor_contact, contractor_address, contractor_phone, contractor_fax, client_name, client_contact, client_location, client_phone, client_fax, order_number, product_name, chip_name, chip_batch, processing_pieces, finished_model, wafer_numbers, cp_mapping, package_method, process_step, shipping_address, source_file, created_at, updated_at) VALUES (%(processing_type)s, %(contractor_name)s, %(contractor_contact)s, %(contractor_address)s, %(contractor_phone)s, %(contractor_fax)s, %(client_name)s, %(client_contact)s, %(client_location)s, %(client_phone)s, %(client_fax)s, %(order_number)s, %(product_name)s, %(chip_name)s, %(chip_batch)s, %(processing_pieces)s, %(finished_model)s, %(wafer_numbers)s, %(cp_mapping)s, %(package_method)s, %(process_step)s, %(shipping_address)s, %(source_file)s, %(created_at)s, %(updated_at)s)]
[parameters: {'processing_type': 'CP1', 'contractor_name': '无锡市宜欣科技有限公司', 'contractor_contact': None, 'contractor_address': None, 'contractor_phone': None, 'contractor_fax': None, 'client_name': None, 'client_contact': None, 'client_location': None, 'client_phone': None, 'client_fax': None, 'order_number': '签名：            （盖章）', 'product_name': '', 'chip_name': '', 'chip_batch': '', 'processing_pieces': '', 'finished_model': '', 'wafer_numbers': '填单:', 'cp_mapping': None, 'package_method': '审核:', 'process_step': '', 'shipping_address': '批准:', 'source_file': '宜欣  生产订单模板(CP)_EXCEL2025062301  JP16701C_P00R_4ba15b77.xls', 'created_at': datetime.datetime(2025, 6, 24, 2, 47, 42, 154620), 'updated_at': datetime.datetime(2025, 6, 24, 2, 47, 42, 154620)}]
(Background on this error at: https://sqlalche.me/e/20/9h9h)
2025-06-24 10:47:42 | ERROR    | app.services.summary_data_saver | CP订单批量保存失败: (pymysql.err.DataError) (1366, "Incorrect integer value: '' for column 'processing_pieces' at row 1")
[SQL: INSERT INTO cp_order_summary (processing_type, contractor_name, contractor_contact, contractor_address, contractor_phone, contractor_fax, client_name, client_contact, client_location, client_phone, client_fax, order_number, product_name, chip_name, chip_batch, processing_pieces, finished_model, wafer_numbers, cp_mapping, package_method, process_step, shipping_address, source_file, created_at, updated_at) VALUES (%(processing_type)s, %(contractor_name)s, %(contractor_contact)s, %(contractor_address)s, %(contractor_phone)s, %(contractor_fax)s, %(client_name)s, %(client_contact)s, %(client_location)s, %(client_phone)s, %(client_fax)s, %(order_number)s, %(product_name)s, %(chip_name)s, %(chip_batch)s, %(processing_pieces)s, %(finished_model)s, %(wafer_numbers)s, %(cp_mapping)s, %(package_method)s, %(process_step)s, %(shipping_address)s, %(source_file)s, %(created_at)s, %(updated_at)s)]
[parameters: {'processing_type': 'CP(全测)', 'contractor_name': '无锡市宜欣科技有限公司', 'contractor_contact': None, 'contractor_address': None, 'contractor_phone': None, 'contractor_fax': None, 'client_name': None, 'client_contact': None, 'client_location': None, 'client_phone': None, 'client_fax': None, 'order_number': '签名：            （盖章）', 'product_name': '', 'chip_name': '', 'chip_batch': '', 'processing_pieces': '', 'finished_model': '', 'wafer_numbers': '填单:', 'cp_mapping': None, 'package_method': '审核:', 'process_step': '', 'shipping_address': '批准:', 'source_file': '宜欣  生产订单模板(CP)_EXCEL2025061802  JP22608D_SP2_P0KR_53df9d8c.xls', 'created_at': datetime.datetime(2025, 6, 24, 2, 47, 42, 618105), 'updated_at': datetime.datetime(2025, 6, 24, 2, 47, 42, 618105)}]
(Background on this error at: https://sqlalche.me/e/20/9h9h)
2025-06-24 10:47:42 | ERROR    | app.services.summary_data_saver | CP订单批量保存失败: (pymysql.err.DataError) (1366, "Incorrect integer value: '' for column 'processing_pieces' at row 1")
[SQL: INSERT INTO cp_order_summary (processing_type, contractor_name, contractor_contact, contractor_address, contractor_phone, contractor_fax, client_name, client_contact, client_location, client_phone, client_fax, order_number, product_name, chip_name, chip_batch, processing_pieces, finished_model, wafer_numbers, cp_mapping, package_method, process_step, shipping_address, source_file, created_at, updated_at) VALUES (%(processing_type)s, %(contractor_name)s, %(contractor_contact)s, %(contractor_address)s, %(contractor_phone)s, %(contractor_fax)s, %(client_name)s, %(client_contact)s, %(client_location)s, %(client_phone)s, %(client_fax)s, %(order_number)s, %(product_name)s, %(chip_name)s, %(chip_batch)s, %(processing_pieces)s, %(finished_model)s, %(wafer_numbers)s, %(cp_mapping)s, %(package_method)s, %(process_step)s, %(shipping_address)s, %(source_file)s, %(created_at)s, %(updated_at)s)]
[parameters: {'processing_type': 'CP(全测)', 'contractor_name': '无锡市宜欣科技有限公司', 'contractor_contact': None, 'contractor_address': None, 'contractor_phone': None, 'contractor_fax': None, 'client_name': None, 'client_contact': None, 'client_location': None, 'client_phone': None, 'client_fax': None, 'order_number': '签名：            （盖章）', 'product_name': '', 'chip_name': '', 'chip_batch': '', 'processing_pieces': '', 'finished_model': '', 'wafer_numbers': '填单:', 'cp_mapping': None, 'package_method': '审核:', 'process_step': '', 'shipping_address': '批准:', 'source_file': '宜欣  生产订单模板(CP)_EXCEL2025061801  JP13B03L_a9d9455b.xls', 'created_at': datetime.datetime(2025, 6, 24, 2, 47, 42, 811798), 'updated_at': datetime.datetime(2025, 6, 24, 2, 47, 42, 811798)}]
(Background on this error at: https://sqlalche.me/e/20/9h9h)
2025-06-24 10:55:47 | ERROR    | app | 扫描邮箱附件失败: No module named 'app.models.email_config'
2025-06-24 10:55:47 | ERROR    | app | 扫描邮箱附件失败: No module named 'app.models.email_config'
2025-06-24 11:04:07 | ERROR    | app | 扫描邮箱附件失败: Entity namespace for "email_configs" has no property "active"
2025-06-24 11:04:07 | ERROR    | app | 扫描邮箱附件失败: Entity namespace for "email_configs" has no property "active"
2025-06-24 11:50:45 | ERROR    | app | 批量处理失败: expected str, bytes or os.PathLike object, not NoneType
2025-06-24 12:02:54 | ERROR    | app | 批量处理失败: expected str, bytes or os.PathLike object, not NoneType
2025-06-24 12:13:05 | ERROR    | app | 批量处理失败: expected str, bytes or os.PathLike object, not NoneType
2025-06-24 12:13:48 | ERROR    | app | 批量处理失败: expected str, bytes or os.PathLike object, not NoneType
2025-06-24 12:13:49 | ERROR    | app | 批量处理失败: expected str, bytes or os.PathLike object, not NoneType
2025-06-24 12:13:50 | ERROR    | app | 批量处理失败: expected str, bytes or os.PathLike object, not NoneType
2025-06-24 16:06:26 | ERROR    | app.utils.email_processor | 处理邮件主题筛选时出错: 'int' object has no attribute 'decode'
2025-06-24 16:06:26 | ERROR    | app.utils.email_processor | 处理邮件主题筛选时出错: 'int' object has no attribute 'decode'
2025-06-24 16:06:28 | ERROR    | app.utils.email_processor | 处理邮件主题筛选时出错: 'int' object has no attribute 'decode'
2025-06-24 16:06:28 | ERROR    | app.utils.email_processor | 处理邮件主题筛选时出错: 'int' object has no attribute 'decode'
2025-06-24 16:06:28 | ERROR    | app.utils.email_processor | 处理邮件主题筛选时出错: 'int' object has no attribute 'decode'
2025-06-24 16:06:28 | ERROR    | app.utils.email_processor | 处理邮件主题筛选时出错: 'int' object has no attribute 'decode'
2025-06-24 16:06:28 | ERROR    | app.utils.email_processor | 处理邮件主题筛选时出错: 'int' object has no attribute 'decode'
2025-06-24 16:06:28 | ERROR    | app.utils.email_processor | 处理邮件主题筛选时出错: 'int' object has no attribute 'decode'
2025-06-24 16:06:28 | ERROR    | app.utils.email_processor | 处理邮件主题筛选时出错: 'int' object has no attribute 'decode'
2025-06-24 16:06:29 | ERROR    | app.utils.email_processor | 处理邮件主题筛选时出错: 'int' object has no attribute 'decode'
2025-06-24 16:06:29 | ERROR    | app.utils.email_processor | 处理邮件主题筛选时出错: 'int' object has no attribute 'decode'
2025-06-24 16:06:29 | ERROR    | app.utils.email_processor | 处理邮件主题筛选时出错: 'int' object has no attribute 'decode'
2025-06-24 16:06:29 | ERROR    | app.utils.email_processor | 处理邮件主题筛选时出错: 'int' object has no attribute 'decode'
2025-06-24 16:06:29 | ERROR    | app.utils.email_processor | 处理邮件主题筛选时出错: 'int' object has no attribute 'decode'
2025-06-24 16:06:30 | ERROR    | app.utils.email_processor | 处理邮件主题筛选时出错: 'int' object has no attribute 'decode'
2025-06-24 16:06:30 | ERROR    | app.utils.email_processor | 处理邮件主题筛选时出错: 'int' object has no attribute 'decode'
2025-06-24 16:06:31 | ERROR    | app.utils.email_processor | 处理邮件主题筛选时出错: 'int' object has no attribute 'decode'
2025-06-24 16:06:31 | ERROR    | app.utils.email_processor | 处理邮件主题筛选时出错: 'int' object has no attribute 'decode'
2025-06-24 16:06:31 | ERROR    | app.utils.email_processor | 处理邮件主题筛选时出错: 'int' object has no attribute 'decode'
2025-06-24 16:06:31 | ERROR    | app.utils.email_processor | 处理邮件主题筛选时出错: 'int' object has no attribute 'decode'
2025-06-24 16:06:31 | ERROR    | app.utils.email_processor | 处理邮件主题筛选时出错: 'int' object has no attribute 'decode'
2025-06-24 16:06:31 | ERROR    | app.utils.email_processor | 处理邮件主题筛选时出错: 'int' object has no attribute 'decode'
2025-06-24 16:06:32 | ERROR    | app.utils.email_processor | 处理邮件主题筛选时出错: 'int' object has no attribute 'decode'
2025-06-24 16:06:32 | ERROR    | app.utils.email_processor | 处理邮件主题筛选时出错: 'int' object has no attribute 'decode'
2025-06-24 16:06:34 | ERROR    | app.utils.email_processor | 处理邮件主题筛选时出错: 'int' object has no attribute 'decode'
2025-06-24 16:06:34 | ERROR    | app.utils.email_processor | 处理邮件主题筛选时出错: 'int' object has no attribute 'decode'
2025-06-24 16:06:34 | ERROR    | app.utils.email_processor | 处理邮件主题筛选时出错: 'int' object has no attribute 'decode'
2025-06-24 16:06:34 | ERROR    | app.utils.email_processor | 处理邮件主题筛选时出错: 'int' object has no attribute 'decode'
2025-06-24 16:06:34 | ERROR    | app.utils.email_processor | 处理邮件主题筛选时出错: 'int' object has no attribute 'decode'
2025-06-24 16:06:34 | ERROR    | app.utils.email_processor | 处理邮件主题筛选时出错: 'int' object has no attribute 'decode'
2025-06-24 16:06:34 | ERROR    | app.utils.email_processor | 处理邮件主题筛选时出错: 'int' object has no attribute 'decode'
2025-06-24 16:06:34 | ERROR    | app.utils.email_processor | 处理邮件主题筛选时出错: 'int' object has no attribute 'decode'
2025-06-24 16:06:34 | ERROR    | app.utils.email_processor | 处理邮件主题筛选时出错: 'int' object has no attribute 'decode'
2025-06-24 16:06:34 | ERROR    | app.utils.email_processor | 处理邮件主题筛选时出错: 'int' object has no attribute 'decode'
2025-06-24 16:06:35 | ERROR    | app.utils.email_processor | 处理邮件主题筛选时出错: 'int' object has no attribute 'decode'
2025-06-24 16:06:35 | ERROR    | app.utils.email_processor | 处理邮件主题筛选时出错: 'int' object has no attribute 'decode'
2025-06-24 16:06:35 | ERROR    | app.utils.email_processor | 处理邮件主题筛选时出错: 'int' object has no attribute 'decode'
2025-06-24 16:06:35 | ERROR    | app.utils.email_processor | 处理邮件主题筛选时出错: 'int' object has no attribute 'decode'
2025-06-24 16:06:35 | ERROR    | app.utils.email_processor | 处理邮件主题筛选时出错: 'int' object has no attribute 'decode'
2025-06-24 16:06:35 | ERROR    | app.utils.email_processor | 处理邮件主题筛选时出错: 'int' object has no attribute 'decode'
2025-06-24 16:06:36 | ERROR    | app.utils.email_processor | 处理邮件主题筛选时出错: 'int' object has no attribute 'decode'
2025-06-24 16:06:36 | ERROR    | app.utils.email_processor | 处理邮件主题筛选时出错: 'int' object has no attribute 'decode'
2025-06-24 16:06:36 | ERROR    | app.utils.email_processor | 处理邮件主题筛选时出错: 'int' object has no attribute 'decode'
2025-06-24 16:06:36 | ERROR    | app.utils.email_processor | 处理邮件主题筛选时出错: 'int' object has no attribute 'decode'
2025-06-24 16:06:36 | ERROR    | app.utils.email_processor | 处理邮件主题筛选时出错: 'int' object has no attribute 'decode'
2025-06-24 16:06:36 | ERROR    | app.utils.email_processor | 处理邮件主题筛选时出错: 'int' object has no attribute 'decode'
2025-06-24 16:06:36 | ERROR    | app.utils.email_processor | 处理邮件主题筛选时出错: 'int' object has no attribute 'decode'
2025-06-24 16:06:36 | ERROR    | app.utils.email_processor | 处理邮件主题筛选时出错: 'int' object has no attribute 'decode'
2025-06-24 16:06:37 | ERROR    | app.utils.email_processor | 处理邮件主题筛选时出错: 'int' object has no attribute 'decode'
2025-06-24 16:06:37 | ERROR    | app.utils.email_processor | 处理邮件主题筛选时出错: 'int' object has no attribute 'decode'
2025-06-24 16:06:37 | ERROR    | app.utils.email_processor | 处理邮件主题筛选时出错: 'int' object has no attribute 'decode'
2025-06-24 16:06:38 | ERROR    | app.utils.email_processor | 处理邮件主题筛选时出错: 'int' object has no attribute 'decode'
2025-06-24 16:06:38 | ERROR    | app.utils.email_processor | 处理邮件主题筛选时出错: 'int' object has no attribute 'decode'
2025-06-24 16:06:38 | ERROR    | app.utils.email_processor | 处理邮件主题筛选时出错: 'int' object has no attribute 'decode'
2025-06-24 16:06:38 | ERROR    | app.utils.email_processor | 处理邮件主题筛选时出错: 'int' object has no attribute 'decode'
2025-06-24 16:06:38 | ERROR    | app.utils.email_processor | 处理邮件主题筛选时出错: 'int' object has no attribute 'decode'
2025-06-24 16:06:40 | ERROR    | app.utils.email_processor | 处理邮件主题筛选时出错: 'int' object has no attribute 'decode'
2025-06-24 16:06:40 | ERROR    | app.utils.email_processor | 处理邮件主题筛选时出错: 'int' object has no attribute 'decode'
2025-06-24 16:06:40 | ERROR    | app.utils.email_processor | 处理邮件主题筛选时出错: 'int' object has no attribute 'decode'
2025-06-24 16:06:40 | ERROR    | app.utils.email_processor | 处理邮件主题筛选时出错: 'int' object has no attribute 'decode'
2025-06-24 16:06:40 | ERROR    | app.utils.email_processor | 处理邮件主题筛选时出错: 'int' object has no attribute 'decode'
2025-06-24 16:06:40 | ERROR    | app.utils.email_processor | 处理邮件主题筛选时出错: 'int' object has no attribute 'decode'
2025-06-24 16:06:40 | ERROR    | app.utils.email_processor | 处理邮件主题筛选时出错: 'int' object has no attribute 'decode'
2025-06-24 16:06:40 | ERROR    | app.utils.email_processor | 处理邮件主题筛选时出错: 'int' object has no attribute 'decode'
2025-06-24 16:06:40 | ERROR    | app.utils.email_processor | 处理邮件主题筛选时出错: 'int' object has no attribute 'decode'
2025-06-24 16:06:40 | ERROR    | app.utils.email_processor | 处理邮件主题筛选时出错: 'int' object has no attribute 'decode'
2025-06-24 16:06:40 | ERROR    | app.utils.email_processor | 处理邮件主题筛选时出错: 'int' object has no attribute 'decode'
2025-06-24 16:06:40 | ERROR    | app.utils.email_processor | 处理邮件主题筛选时出错: 'int' object has no attribute 'decode'
2025-06-24 16:06:41 | ERROR    | app.utils.email_processor | 处理邮件主题筛选时出错: 'int' object has no attribute 'decode'
2025-06-24 16:06:41 | ERROR    | app.utils.email_processor | 处理邮件主题筛选时出错: 'int' object has no attribute 'decode'
2025-06-24 16:06:56 | ERROR    | app.utils.email_processor | 处理邮件主题筛选时出错: 'int' object has no attribute 'decode'
2025-06-24 16:06:56 | ERROR    | app.utils.email_processor | 处理邮件主题筛选时出错: 'int' object has no attribute 'decode'
2025-06-24 16:06:56 | ERROR    | app.utils.email_processor | 处理邮件主题筛选时出错: 'int' object has no attribute 'decode'
2025-06-24 16:06:56 | ERROR    | app.utils.email_processor | 处理邮件主题筛选时出错: 'int' object has no attribute 'decode'
2025-06-24 16:06:56 | ERROR    | app.utils.email_processor | 处理邮件主题筛选时出错: 'int' object has no attribute 'decode'
2025-06-24 16:06:56 | ERROR    | app.utils.email_processor | 处理邮件主题筛选时出错: 'int' object has no attribute 'decode'
2025-06-24 16:06:57 | ERROR    | app.utils.email_processor | 处理邮件主题筛选时出错: 'int' object has no attribute 'decode'
2025-06-24 16:06:57 | ERROR    | app.utils.email_processor | 处理邮件主题筛选时出错: 'int' object has no attribute 'decode'
2025-06-24 16:06:58 | ERROR    | app.utils.email_processor | 处理邮件主题筛选时出错: 'int' object has no attribute 'decode'
2025-06-24 16:06:58 | ERROR    | app.utils.email_processor | 处理邮件主题筛选时出错: 'int' object has no attribute 'decode'
2025-06-24 16:06:58 | ERROR    | app.utils.email_processor | 处理邮件主题筛选时出错: 'int' object has no attribute 'decode'
2025-06-24 16:06:58 | ERROR    | app.utils.email_processor | 处理邮件主题筛选时出错: 'int' object has no attribute 'decode'
2025-06-24 16:06:58 | ERROR    | app.utils.email_processor | 处理邮件主题筛选时出错: 'int' object has no attribute 'decode'
2025-06-24 16:06:58 | ERROR    | app.utils.email_processor | 处理邮件主题筛选时出错: 'int' object has no attribute 'decode'
2025-06-24 16:06:58 | ERROR    | app.utils.email_processor | 处理邮件主题筛选时出错: 'int' object has no attribute 'decode'
2025-06-24 16:06:58 | ERROR    | app.utils.email_processor | 处理邮件主题筛选时出错: 'int' object has no attribute 'decode'
2025-06-24 16:06:58 | ERROR    | app.utils.email_processor | 处理邮件主题筛选时出错: 'int' object has no attribute 'decode'
2025-06-24 16:06:58 | ERROR    | app.utils.email_processor | 处理邮件主题筛选时出错: 'int' object has no attribute 'decode'
2025-06-24 16:06:59 | ERROR    | app.utils.email_processor | 处理邮件主题筛选时出错: 'int' object has no attribute 'decode'
2025-06-24 16:06:59 | ERROR    | app.utils.email_processor | 处理邮件主题筛选时出错: 'int' object has no attribute 'decode'
2025-06-24 16:06:59 | ERROR    | app.utils.email_processor | 处理邮件主题筛选时出错: 'int' object has no attribute 'decode'
2025-06-24 16:06:59 | ERROR    | app.utils.email_processor | 处理邮件主题筛选时出错: 'int' object has no attribute 'decode'
2025-06-24 16:06:59 | ERROR    | app.utils.email_processor | 处理邮件主题筛选时出错: 'int' object has no attribute 'decode'
2025-06-24 16:06:59 | ERROR    | app.utils.email_processor | 处理邮件主题筛选时出错: 'int' object has no attribute 'decode'
2025-06-24 16:06:59 | ERROR    | app.utils.email_processor | 处理邮件主题筛选时出错: 'int' object has no attribute 'decode'
2025-06-24 16:06:59 | ERROR    | app.utils.email_processor | 处理邮件主题筛选时出错: 'int' object has no attribute 'decode'
2025-06-24 16:06:59 | ERROR    | app.utils.email_processor | 处理邮件主题筛选时出错: 'int' object has no attribute 'decode'
2025-06-24 16:06:59 | ERROR    | app.utils.email_processor | 处理邮件主题筛选时出错: 'int' object has no attribute 'decode'
2025-06-24 16:07:00 | ERROR    | app.utils.email_processor | 处理邮件主题筛选时出错: 'int' object has no attribute 'decode'
2025-06-24 16:07:00 | ERROR    | app.utils.email_processor | 处理邮件主题筛选时出错: 'int' object has no attribute 'decode'
2025-06-24 16:07:00 | ERROR    | app.utils.email_processor | 处理邮件主题筛选时出错: 'int' object has no attribute 'decode'
2025-06-24 16:07:00 | ERROR    | app.utils.email_processor | 处理邮件主题筛选时出错: 'int' object has no attribute 'decode'
2025-06-24 16:07:01 | ERROR    | app.utils.email_processor | 处理邮件主题筛选时出错: 'int' object has no attribute 'decode'
2025-06-24 16:07:01 | ERROR    | app.utils.email_processor | 处理邮件主题筛选时出错: 'int' object has no attribute 'decode'
2025-06-24 16:07:02 | ERROR    | app.utils.email_processor | 处理邮件主题筛选时出错: 'int' object has no attribute 'decode'
2025-06-24 16:07:02 | ERROR    | app.utils.email_processor | 处理邮件主题筛选时出错: 'int' object has no attribute 'decode'
2025-06-24 16:07:02 | ERROR    | app.utils.email_processor | 处理邮件主题筛选时出错: 'int' object has no attribute 'decode'
2025-06-24 16:07:02 | ERROR    | app.utils.email_processor | 处理邮件主题筛选时出错: 'int' object has no attribute 'decode'
2025-06-24 16:07:02 | ERROR    | app.utils.email_processor | 处理邮件主题筛选时出错: 'int' object has no attribute 'decode'
2025-06-24 16:07:02 | ERROR    | app.utils.email_processor | 处理邮件主题筛选时出错: 'int' object has no attribute 'decode'
2025-06-24 16:07:08 | ERROR    | app.utils.email_processor | 预览邮件 b'32780' 时出错: 'int' object has no attribute 'decode'
2025-06-24 16:07:08 | ERROR    | app.utils.email_processor | 预览邮件 b'32792' 时出错: 'int' object has no attribute 'decode'
2025-06-24 16:07:08 | ERROR    | app.utils.email_processor | 预览邮件 b'32797' 时出错: 'int' object has no attribute 'decode'
2025-06-24 16:07:08 | ERROR    | app.utils.email_processor | 预览邮件 b'32808' 时出错: 'int' object has no attribute 'decode'
2025-06-24 16:07:08 | ERROR    | app.utils.email_processor | 预览邮件 b'32809' 时出错: 'int' object has no attribute 'decode'
2025-06-24 16:07:09 | ERROR    | app.utils.email_processor | 预览邮件 b'32811' 时出错: 'int' object has no attribute 'decode'
2025-06-24 16:07:09 | ERROR    | app.utils.email_processor | 预览邮件 b'32812' 时出错: 'int' object has no attribute 'decode'
2025-06-24 16:07:09 | ERROR    | app.utils.email_processor | 预览邮件 b'32813' 时出错: 'int' object has no attribute 'decode'
2025-06-24 16:07:09 | ERROR    | app.utils.email_processor | 预览邮件 b'32814' 时出错: 'int' object has no attribute 'decode'
2025-06-24 16:07:09 | ERROR    | app.utils.email_processor | 预览邮件 b'32815' 时出错: 'int' object has no attribute 'decode'
2025-06-24 16:07:09 | ERROR    | app.utils.email_processor | 预览邮件 b'32816' 时出错: 'int' object has no attribute 'decode'
2025-06-24 16:07:09 | ERROR    | app.utils.email_processor | 预览邮件 b'32817' 时出错: 'int' object has no attribute 'decode'
2025-06-24 16:07:09 | ERROR    | app.utils.email_processor | 预览邮件 b'32836' 时出错: 'int' object has no attribute 'decode'
2025-06-24 16:07:10 | ERROR    | app.utils.email_processor | 预览邮件 b'32840' 时出错: 'int' object has no attribute 'decode'
2025-06-24 16:12:07 | ERROR    | app.utils.email_processor | 处理邮件主题筛选时出错: 'int' object has no attribute 'decode'
2025-06-24 16:12:08 | ERROR    | app.utils.email_processor | 处理邮件主题筛选时出错: 'int' object has no attribute 'decode'
2025-06-24 16:12:09 | ERROR    | app.utils.email_processor | 处理邮件主题筛选时出错: 'int' object has no attribute 'decode'
2025-06-24 16:12:09 | ERROR    | app.utils.email_processor | 处理邮件主题筛选时出错: 'int' object has no attribute 'decode'
2025-06-24 16:12:09 | ERROR    | app.utils.email_processor | 处理邮件主题筛选时出错: 'int' object has no attribute 'decode'
2025-06-24 16:12:10 | ERROR    | app.utils.email_processor | 处理邮件主题筛选时出错: 'int' object has no attribute 'decode'
2025-06-24 16:12:10 | ERROR    | app.utils.email_processor | 处理邮件主题筛选时出错: 'int' object has no attribute 'decode'
2025-06-24 16:12:10 | ERROR    | app.utils.email_processor | 处理邮件主题筛选时出错: 'int' object has no attribute 'decode'
2025-06-24 16:12:10 | ERROR    | app.utils.email_processor | 处理邮件主题筛选时出错: 'int' object has no attribute 'decode'
2025-06-24 16:12:10 | ERROR    | app.utils.email_processor | 处理邮件主题筛选时出错: 'int' object has no attribute 'decode'
2025-06-24 16:12:10 | ERROR    | app.utils.email_processor | 处理邮件主题筛选时出错: 'int' object has no attribute 'decode'
2025-06-24 16:12:10 | ERROR    | app.utils.email_processor | 处理邮件主题筛选时出错: 'int' object has no attribute 'decode'
2025-06-24 16:12:10 | ERROR    | app.utils.email_processor | 处理邮件主题筛选时出错: 'int' object has no attribute 'decode'
2025-06-24 16:12:10 | ERROR    | app.utils.email_processor | 处理邮件主题筛选时出错: 'int' object has no attribute 'decode'
2025-06-24 16:12:11 | ERROR    | app.utils.email_processor | 处理邮件主题筛选时出错: 'int' object has no attribute 'decode'
2025-06-24 16:12:11 | ERROR    | app.utils.email_processor | 处理邮件主题筛选时出错: 'int' object has no attribute 'decode'
2025-06-24 16:12:12 | ERROR    | app.utils.email_processor | 处理邮件主题筛选时出错: 'int' object has no attribute 'decode'
2025-06-24 16:12:12 | ERROR    | app.utils.email_processor | 处理邮件主题筛选时出错: 'int' object has no attribute 'decode'
2025-06-24 16:12:12 | ERROR    | app.utils.email_processor | 处理邮件主题筛选时出错: 'int' object has no attribute 'decode'
2025-06-24 16:12:12 | ERROR    | app.utils.email_processor | 处理邮件主题筛选时出错: 'int' object has no attribute 'decode'
2025-06-24 16:12:12 | ERROR    | app.utils.email_processor | 处理邮件主题筛选时出错: 'int' object has no attribute 'decode'
2025-06-24 16:12:12 | ERROR    | app.utils.email_processor | 处理邮件主题筛选时出错: 'int' object has no attribute 'decode'
2025-06-24 16:12:13 | ERROR    | app.utils.email_processor | 处理邮件主题筛选时出错: 'int' object has no attribute 'decode'
2025-06-24 16:12:14 | ERROR    | app.utils.email_processor | 处理邮件主题筛选时出错: 'int' object has no attribute 'decode'
2025-06-24 16:12:15 | ERROR    | app.utils.email_processor | 处理邮件主题筛选时出错: 'int' object has no attribute 'decode'
2025-06-24 16:12:15 | ERROR    | app.utils.email_processor | 处理邮件主题筛选时出错: 'int' object has no attribute 'decode'
2025-06-24 16:12:15 | ERROR    | app.utils.email_processor | 处理邮件主题筛选时出错: 'int' object has no attribute 'decode'
2025-06-24 16:12:15 | ERROR    | app.utils.email_processor | 处理邮件主题筛选时出错: 'int' object has no attribute 'decode'
2025-06-24 16:12:15 | ERROR    | app.utils.email_processor | 处理邮件主题筛选时出错: 'int' object has no attribute 'decode'
2025-06-24 16:12:15 | ERROR    | app.utils.email_processor | 处理邮件主题筛选时出错: 'int' object has no attribute 'decode'
2025-06-24 16:12:15 | ERROR    | app.utils.email_processor | 处理邮件主题筛选时出错: 'int' object has no attribute 'decode'
2025-06-24 16:12:15 | ERROR    | app.utils.email_processor | 处理邮件主题筛选时出错: 'int' object has no attribute 'decode'
2025-06-24 16:12:15 | ERROR    | app.utils.email_processor | 处理邮件主题筛选时出错: 'int' object has no attribute 'decode'
2025-06-24 16:12:15 | ERROR    | app.utils.email_processor | 处理邮件主题筛选时出错: 'int' object has no attribute 'decode'
2025-06-24 16:12:16 | ERROR    | app.utils.email_processor | 处理邮件主题筛选时出错: 'int' object has no attribute 'decode'
2025-06-24 16:12:16 | ERROR    | app.utils.email_processor | 处理邮件主题筛选时出错: 'int' object has no attribute 'decode'
2025-06-24 16:12:16 | ERROR    | app.utils.email_processor | 处理邮件主题筛选时出错: 'int' object has no attribute 'decode'
2025-06-24 16:12:16 | ERROR    | app.utils.email_processor | 处理邮件主题筛选时出错: 'int' object has no attribute 'decode'
2025-06-24 16:12:16 | ERROR    | app.utils.email_processor | 处理邮件主题筛选时出错: 'int' object has no attribute 'decode'
2025-06-24 16:12:16 | ERROR    | app.utils.email_processor | 处理邮件主题筛选时出错: 'int' object has no attribute 'decode'
2025-06-24 16:12:16 | ERROR    | app.utils.email_processor | 处理邮件主题筛选时出错: 'int' object has no attribute 'decode'
2025-06-24 16:12:17 | ERROR    | app.utils.email_processor | 处理邮件主题筛选时出错: 'int' object has no attribute 'decode'
2025-06-24 16:12:17 | ERROR    | app.utils.email_processor | 处理邮件主题筛选时出错: 'int' object has no attribute 'decode'
2025-06-24 16:12:17 | ERROR    | app.utils.email_processor | 处理邮件主题筛选时出错: 'int' object has no attribute 'decode'
2025-06-24 16:12:17 | ERROR    | app.utils.email_processor | 处理邮件主题筛选时出错: 'int' object has no attribute 'decode'
2025-06-24 16:12:17 | ERROR    | app.utils.email_processor | 处理邮件主题筛选时出错: 'int' object has no attribute 'decode'
2025-06-24 16:12:17 | ERROR    | app.utils.email_processor | 处理邮件主题筛选时出错: 'int' object has no attribute 'decode'
2025-06-24 16:12:17 | ERROR    | app.utils.email_processor | 处理邮件主题筛选时出错: 'int' object has no attribute 'decode'
2025-06-24 16:12:17 | ERROR    | app.utils.email_processor | 处理邮件主题筛选时出错: 'int' object has no attribute 'decode'
2025-06-24 16:12:18 | ERROR    | app.utils.email_processor | 处理邮件主题筛选时出错: 'int' object has no attribute 'decode'
2025-06-24 16:12:18 | ERROR    | app.utils.email_processor | 处理邮件主题筛选时出错: 'int' object has no attribute 'decode'
2025-06-24 16:12:18 | ERROR    | app.utils.email_processor | 处理邮件主题筛选时出错: 'int' object has no attribute 'decode'
2025-06-24 16:12:19 | ERROR    | app.utils.email_processor | 处理邮件主题筛选时出错: 'int' object has no attribute 'decode'
2025-06-24 16:12:19 | ERROR    | app.utils.email_processor | 处理邮件主题筛选时出错: 'int' object has no attribute 'decode'
2025-06-24 16:12:19 | ERROR    | app.utils.email_processor | 处理邮件主题筛选时出错: 'int' object has no attribute 'decode'
2025-06-24 16:12:19 | ERROR    | app.utils.email_processor | 处理邮件主题筛选时出错: 'int' object has no attribute 'decode'
2025-06-24 16:12:21 | ERROR    | app.utils.email_processor | 处理邮件主题筛选时出错: 'int' object has no attribute 'decode'
2025-06-24 16:12:21 | ERROR    | app.utils.email_processor | 处理邮件主题筛选时出错: 'int' object has no attribute 'decode'
2025-06-24 16:12:21 | ERROR    | app.utils.email_processor | 处理邮件主题筛选时出错: 'int' object has no attribute 'decode'
2025-06-24 16:12:21 | ERROR    | app.utils.email_processor | 处理邮件主题筛选时出错: 'int' object has no attribute 'decode'
2025-06-24 16:12:21 | ERROR    | app.utils.email_processor | 处理邮件主题筛选时出错: 'int' object has no attribute 'decode'
2025-06-24 16:12:21 | ERROR    | app.utils.email_processor | 处理邮件主题筛选时出错: 'int' object has no attribute 'decode'
2025-06-24 16:12:21 | ERROR    | app.utils.email_processor | 处理邮件主题筛选时出错: 'int' object has no attribute 'decode'
2025-06-24 16:12:21 | ERROR    | app.utils.email_processor | 处理邮件主题筛选时出错: 'int' object has no attribute 'decode'
2025-06-24 16:12:21 | ERROR    | app.utils.email_processor | 处理邮件主题筛选时出错: 'int' object has no attribute 'decode'
2025-06-24 16:12:21 | ERROR    | app.utils.email_processor | 处理邮件主题筛选时出错: 'int' object has no attribute 'decode'
2025-06-24 16:12:21 | ERROR    | app.utils.email_processor | 处理邮件主题筛选时出错: 'int' object has no attribute 'decode'
2025-06-24 16:12:21 | ERROR    | app.utils.email_processor | 处理邮件主题筛选时出错: 'int' object has no attribute 'decode'
2025-06-24 16:12:21 | ERROR    | app.utils.email_processor | 处理邮件主题筛选时出错: 'int' object has no attribute 'decode'
2025-06-24 16:12:22 | ERROR    | app.utils.email_processor | 处理邮件主题筛选时出错: 'int' object has no attribute 'decode'
2025-06-24 16:12:24 | ERROR    | app.utils.email_processor | 处理邮件主题筛选时出错: 'int' object has no attribute 'decode'
2025-06-24 16:12:24 | ERROR    | app.utils.email_processor | 处理邮件主题筛选时出错: 'int' object has no attribute 'decode'
2025-06-24 16:12:25 | ERROR    | app.utils.email_processor | 处理邮件主题筛选时出错: 'int' object has no attribute 'decode'
2025-06-24 16:12:25 | ERROR    | app.utils.email_processor | 处理邮件主题筛选时出错: 'int' object has no attribute 'decode'
2025-06-24 16:12:26 | ERROR    | app.utils.email_processor | 处理邮件主题筛选时出错: 'int' object has no attribute 'decode'
2025-06-24 16:12:26 | ERROR    | app.utils.email_processor | 处理邮件主题筛选时出错: 'int' object has no attribute 'decode'
2025-06-24 16:12:28 | ERROR    | app.utils.email_processor | 处理邮件主题筛选时出错: 'int' object has no attribute 'decode'
2025-06-24 16:12:28 | ERROR    | app.utils.email_processor | 处理邮件主题筛选时出错: 'int' object has no attribute 'decode'
2025-06-24 16:12:28 | ERROR    | app.utils.email_processor | 处理邮件主题筛选时出错: 'int' object has no attribute 'decode'
2025-06-24 16:12:28 | ERROR    | app.utils.email_processor | 处理邮件主题筛选时出错: 'int' object has no attribute 'decode'
2025-06-24 16:12:28 | ERROR    | app.utils.email_processor | 处理邮件主题筛选时出错: 'int' object has no attribute 'decode'
2025-06-24 16:12:28 | ERROR    | app.utils.email_processor | 处理邮件主题筛选时出错: 'int' object has no attribute 'decode'
2025-06-24 16:12:28 | ERROR    | app.utils.email_processor | 处理邮件主题筛选时出错: 'int' object has no attribute 'decode'
2025-06-24 16:12:28 | ERROR    | app.utils.email_processor | 处理邮件主题筛选时出错: 'int' object has no attribute 'decode'
2025-06-24 16:12:28 | ERROR    | app.utils.email_processor | 处理邮件主题筛选时出错: 'int' object has no attribute 'decode'
2025-06-24 16:12:28 | ERROR    | app.utils.email_processor | 处理邮件主题筛选时出错: 'int' object has no attribute 'decode'
2025-06-24 16:12:28 | ERROR    | app.utils.email_processor | 处理邮件主题筛选时出错: 'int' object has no attribute 'decode'
2025-06-24 16:12:28 | ERROR    | app.utils.email_processor | 处理邮件主题筛选时出错: 'int' object has no attribute 'decode'
2025-06-24 16:12:28 | ERROR    | app.utils.email_processor | 处理邮件主题筛选时出错: 'int' object has no attribute 'decode'
2025-06-24 16:12:28 | ERROR    | app.utils.email_processor | 处理邮件主题筛选时出错: 'int' object has no attribute 'decode'
2025-06-24 16:12:28 | ERROR    | app.utils.email_processor | 处理邮件主题筛选时出错: 'int' object has no attribute 'decode'
2025-06-24 16:12:28 | ERROR    | app.utils.email_processor | 处理邮件主题筛选时出错: 'int' object has no attribute 'decode'
2025-06-24 16:12:28 | ERROR    | app.utils.email_processor | 处理邮件主题筛选时出错: 'int' object has no attribute 'decode'
2025-06-24 16:12:28 | ERROR    | app.utils.email_processor | 处理邮件主题筛选时出错: 'int' object has no attribute 'decode'
2025-06-24 16:12:30 | ERROR    | app.utils.email_processor | 处理邮件主题筛选时出错: 'int' object has no attribute 'decode'
2025-06-24 16:12:30 | ERROR    | app.utils.email_processor | 处理邮件主题筛选时出错: 'int' object has no attribute 'decode'
2025-06-24 16:12:30 | ERROR    | app.utils.email_processor | 处理邮件主题筛选时出错: 'int' object has no attribute 'decode'
2025-06-24 16:12:30 | ERROR    | app.utils.email_processor | 处理邮件主题筛选时出错: 'int' object has no attribute 'decode'
2025-06-24 16:12:30 | ERROR    | app.utils.email_processor | 处理邮件主题筛选时出错: 'int' object has no attribute 'decode'
2025-06-24 16:12:30 | ERROR    | app.utils.email_processor | 处理邮件主题筛选时出错: 'int' object has no attribute 'decode'
2025-06-24 16:12:35 | ERROR    | app.utils.email_processor | 处理邮件主题筛选时出错: 'int' object has no attribute 'decode'
2025-06-24 16:12:35 | ERROR    | app.utils.email_processor | 处理邮件主题筛选时出错: 'int' object has no attribute 'decode'
2025-06-24 16:12:35 | ERROR    | app.utils.email_processor | 处理邮件主题筛选时出错: 'int' object has no attribute 'decode'
2025-06-24 16:12:36 | ERROR    | app.utils.email_processor | 处理邮件主题筛选时出错: 'int' object has no attribute 'decode'
2025-06-24 16:12:36 | ERROR    | app.utils.email_processor | 处理邮件主题筛选时出错: 'int' object has no attribute 'decode'
2025-06-24 16:12:36 | ERROR    | app.utils.email_processor | 处理邮件主题筛选时出错: 'int' object has no attribute 'decode'
2025-06-24 16:12:37 | ERROR    | app.utils.email_processor | 处理邮件主题筛选时出错: 'int' object has no attribute 'decode'
2025-06-24 16:12:37 | ERROR    | app.utils.email_processor | 处理邮件主题筛选时出错: 'int' object has no attribute 'decode'
2025-06-24 16:12:37 | ERROR    | app.utils.email_processor | 处理邮件主题筛选时出错: 'int' object has no attribute 'decode'
2025-06-24 16:12:38 | ERROR    | app.utils.email_processor | 处理邮件主题筛选时出错: 'int' object has no attribute 'decode'
2025-06-24 16:12:38 | ERROR    | app.utils.email_processor | 处理邮件主题筛选时出错: 'int' object has no attribute 'decode'
2025-06-24 16:12:38 | ERROR    | app.utils.email_processor | 处理邮件主题筛选时出错: 'int' object has no attribute 'decode'
2025-06-24 16:12:38 | ERROR    | app.utils.email_processor | 处理邮件主题筛选时出错: 'int' object has no attribute 'decode'
2025-06-24 16:12:38 | ERROR    | app.utils.email_processor | 处理邮件主题筛选时出错: 'int' object has no attribute 'decode'
2025-06-24 16:12:38 | ERROR    | app.utils.email_processor | 处理邮件主题筛选时出错: 'int' object has no attribute 'decode'
2025-06-24 16:12:38 | ERROR    | app.utils.email_processor | 处理邮件主题筛选时出错: 'int' object has no attribute 'decode'
2025-06-24 16:12:38 | ERROR    | app.utils.email_processor | 处理邮件主题筛选时出错: 'int' object has no attribute 'decode'
2025-06-24 16:12:38 | ERROR    | app.utils.email_processor | 处理邮件主题筛选时出错: 'int' object has no attribute 'decode'
2025-06-24 16:12:38 | ERROR    | app.utils.email_processor | 处理邮件主题筛选时出错: 'int' object has no attribute 'decode'
2025-06-24 16:12:38 | ERROR    | app.utils.email_processor | 处理邮件主题筛选时出错: 'int' object has no attribute 'decode'
2025-06-24 16:12:38 | ERROR    | app.utils.email_processor | 处理邮件主题筛选时出错: 'int' object has no attribute 'decode'
2025-06-24 16:12:38 | ERROR    | app.utils.email_processor | 处理邮件主题筛选时出错: 'int' object has no attribute 'decode'
2025-06-24 16:12:39 | ERROR    | app.utils.email_processor | 处理邮件主题筛选时出错: 'int' object has no attribute 'decode'
2025-06-24 16:12:39 | ERROR    | app.utils.email_processor | 处理邮件主题筛选时出错: 'int' object has no attribute 'decode'
2025-06-24 16:12:39 | ERROR    | app.utils.email_processor | 处理邮件主题筛选时出错: 'int' object has no attribute 'decode'
2025-06-24 16:12:39 | ERROR    | app.utils.email_processor | 处理邮件主题筛选时出错: 'int' object has no attribute 'decode'
2025-06-24 16:12:39 | ERROR    | app.utils.email_processor | 处理邮件主题筛选时出错: 'int' object has no attribute 'decode'
2025-06-24 16:12:39 | ERROR    | app.utils.email_processor | 处理邮件主题筛选时出错: 'int' object has no attribute 'decode'
2025-06-24 16:12:40 | ERROR    | app.utils.email_processor | 处理邮件主题筛选时出错: 'int' object has no attribute 'decode'
2025-06-24 16:12:40 | ERROR    | app.utils.email_processor | 处理邮件主题筛选时出错: 'int' object has no attribute 'decode'
2025-06-24 16:12:40 | ERROR    | app.utils.email_processor | 处理邮件主题筛选时出错: 'int' object has no attribute 'decode'
2025-06-24 16:12:40 | ERROR    | app.utils.email_processor | 处理邮件主题筛选时出错: 'int' object has no attribute 'decode'
2025-06-24 16:12:42 | ERROR    | app.utils.email_processor | 处理邮件主题筛选时出错: 'int' object has no attribute 'decode'
2025-06-24 16:12:42 | ERROR    | app.utils.email_processor | 处理邮件主题筛选时出错: 'int' object has no attribute 'decode'
2025-06-24 16:12:42 | ERROR    | app.utils.email_processor | 处理邮件主题筛选时出错: 'int' object has no attribute 'decode'
2025-06-24 16:12:42 | ERROR    | app.utils.email_processor | 处理邮件主题筛选时出错: 'int' object has no attribute 'decode'
2025-06-24 16:12:42 | ERROR    | app.utils.email_processor | 处理邮件主题筛选时出错: 'int' object has no attribute 'decode'
2025-06-24 16:12:42 | ERROR    | app.utils.email_processor | 处理邮件主题筛选时出错: 'int' object has no attribute 'decode'
2025-06-24 16:12:42 | ERROR    | app.utils.email_processor | 处理邮件主题筛选时出错: 'int' object has no attribute 'decode'
2025-06-24 16:12:42 | ERROR    | app.utils.email_processor | 处理邮件主题筛选时出错: 'int' object has no attribute 'decode'
2025-06-24 16:13:51 | ERROR    | app.utils.email_processor | 处理邮件主题筛选时出错: 'int' object has no attribute 'decode'
2025-06-24 16:13:51 | ERROR    | app.utils.email_processor | 处理邮件主题筛选时出错: 'int' object has no attribute 'decode'
2025-06-24 16:13:52 | ERROR    | app.utils.email_processor | 处理邮件主题筛选时出错: 'int' object has no attribute 'decode'
2025-06-24 16:13:52 | ERROR    | app.utils.email_processor | 处理邮件主题筛选时出错: 'int' object has no attribute 'decode'
2025-06-24 16:13:52 | ERROR    | app.utils.email_processor | 处理邮件主题筛选时出错: 'int' object has no attribute 'decode'
2025-06-24 16:13:53 | ERROR    | app.utils.email_processor | 处理邮件主题筛选时出错: 'int' object has no attribute 'decode'
2025-06-24 16:13:53 | ERROR    | app.utils.email_processor | 处理邮件主题筛选时出错: 'int' object has no attribute 'decode'
2025-06-24 16:13:53 | ERROR    | app.utils.email_processor | 处理邮件主题筛选时出错: 'int' object has no attribute 'decode'
2025-06-24 16:13:53 | ERROR    | app.utils.email_processor | 处理邮件主题筛选时出错: 'int' object has no attribute 'decode'
2025-06-24 16:13:53 | ERROR    | app.utils.email_processor | 处理邮件主题筛选时出错: 'int' object has no attribute 'decode'
2025-06-24 16:13:53 | ERROR    | app.utils.email_processor | 处理邮件主题筛选时出错: 'int' object has no attribute 'decode'
2025-06-24 16:13:53 | ERROR    | app.utils.email_processor | 处理邮件主题筛选时出错: 'int' object has no attribute 'decode'
2025-06-24 16:13:53 | ERROR    | app.utils.email_processor | 处理邮件主题筛选时出错: 'int' object has no attribute 'decode'
2025-06-24 16:13:53 | ERROR    | app.utils.email_processor | 处理邮件主题筛选时出错: 'int' object has no attribute 'decode'
2025-06-24 16:13:54 | ERROR    | app.utils.email_processor | 处理邮件主题筛选时出错: 'int' object has no attribute 'decode'
2025-06-24 16:13:55 | ERROR    | app.utils.email_processor | 处理邮件主题筛选时出错: 'int' object has no attribute 'decode'
2025-06-24 16:13:55 | ERROR    | app.utils.email_processor | 处理邮件主题筛选时出错: 'int' object has no attribute 'decode'
2025-06-24 16:13:55 | ERROR    | app.utils.email_processor | 处理邮件主题筛选时出错: 'int' object has no attribute 'decode'
2025-06-24 16:13:55 | ERROR    | app.utils.email_processor | 处理邮件主题筛选时出错: 'int' object has no attribute 'decode'
2025-06-24 16:13:56 | ERROR    | app.utils.email_processor | 处理邮件主题筛选时出错: 'int' object has no attribute 'decode'
2025-06-24 16:13:56 | ERROR    | app.utils.email_processor | 处理邮件主题筛选时出错: 'int' object has no attribute 'decode'
2025-06-24 16:13:56 | ERROR    | app.utils.email_processor | 处理邮件主题筛选时出错: 'int' object has no attribute 'decode'
2025-06-24 16:13:56 | ERROR    | app.utils.email_processor | 处理邮件主题筛选时出错: 'int' object has no attribute 'decode'
2025-06-24 16:13:57 | ERROR    | app.utils.email_processor | 处理邮件主题筛选时出错: 'int' object has no attribute 'decode'
2025-06-24 16:13:57 | ERROR    | app.utils.email_processor | 处理邮件主题筛选时出错: 'int' object has no attribute 'decode'
2025-06-24 16:13:57 | ERROR    | app.utils.email_processor | 处理邮件主题筛选时出错: 'int' object has no attribute 'decode'
2025-06-24 16:13:57 | ERROR    | app.utils.email_processor | 处理邮件主题筛选时出错: 'int' object has no attribute 'decode'
2025-06-24 16:13:57 | ERROR    | app.utils.email_processor | 处理邮件主题筛选时出错: 'int' object has no attribute 'decode'
2025-06-24 16:13:57 | ERROR    | app.utils.email_processor | 处理邮件主题筛选时出错: 'int' object has no attribute 'decode'
2025-06-24 16:13:57 | ERROR    | app.utils.email_processor | 处理邮件主题筛选时出错: 'int' object has no attribute 'decode'
2025-06-24 16:13:58 | ERROR    | app.utils.email_processor | 处理邮件主题筛选时出错: 'int' object has no attribute 'decode'
2025-06-24 16:13:58 | ERROR    | app.utils.email_processor | 处理邮件主题筛选时出错: 'int' object has no attribute 'decode'
2025-06-24 16:13:58 | ERROR    | app.utils.email_processor | 处理邮件主题筛选时出错: 'int' object has no attribute 'decode'
2025-06-24 16:13:58 | ERROR    | app.utils.email_processor | 处理邮件主题筛选时出错: 'int' object has no attribute 'decode'
2025-06-24 16:13:58 | ERROR    | app.utils.email_processor | 处理邮件主题筛选时出错: 'int' object has no attribute 'decode'
2025-06-24 16:13:58 | ERROR    | app.utils.email_processor | 处理邮件主题筛选时出错: 'int' object has no attribute 'decode'
2025-06-24 16:13:59 | ERROR    | app.utils.email_processor | 处理邮件主题筛选时出错: 'int' object has no attribute 'decode'
2025-06-24 16:13:59 | ERROR    | app.utils.email_processor | 处理邮件主题筛选时出错: 'int' object has no attribute 'decode'
2025-06-24 16:13:59 | ERROR    | app.utils.email_processor | 处理邮件主题筛选时出错: 'int' object has no attribute 'decode'
2025-06-24 16:13:59 | ERROR    | app.utils.email_processor | 处理邮件主题筛选时出错: 'int' object has no attribute 'decode'
2025-06-24 16:13:59 | ERROR    | app.utils.email_processor | 处理邮件主题筛选时出错: 'int' object has no attribute 'decode'
2025-06-24 16:13:59 | ERROR    | app.utils.email_processor | 处理邮件主题筛选时出错: 'int' object has no attribute 'decode'
2025-06-24 16:13:59 | ERROR    | app.utils.email_processor | 处理邮件主题筛选时出错: 'int' object has no attribute 'decode'
2025-06-24 16:13:59 | ERROR    | app.utils.email_processor | 处理邮件主题筛选时出错: 'int' object has no attribute 'decode'
2025-06-24 16:13:59 | ERROR    | app.utils.email_processor | 处理邮件主题筛选时出错: 'int' object has no attribute 'decode'
2025-06-24 16:13:59 | ERROR    | app.utils.email_processor | 处理邮件主题筛选时出错: 'int' object has no attribute 'decode'
2025-06-24 16:14:00 | ERROR    | app.utils.email_processor | 处理邮件主题筛选时出错: 'int' object has no attribute 'decode'
2025-06-24 16:14:00 | ERROR    | app.utils.email_processor | 处理邮件主题筛选时出错: 'int' object has no attribute 'decode'
2025-06-24 16:14:00 | ERROR    | app.utils.email_processor | 处理邮件主题筛选时出错: 'int' object has no attribute 'decode'
2025-06-24 16:14:00 | ERROR    | app.utils.email_processor | 处理邮件主题筛选时出错: 'int' object has no attribute 'decode'
2025-06-24 16:14:00 | ERROR    | app.utils.email_processor | 处理邮件主题筛选时出错: 'int' object has no attribute 'decode'
2025-06-24 16:14:00 | ERROR    | app.utils.email_processor | 处理邮件主题筛选时出错: 'int' object has no attribute 'decode'
2025-06-24 16:14:00 | ERROR    | app.utils.email_processor | 处理邮件主题筛选时出错: 'int' object has no attribute 'decode'
2025-06-24 16:14:01 | ERROR    | app.utils.email_processor | 处理邮件主题筛选时出错: 'int' object has no attribute 'decode'
2025-06-24 16:14:01 | ERROR    | app.utils.email_processor | 处理邮件主题筛选时出错: 'int' object has no attribute 'decode'
2025-06-24 16:14:02 | ERROR    | app.utils.email_processor | 处理邮件主题筛选时出错: 'int' object has no attribute 'decode'
2025-06-24 16:14:02 | ERROR    | app.utils.email_processor | 处理邮件主题筛选时出错: 'int' object has no attribute 'decode'
2025-06-24 16:14:02 | ERROR    | app.utils.email_processor | 处理邮件主题筛选时出错: 'int' object has no attribute 'decode'
2025-06-24 16:14:02 | ERROR    | app.utils.email_processor | 处理邮件主题筛选时出错: 'int' object has no attribute 'decode'
2025-06-24 16:14:02 | ERROR    | app.utils.email_processor | 处理邮件主题筛选时出错: 'int' object has no attribute 'decode'
2025-06-24 16:14:02 | ERROR    | app.utils.email_processor | 处理邮件主题筛选时出错: 'int' object has no attribute 'decode'
2025-06-24 16:14:02 | ERROR    | app.utils.email_processor | 处理邮件主题筛选时出错: 'int' object has no attribute 'decode'
2025-06-24 16:14:02 | ERROR    | app.utils.email_processor | 处理邮件主题筛选时出错: 'int' object has no attribute 'decode'
2025-06-24 16:14:03 | ERROR    | app.utils.email_processor | 处理邮件主题筛选时出错: 'int' object has no attribute 'decode'
2025-06-24 16:14:04 | ERROR    | app.utils.email_processor | 处理邮件主题筛选时出错: 'int' object has no attribute 'decode'
2025-06-24 16:14:04 | ERROR    | app.utils.email_processor | 处理邮件主题筛选时出错: 'int' object has no attribute 'decode'
2025-06-24 16:14:04 | ERROR    | app.utils.email_processor | 处理邮件主题筛选时出错: 'int' object has no attribute 'decode'
2025-06-24 16:14:04 | ERROR    | app.utils.email_processor | 处理邮件主题筛选时出错: 'int' object has no attribute 'decode'
2025-06-24 16:14:04 | ERROR    | app.utils.email_processor | 处理邮件主题筛选时出错: 'int' object has no attribute 'decode'
2025-06-24 16:14:04 | ERROR    | app.utils.email_processor | 处理邮件主题筛选时出错: 'int' object has no attribute 'decode'
2025-06-24 16:14:04 | ERROR    | app.utils.email_processor | 处理邮件主题筛选时出错: 'int' object has no attribute 'decode'
2025-06-24 16:14:05 | ERROR    | app.utils.email_processor | 处理邮件主题筛选时出错: 'int' object has no attribute 'decode'
2025-06-24 16:14:05 | ERROR    | app.utils.email_processor | 处理邮件主题筛选时出错: 'int' object has no attribute 'decode'
2025-06-24 16:14:06 | ERROR    | app.utils.email_processor | 处理邮件主题筛选时出错: 'int' object has no attribute 'decode'
2025-06-24 16:14:06 | ERROR    | app.utils.email_processor | 处理邮件主题筛选时出错: 'int' object has no attribute 'decode'
2025-06-24 16:14:06 | ERROR    | app.utils.email_processor | 处理邮件主题筛选时出错: 'int' object has no attribute 'decode'
2025-06-24 16:14:07 | ERROR    | app.utils.email_processor | 处理邮件主题筛选时出错: 'int' object has no attribute 'decode'
2025-06-24 16:14:07 | ERROR    | app.utils.email_processor | 处理邮件主题筛选时出错: 'int' object has no attribute 'decode'
2025-06-24 16:14:07 | ERROR    | app.utils.email_processor | 处理邮件主题筛选时出错: 'int' object has no attribute 'decode'
2025-06-24 16:14:07 | ERROR    | app.utils.email_processor | 处理邮件主题筛选时出错: 'int' object has no attribute 'decode'
2025-06-24 16:14:07 | ERROR    | app.utils.email_processor | 处理邮件主题筛选时出错: 'int' object has no attribute 'decode'
2025-06-24 16:14:07 | ERROR    | app.utils.email_processor | 处理邮件主题筛选时出错: 'int' object has no attribute 'decode'
2025-06-24 16:14:07 | ERROR    | app.utils.email_processor | 处理邮件主题筛选时出错: 'int' object has no attribute 'decode'
2025-06-24 16:14:08 | ERROR    | app.utils.email_processor | 处理邮件主题筛选时出错: 'int' object has no attribute 'decode'
2025-06-24 16:14:08 | ERROR    | app.utils.email_processor | 处理邮件主题筛选时出错: 'int' object has no attribute 'decode'
2025-06-24 16:14:08 | ERROR    | app.utils.email_processor | 处理邮件主题筛选时出错: 'int' object has no attribute 'decode'
2025-06-24 16:14:08 | ERROR    | app.utils.email_processor | 处理邮件主题筛选时出错: 'int' object has no attribute 'decode'
2025-06-24 16:14:08 | ERROR    | app.utils.email_processor | 处理邮件主题筛选时出错: 'int' object has no attribute 'decode'
2025-06-24 16:14:09 | ERROR    | app.utils.email_processor | 处理邮件主题筛选时出错: 'int' object has no attribute 'decode'
2025-06-24 16:14:09 | ERROR    | app.utils.email_processor | 处理邮件主题筛选时出错: 'int' object has no attribute 'decode'
2025-06-24 16:14:10 | ERROR    | app.utils.email_processor | 处理邮件主题筛选时出错: 'int' object has no attribute 'decode'
2025-06-24 16:14:10 | ERROR    | app.utils.email_processor | 处理邮件主题筛选时出错: 'int' object has no attribute 'decode'
2025-06-24 16:14:10 | ERROR    | app.utils.email_processor | 处理邮件主题筛选时出错: 'int' object has no attribute 'decode'
2025-06-24 16:14:10 | ERROR    | app.utils.email_processor | 处理邮件主题筛选时出错: 'int' object has no attribute 'decode'
2025-06-24 16:14:10 | ERROR    | app.utils.email_processor | 处理邮件主题筛选时出错: 'int' object has no attribute 'decode'
2025-06-24 16:14:10 | ERROR    | app.utils.email_processor | 处理邮件主题筛选时出错: 'int' object has no attribute 'decode'
2025-06-24 16:14:10 | ERROR    | app.utils.email_processor | 处理邮件主题筛选时出错: 'int' object has no attribute 'decode'
2025-06-24 16:14:11 | ERROR    | app.utils.email_processor | 处理邮件主题筛选时出错: 'int' object has no attribute 'decode'
2025-06-24 16:14:12 | ERROR    | app.utils.email_processor | 处理邮件主题筛选时出错: 'int' object has no attribute 'decode'
2025-06-24 16:14:12 | ERROR    | app.utils.email_processor | 处理邮件主题筛选时出错: 'int' object has no attribute 'decode'
2025-06-24 16:14:12 | ERROR    | app.utils.email_processor | 处理邮件主题筛选时出错: 'int' object has no attribute 'decode'
2025-06-24 16:14:12 | ERROR    | app.utils.email_processor | 处理邮件主题筛选时出错: 'int' object has no attribute 'decode'
2025-06-24 16:14:12 | ERROR    | app.utils.email_processor | 处理邮件主题筛选时出错: 'int' object has no attribute 'decode'
2025-06-24 16:14:13 | ERROR    | app.utils.email_processor | 处理邮件主题筛选时出错: 'int' object has no attribute 'decode'
2025-06-24 16:14:14 | ERROR    | app.utils.email_processor | 处理邮件主题筛选时出错: 'int' object has no attribute 'decode'
2025-06-24 16:14:14 | ERROR    | app.utils.email_processor | 处理邮件主题筛选时出错: 'int' object has no attribute 'decode'
2025-06-24 16:14:14 | ERROR    | app.utils.email_processor | 处理邮件主题筛选时出错: 'int' object has no attribute 'decode'
2025-06-24 16:14:14 | ERROR    | app.utils.email_processor | 处理邮件主题筛选时出错: 'int' object has no attribute 'decode'
2025-06-24 16:14:14 | ERROR    | app.utils.email_processor | 处理邮件主题筛选时出错: 'int' object has no attribute 'decode'
2025-06-24 16:14:14 | ERROR    | app.utils.email_processor | 处理邮件主题筛选时出错: 'int' object has no attribute 'decode'
2025-06-24 16:14:14 | ERROR    | app.utils.email_processor | 处理邮件主题筛选时出错: 'int' object has no attribute 'decode'
2025-06-24 16:14:14 | ERROR    | app.utils.email_processor | 处理邮件主题筛选时出错: 'int' object has no attribute 'decode'
2025-06-24 16:14:15 | ERROR    | app.utils.email_processor | 处理邮件主题筛选时出错: 'int' object has no attribute 'decode'
2025-06-24 16:14:15 | ERROR    | app.utils.email_processor | 处理邮件主题筛选时出错: 'int' object has no attribute 'decode'
2025-06-24 16:14:15 | ERROR    | app.utils.email_processor | 处理邮件主题筛选时出错: 'int' object has no attribute 'decode'
2025-06-24 16:14:16 | ERROR    | app.utils.email_processor | 处理邮件主题筛选时出错: 'int' object has no attribute 'decode'
2025-06-24 16:14:16 | ERROR    | app.utils.email_processor | 处理邮件主题筛选时出错: 'int' object has no attribute 'decode'
2025-06-24 16:14:16 | ERROR    | app.utils.email_processor | 处理邮件主题筛选时出错: 'int' object has no attribute 'decode'
2025-06-24 16:14:16 | ERROR    | app.utils.email_processor | 处理邮件主题筛选时出错: 'int' object has no attribute 'decode'
2025-06-24 16:14:17 | ERROR    | app.utils.email_processor | 处理邮件主题筛选时出错: 'int' object has no attribute 'decode'
2025-06-24 16:14:17 | ERROR    | app.utils.email_processor | 处理邮件主题筛选时出错: 'int' object has no attribute 'decode'
2025-06-24 16:14:17 | ERROR    | app.utils.email_processor | 处理邮件主题筛选时出错: 'int' object has no attribute 'decode'
2025-06-24 16:14:17 | ERROR    | app.utils.email_processor | 处理邮件主题筛选时出错: 'int' object has no attribute 'decode'
2025-06-24 16:14:17 | ERROR    | app.utils.email_processor | 处理邮件主题筛选时出错: 'int' object has no attribute 'decode'
2025-06-24 16:14:17 | ERROR    | app.utils.email_processor | 处理邮件主题筛选时出错: 'int' object has no attribute 'decode'
2025-06-24 16:14:18 | ERROR    | app.utils.email_processor | 处理邮件主题筛选时出错: 'int' object has no attribute 'decode'
2025-06-24 16:14:18 | ERROR    | app.utils.email_processor | 处理邮件主题筛选时出错: 'int' object has no attribute 'decode'
2025-06-24 16:14:18 | ERROR    | app.utils.email_processor | 处理邮件主题筛选时出错: 'int' object has no attribute 'decode'
2025-06-24 16:14:18 | ERROR    | app.utils.email_processor | 处理邮件主题筛选时出错: 'int' object has no attribute 'decode'
2025-06-24 16:14:18 | ERROR    | app.utils.email_processor | 处理邮件主题筛选时出错: 'int' object has no attribute 'decode'
2025-06-24 16:14:18 | ERROR    | app.utils.email_processor | 处理邮件主题筛选时出错: 'int' object has no attribute 'decode'
2025-06-24 16:14:19 | ERROR    | app.utils.email_processor | 处理邮件主题筛选时出错: 'int' object has no attribute 'decode'
2025-06-24 16:14:19 | ERROR    | app.utils.email_processor | 处理邮件主题筛选时出错: 'int' object has no attribute 'decode'
2025-06-24 16:14:19 | ERROR    | app.utils.email_processor | 处理邮件主题筛选时出错: 'int' object has no attribute 'decode'
2025-06-24 16:14:20 | ERROR    | app.utils.email_processor | 处理邮件主题筛选时出错: 'int' object has no attribute 'decode'
2025-06-24 16:14:20 | ERROR    | app.utils.email_processor | 处理邮件主题筛选时出错: 'int' object has no attribute 'decode'
2025-06-24 16:14:21 | ERROR    | app.utils.email_processor | 处理邮件主题筛选时出错: 'int' object has no attribute 'decode'
2025-06-24 16:14:21 | ERROR    | app.utils.email_processor | 处理邮件主题筛选时出错: 'int' object has no attribute 'decode'
2025-06-24 16:14:22 | ERROR    | app.utils.email_processor | 处理邮件主题筛选时出错: 'int' object has no attribute 'decode'
2025-06-24 16:14:22 | ERROR    | app.utils.email_processor | 处理邮件主题筛选时出错: 'int' object has no attribute 'decode'
2025-06-24 16:15:02 | ERROR    | app.utils.email_processor | 处理邮件时出错: 'int' object has no attribute 'decode'
2025-06-24 16:15:02 | ERROR    | app.utils.email_processor | 处理邮件时出错: 'int' object has no attribute 'decode'
2025-06-24 16:15:46 | ERROR    | app.utils.email_processor | 处理邮件时出错: 'int' object has no attribute 'decode'
2025-06-24 16:16:01 | ERROR    | app.utils.email_processor | 处理邮件时出错: 'int' object has no attribute 'decode'
2025-06-24 16:16:01 | ERROR    | app.utils.email_processor | 处理邮件时出错: 'int' object has no attribute 'decode'
2025-06-24 16:16:01 | ERROR    | app.utils.email_processor | 处理邮件时出错: 'int' object has no attribute 'decode'
2025-06-24 16:16:02 | ERROR    | app.utils.email_processor | 处理邮件时出错: 'int' object has no attribute 'decode'
2025-06-24 16:16:02 | ERROR    | app.utils.email_processor | 处理邮件时出错: 'int' object has no attribute 'decode'
2025-06-24 16:16:02 | ERROR    | app.utils.email_processor | 处理邮件时出错: 'int' object has no attribute 'decode'
2025-06-24 16:16:02 | ERROR    | app.utils.email_processor | 处理邮件时出错: 'int' object has no attribute 'decode'
2025-06-24 16:16:02 | ERROR    | app.utils.email_processor | 处理邮件时出错: 'int' object has no attribute 'decode'
2025-06-24 16:16:04 | ERROR    | app.utils.email_processor | 处理邮件时出错: 'int' object has no attribute 'decode'
2025-06-24 16:16:04 | ERROR    | app.utils.email_processor | 处理邮件时出错: 'int' object has no attribute 'decode'
2025-06-24 16:16:04 | ERROR    | app.utils.email_processor | 处理邮件时出错: 'int' object has no attribute 'decode'
2025-06-24 16:16:05 | ERROR    | app.utils.email_processor | 处理邮件时出错: 'int' object has no attribute 'decode'
2025-06-24 16:16:05 | ERROR    | app.utils.email_processor | 处理邮件时出错: 'int' object has no attribute 'decode'
2025-06-24 16:16:10 | ERROR    | app.utils.email_processor | 处理邮件时出错: 'int' object has no attribute 'decode'
2025-06-24 16:16:13 | ERROR    | app.utils.email_processor | 处理邮件时出错: 'int' object has no attribute 'decode'
2025-06-24 16:16:13 | ERROR    | app.utils.email_processor | 处理邮件时出错: 'int' object has no attribute 'decode'
2025-06-24 16:16:13 | ERROR    | app.utils.email_processor | 处理邮件时出错: 'int' object has no attribute 'decode'
2025-06-24 16:17:35 | ERROR    | app.utils.email_processor | 处理邮件主题筛选时出错: 'int' object has no attribute 'decode'
2025-06-24 16:17:35 | ERROR    | app.utils.email_processor | 处理邮件主题筛选时出错: 'int' object has no attribute 'decode'
2025-06-24 16:17:36 | ERROR    | app.utils.email_processor | 处理邮件主题筛选时出错: 'int' object has no attribute 'decode'
2025-06-24 16:17:36 | ERROR    | app.utils.email_processor | 处理邮件主题筛选时出错: 'int' object has no attribute 'decode'
2025-06-24 16:17:36 | ERROR    | app.utils.email_processor | 处理邮件主题筛选时出错: 'int' object has no attribute 'decode'
2025-06-24 16:17:37 | ERROR    | app.utils.email_processor | 处理邮件主题筛选时出错: 'int' object has no attribute 'decode'
2025-06-24 16:17:37 | ERROR    | app.utils.email_processor | 处理邮件主题筛选时出错: 'int' object has no attribute 'decode'
2025-06-24 16:17:37 | ERROR    | app.utils.email_processor | 处理邮件主题筛选时出错: 'int' object has no attribute 'decode'
2025-06-24 16:17:37 | ERROR    | app.utils.email_processor | 处理邮件主题筛选时出错: 'int' object has no attribute 'decode'
2025-06-24 16:17:37 | ERROR    | app.utils.email_processor | 处理邮件主题筛选时出错: 'int' object has no attribute 'decode'
2025-06-24 16:17:37 | ERROR    | app.utils.email_processor | 处理邮件主题筛选时出错: 'int' object has no attribute 'decode'
2025-06-24 16:17:37 | ERROR    | app.utils.email_processor | 处理邮件主题筛选时出错: 'int' object has no attribute 'decode'
2025-06-24 16:17:37 | ERROR    | app.utils.email_processor | 处理邮件主题筛选时出错: 'int' object has no attribute 'decode'
2025-06-24 16:17:37 | ERROR    | app.utils.email_processor | 处理邮件主题筛选时出错: 'int' object has no attribute 'decode'
2025-06-24 16:17:38 | ERROR    | app.utils.email_processor | 处理邮件主题筛选时出错: 'int' object has no attribute 'decode'
2025-06-24 16:17:38 | ERROR    | app.utils.email_processor | 处理邮件主题筛选时出错: 'int' object has no attribute 'decode'
2025-06-24 16:17:39 | ERROR    | app.utils.email_processor | 处理邮件主题筛选时出错: 'int' object has no attribute 'decode'
2025-06-24 16:17:39 | ERROR    | app.utils.email_processor | 处理邮件主题筛选时出错: 'int' object has no attribute 'decode'
2025-06-24 16:17:39 | ERROR    | app.utils.email_processor | 处理邮件主题筛选时出错: 'int' object has no attribute 'decode'
2025-06-24 16:17:39 | ERROR    | app.utils.email_processor | 处理邮件主题筛选时出错: 'int' object has no attribute 'decode'
2025-06-24 16:17:39 | ERROR    | app.utils.email_processor | 处理邮件主题筛选时出错: 'int' object has no attribute 'decode'
2025-06-24 16:17:40 | ERROR    | app.utils.email_processor | 处理邮件主题筛选时出错: 'int' object has no attribute 'decode'
2025-06-24 16:17:40 | ERROR    | app.utils.email_processor | 处理邮件主题筛选时出错: 'int' object has no attribute 'decode'
2025-06-24 16:17:41 | ERROR    | app.utils.email_processor | 处理邮件主题筛选时出错: 'int' object has no attribute 'decode'
2025-06-24 16:17:42 | ERROR    | app.utils.email_processor | 处理邮件主题筛选时出错: 'int' object has no attribute 'decode'
2025-06-24 16:17:42 | ERROR    | app.utils.email_processor | 处理邮件主题筛选时出错: 'int' object has no attribute 'decode'
2025-06-24 16:17:42 | ERROR    | app.utils.email_processor | 处理邮件主题筛选时出错: 'int' object has no attribute 'decode'
2025-06-24 16:17:42 | ERROR    | app.utils.email_processor | 处理邮件主题筛选时出错: 'int' object has no attribute 'decode'
2025-06-24 16:17:42 | ERROR    | app.utils.email_processor | 处理邮件主题筛选时出错: 'int' object has no attribute 'decode'
2025-06-24 16:17:42 | ERROR    | app.utils.email_processor | 处理邮件主题筛选时出错: 'int' object has no attribute 'decode'
2025-06-24 16:17:42 | ERROR    | app.utils.email_processor | 处理邮件主题筛选时出错: 'int' object has no attribute 'decode'
2025-06-24 16:17:42 | ERROR    | app.utils.email_processor | 处理邮件主题筛选时出错: 'int' object has no attribute 'decode'
2025-06-24 16:17:42 | ERROR    | app.utils.email_processor | 处理邮件主题筛选时出错: 'int' object has no attribute 'decode'
2025-06-24 16:17:42 | ERROR    | app.utils.email_processor | 处理邮件主题筛选时出错: 'int' object has no attribute 'decode'
2025-06-24 16:17:42 | ERROR    | app.utils.email_processor | 处理邮件主题筛选时出错: 'int' object has no attribute 'decode'
2025-06-24 16:17:43 | ERROR    | app.utils.email_processor | 处理邮件主题筛选时出错: 'int' object has no attribute 'decode'
2025-06-24 16:17:43 | ERROR    | app.utils.email_processor | 处理邮件主题筛选时出错: 'int' object has no attribute 'decode'
2025-06-24 16:17:43 | ERROR    | app.utils.email_processor | 处理邮件主题筛选时出错: 'int' object has no attribute 'decode'
2025-06-24 16:17:43 | ERROR    | app.utils.email_processor | 处理邮件主题筛选时出错: 'int' object has no attribute 'decode'
2025-06-24 16:17:43 | ERROR    | app.utils.email_processor | 处理邮件主题筛选时出错: 'int' object has no attribute 'decode'
2025-06-24 16:17:43 | ERROR    | app.utils.email_processor | 处理邮件主题筛选时出错: 'int' object has no attribute 'decode'
2025-06-24 16:17:43 | ERROR    | app.utils.email_processor | 处理邮件主题筛选时出错: 'int' object has no attribute 'decode'
2025-06-24 16:17:43 | ERROR    | app.utils.email_processor | 处理邮件主题筛选时出错: 'int' object has no attribute 'decode'
2025-06-24 16:17:43 | ERROR    | app.utils.email_processor | 处理邮件主题筛选时出错: 'int' object has no attribute 'decode'
2025-06-24 16:17:44 | ERROR    | app.utils.email_processor | 处理邮件主题筛选时出错: 'int' object has no attribute 'decode'
2025-06-24 16:17:44 | ERROR    | app.utils.email_processor | 处理邮件主题筛选时出错: 'int' object has no attribute 'decode'
2025-06-24 16:17:44 | ERROR    | app.utils.email_processor | 处理邮件主题筛选时出错: 'int' object has no attribute 'decode'
2025-06-24 16:17:44 | ERROR    | app.utils.email_processor | 处理邮件主题筛选时出错: 'int' object has no attribute 'decode'
2025-06-24 16:17:44 | ERROR    | app.utils.email_processor | 处理邮件主题筛选时出错: 'int' object has no attribute 'decode'
2025-06-24 16:17:44 | ERROR    | app.utils.email_processor | 处理邮件主题筛选时出错: 'int' object has no attribute 'decode'
2025-06-24 16:17:45 | ERROR    | app.utils.email_processor | 处理邮件主题筛选时出错: 'int' object has no attribute 'decode'
2025-06-24 16:17:45 | ERROR    | app.utils.email_processor | 处理邮件主题筛选时出错: 'int' object has no attribute 'decode'
2025-06-24 16:17:45 | ERROR    | app.utils.email_processor | 处理邮件主题筛选时出错: 'int' object has no attribute 'decode'
2025-06-24 16:17:45 | ERROR    | app.utils.email_processor | 处理邮件主题筛选时出错: 'int' object has no attribute 'decode'
2025-06-24 16:17:45 | ERROR    | app.utils.email_processor | 处理邮件主题筛选时出错: 'int' object has no attribute 'decode'
2025-06-24 16:17:46 | ERROR    | app.utils.email_processor | 处理邮件主题筛选时出错: 'int' object has no attribute 'decode'
2025-06-24 16:17:47 | ERROR    | app.utils.email_processor | 处理邮件主题筛选时出错: 'int' object has no attribute 'decode'
2025-06-24 16:17:47 | ERROR    | app.utils.email_processor | 处理邮件主题筛选时出错: 'int' object has no attribute 'decode'
2025-06-24 16:17:47 | ERROR    | app.utils.email_processor | 处理邮件主题筛选时出错: 'int' object has no attribute 'decode'
2025-06-24 16:17:47 | ERROR    | app.utils.email_processor | 处理邮件主题筛选时出错: 'int' object has no attribute 'decode'
2025-06-24 16:17:47 | ERROR    | app.utils.email_processor | 处理邮件主题筛选时出错: 'int' object has no attribute 'decode'
2025-06-24 16:17:47 | ERROR    | app.utils.email_processor | 处理邮件主题筛选时出错: 'int' object has no attribute 'decode'
2025-06-24 16:17:47 | ERROR    | app.utils.email_processor | 处理邮件主题筛选时出错: 'int' object has no attribute 'decode'
2025-06-24 16:17:47 | ERROR    | app.utils.email_processor | 处理邮件主题筛选时出错: 'int' object has no attribute 'decode'
2025-06-24 16:17:47 | ERROR    | app.utils.email_processor | 处理邮件主题筛选时出错: 'int' object has no attribute 'decode'
2025-06-24 16:17:47 | ERROR    | app.utils.email_processor | 处理邮件主题筛选时出错: 'int' object has no attribute 'decode'
2025-06-24 16:17:47 | ERROR    | app.utils.email_processor | 处理邮件主题筛选时出错: 'int' object has no attribute 'decode'
2025-06-24 16:17:47 | ERROR    | app.utils.email_processor | 处理邮件主题筛选时出错: 'int' object has no attribute 'decode'
2025-06-24 16:17:47 | ERROR    | app.utils.email_processor | 处理邮件主题筛选时出错: 'int' object has no attribute 'decode'
2025-06-24 16:17:48 | ERROR    | app.utils.email_processor | 处理邮件主题筛选时出错: 'int' object has no attribute 'decode'
2025-06-24 16:17:50 | ERROR    | app.utils.email_processor | 处理邮件主题筛选时出错: 'int' object has no attribute 'decode'
2025-06-24 16:17:50 | ERROR    | app.utils.email_processor | 处理邮件主题筛选时出错: 'int' object has no attribute 'decode'
2025-06-24 16:17:50 | ERROR    | app.utils.email_processor | 处理邮件主题筛选时出错: 'int' object has no attribute 'decode'
2025-06-24 16:17:51 | ERROR    | app.utils.email_processor | 处理邮件主题筛选时出错: 'int' object has no attribute 'decode'
2025-06-24 16:17:51 | ERROR    | app.utils.email_processor | 处理邮件主题筛选时出错: 'int' object has no attribute 'decode'
2025-06-24 16:17:51 | ERROR    | app.utils.email_processor | 处理邮件主题筛选时出错: 'int' object has no attribute 'decode'
2025-06-24 16:17:52 | ERROR    | app.utils.email_processor | 处理邮件主题筛选时出错: 'int' object has no attribute 'decode'
2025-06-24 16:17:52 | ERROR    | app.utils.email_processor | 处理邮件主题筛选时出错: 'int' object has no attribute 'decode'
2025-06-24 16:17:52 | ERROR    | app.utils.email_processor | 处理邮件主题筛选时出错: 'int' object has no attribute 'decode'
2025-06-24 16:17:52 | ERROR    | app.utils.email_processor | 处理邮件主题筛选时出错: 'int' object has no attribute 'decode'
2025-06-24 16:17:52 | ERROR    | app.utils.email_processor | 处理邮件主题筛选时出错: 'int' object has no attribute 'decode'
2025-06-24 16:17:53 | ERROR    | app.utils.email_processor | 处理邮件主题筛选时出错: 'int' object has no attribute 'decode'
2025-06-24 16:17:53 | ERROR    | app.utils.email_processor | 处理邮件主题筛选时出错: 'int' object has no attribute 'decode'
2025-06-24 16:17:53 | ERROR    | app.utils.email_processor | 处理邮件主题筛选时出错: 'int' object has no attribute 'decode'
2025-06-24 16:17:53 | ERROR    | app.utils.email_processor | 处理邮件主题筛选时出错: 'int' object has no attribute 'decode'
2025-06-24 16:17:53 | ERROR    | app.utils.email_processor | 处理邮件主题筛选时出错: 'int' object has no attribute 'decode'
2025-06-24 16:17:53 | ERROR    | app.utils.email_processor | 处理邮件主题筛选时出错: 'int' object has no attribute 'decode'
2025-06-24 16:17:53 | ERROR    | app.utils.email_processor | 处理邮件主题筛选时出错: 'int' object has no attribute 'decode'
2025-06-24 16:17:53 | ERROR    | app.utils.email_processor | 处理邮件主题筛选时出错: 'int' object has no attribute 'decode'
2025-06-24 16:17:53 | ERROR    | app.utils.email_processor | 处理邮件主题筛选时出错: 'int' object has no attribute 'decode'
2025-06-24 16:17:53 | ERROR    | app.utils.email_processor | 处理邮件主题筛选时出错: 'int' object has no attribute 'decode'
2025-06-24 16:17:53 | ERROR    | app.utils.email_processor | 处理邮件主题筛选时出错: 'int' object has no attribute 'decode'
2025-06-24 16:17:53 | ERROR    | app.utils.email_processor | 处理邮件主题筛选时出错: 'int' object has no attribute 'decode'
2025-06-24 16:17:53 | ERROR    | app.utils.email_processor | 处理邮件主题筛选时出错: 'int' object has no attribute 'decode'
2025-06-24 16:17:54 | ERROR    | app.utils.email_processor | 处理邮件主题筛选时出错: 'int' object has no attribute 'decode'
2025-06-24 16:17:54 | ERROR    | app.utils.email_processor | 处理邮件主题筛选时出错: 'int' object has no attribute 'decode'
2025-06-24 16:17:54 | ERROR    | app.utils.email_processor | 处理邮件主题筛选时出错: 'int' object has no attribute 'decode'
2025-06-24 16:17:55 | ERROR    | app.utils.email_processor | 处理邮件主题筛选时出错: 'int' object has no attribute 'decode'
2025-06-24 16:17:55 | ERROR    | app.utils.email_processor | 处理邮件主题筛选时出错: 'int' object has no attribute 'decode'
2025-06-24 16:17:55 | ERROR    | app.utils.email_processor | 处理邮件主题筛选时出错: 'int' object has no attribute 'decode'
2025-06-24 16:17:59 | ERROR    | app.utils.email_processor | 处理邮件主题筛选时出错: 'int' object has no attribute 'decode'
2025-06-24 16:17:59 | ERROR    | app.utils.email_processor | 处理邮件主题筛选时出错: 'int' object has no attribute 'decode'
2025-06-24 16:17:59 | ERROR    | app.utils.email_processor | 处理邮件主题筛选时出错: 'int' object has no attribute 'decode'
2025-06-24 16:17:59 | ERROR    | app.utils.email_processor | 处理邮件主题筛选时出错: 'int' object has no attribute 'decode'
2025-06-24 16:17:59 | ERROR    | app.utils.email_processor | 处理邮件主题筛选时出错: 'int' object has no attribute 'decode'
2025-06-24 16:17:59 | ERROR    | app.utils.email_processor | 处理邮件主题筛选时出错: 'int' object has no attribute 'decode'
2025-06-24 16:18:01 | ERROR    | app.utils.email_processor | 处理邮件主题筛选时出错: 'int' object has no attribute 'decode'
2025-06-24 16:18:01 | ERROR    | app.utils.email_processor | 处理邮件主题筛选时出错: 'int' object has no attribute 'decode'
2025-06-24 16:18:01 | ERROR    | app.utils.email_processor | 处理邮件主题筛选时出错: 'int' object has no attribute 'decode'
2025-06-24 16:18:01 | ERROR    | app.utils.email_processor | 处理邮件主题筛选时出错: 'int' object has no attribute 'decode'
2025-06-24 16:18:01 | ERROR    | app.utils.email_processor | 处理邮件主题筛选时出错: 'int' object has no attribute 'decode'
2025-06-24 16:18:01 | ERROR    | app.utils.email_processor | 处理邮件主题筛选时出错: 'int' object has no attribute 'decode'
2025-06-24 16:18:01 | ERROR    | app.utils.email_processor | 处理邮件主题筛选时出错: 'int' object has no attribute 'decode'
2025-06-24 16:18:01 | ERROR    | app.utils.email_processor | 处理邮件主题筛选时出错: 'int' object has no attribute 'decode'
2025-06-24 16:18:01 | ERROR    | app.utils.email_processor | 处理邮件主题筛选时出错: 'int' object has no attribute 'decode'
2025-06-24 16:18:01 | ERROR    | app.utils.email_processor | 处理邮件主题筛选时出错: 'int' object has no attribute 'decode'
2025-06-24 16:18:02 | ERROR    | app.utils.email_processor | 处理邮件主题筛选时出错: 'int' object has no attribute 'decode'
2025-06-24 16:18:02 | ERROR    | app.utils.email_processor | 处理邮件主题筛选时出错: 'int' object has no attribute 'decode'
2025-06-24 16:18:02 | ERROR    | app.utils.email_processor | 处理邮件主题筛选时出错: 'int' object has no attribute 'decode'
2025-06-24 16:18:02 | ERROR    | app.utils.email_processor | 处理邮件主题筛选时出错: 'int' object has no attribute 'decode'
2025-06-24 16:18:02 | ERROR    | app.utils.email_processor | 处理邮件主题筛选时出错: 'int' object has no attribute 'decode'
2025-06-24 16:18:02 | ERROR    | app.utils.email_processor | 处理邮件主题筛选时出错: 'int' object has no attribute 'decode'
2025-06-24 16:18:02 | ERROR    | app.utils.email_processor | 处理邮件主题筛选时出错: 'int' object has no attribute 'decode'
2025-06-24 16:18:02 | ERROR    | app.utils.email_processor | 处理邮件主题筛选时出错: 'int' object has no attribute 'decode'
2025-06-24 16:18:02 | ERROR    | app.utils.email_processor | 处理邮件主题筛选时出错: 'int' object has no attribute 'decode'
2025-06-24 16:18:02 | ERROR    | app.utils.email_processor | 处理邮件主题筛选时出错: 'int' object has no attribute 'decode'
2025-06-24 16:18:02 | ERROR    | app.utils.email_processor | 处理邮件主题筛选时出错: 'int' object has no attribute 'decode'
2025-06-24 16:18:02 | ERROR    | app.utils.email_processor | 处理邮件主题筛选时出错: 'int' object has no attribute 'decode'
2025-06-24 16:18:03 | ERROR    | app.utils.email_processor | 处理邮件主题筛选时出错: 'int' object has no attribute 'decode'
2025-06-24 16:18:03 | ERROR    | app.utils.email_processor | 处理邮件主题筛选时出错: 'int' object has no attribute 'decode'
2025-06-24 16:18:03 | ERROR    | app.utils.email_processor | 处理邮件主题筛选时出错: 'int' object has no attribute 'decode'
2025-06-24 16:18:04 | ERROR    | app.utils.email_processor | 处理邮件主题筛选时出错: 'int' object has no attribute 'decode'
2025-06-24 16:18:04 | ERROR    | app.utils.email_processor | 处理邮件主题筛选时出错: 'int' object has no attribute 'decode'
2025-06-24 16:18:05 | ERROR    | app.utils.email_processor | 处理邮件主题筛选时出错: 'int' object has no attribute 'decode'
2025-06-24 16:18:05 | ERROR    | app.utils.email_processor | 处理邮件主题筛选时出错: 'int' object has no attribute 'decode'
2025-06-24 16:18:05 | ERROR    | app.utils.email_processor | 处理邮件主题筛选时出错: 'int' object has no attribute 'decode'
2025-06-24 16:18:05 | ERROR    | app.utils.email_processor | 处理邮件主题筛选时出错: 'int' object has no attribute 'decode'
2025-06-24 16:18:05 | ERROR    | app.utils.email_processor | 处理邮件主题筛选时出错: 'int' object has no attribute 'decode'
2025-06-24 16:18:05 | ERROR    | app.utils.email_processor | 处理邮件主题筛选时出错: 'int' object has no attribute 'decode'
2025-06-24 16:18:05 | ERROR    | app.utils.email_processor | 处理邮件主题筛选时出错: 'int' object has no attribute 'decode'
2025-06-24 16:22:51 | ERROR    | app.utils.email_processor | 处理邮件主题筛选时出错: 'int' object has no attribute 'decode'
2025-06-24 16:22:51 | ERROR    | app.utils.email_processor | 处理邮件主题筛选时出错: 'int' object has no attribute 'decode'
2025-06-24 16:22:53 | ERROR    | app.utils.email_processor | 处理邮件主题筛选时出错: 'int' object has no attribute 'decode'
2025-06-24 16:22:53 | ERROR    | app.utils.email_processor | 处理邮件主题筛选时出错: 'int' object has no attribute 'decode'
2025-06-24 16:22:53 | ERROR    | app.utils.email_processor | 处理邮件主题筛选时出错: 'int' object has no attribute 'decode'
2025-06-24 16:22:53 | ERROR    | app.utils.email_processor | 处理邮件主题筛选时出错: 'int' object has no attribute 'decode'
2025-06-24 16:22:53 | ERROR    | app.utils.email_processor | 处理邮件主题筛选时出错: 'int' object has no attribute 'decode'
2025-06-24 16:22:53 | ERROR    | app.utils.email_processor | 处理邮件主题筛选时出错: 'int' object has no attribute 'decode'
2025-06-24 16:22:53 | ERROR    | app.utils.email_processor | 处理邮件主题筛选时出错: 'int' object has no attribute 'decode'
2025-06-24 16:22:53 | ERROR    | app.utils.email_processor | 处理邮件主题筛选时出错: 'int' object has no attribute 'decode'
2025-06-24 16:22:53 | ERROR    | app.utils.email_processor | 处理邮件主题筛选时出错: 'int' object has no attribute 'decode'
2025-06-24 16:22:53 | ERROR    | app.utils.email_processor | 处理邮件主题筛选时出错: 'int' object has no attribute 'decode'
2025-06-24 16:22:53 | ERROR    | app.utils.email_processor | 处理邮件主题筛选时出错: 'int' object has no attribute 'decode'
2025-06-24 16:22:53 | ERROR    | app.utils.email_processor | 处理邮件主题筛选时出错: 'int' object has no attribute 'decode'
2025-06-24 16:22:54 | ERROR    | app.utils.email_processor | 处理邮件主题筛选时出错: 'int' object has no attribute 'decode'
2025-06-24 16:22:54 | ERROR    | app.utils.email_processor | 处理邮件主题筛选时出错: 'int' object has no attribute 'decode'
2025-06-24 16:22:56 | ERROR    | app.utils.email_processor | 处理邮件主题筛选时出错: 'int' object has no attribute 'decode'
2025-06-24 16:22:56 | ERROR    | app.utils.email_processor | 处理邮件主题筛选时出错: 'int' object has no attribute 'decode'
2025-06-24 16:22:56 | ERROR    | app.utils.email_processor | 处理邮件主题筛选时出错: 'int' object has no attribute 'decode'
2025-06-24 16:22:56 | ERROR    | app.utils.email_processor | 处理邮件主题筛选时出错: 'int' object has no attribute 'decode'
2025-06-24 16:22:56 | ERROR    | app.utils.email_processor | 处理邮件主题筛选时出错: 'int' object has no attribute 'decode'
2025-06-24 16:22:56 | ERROR    | app.utils.email_processor | 处理邮件主题筛选时出错: 'int' object has no attribute 'decode'
2025-06-24 16:22:56 | ERROR    | app.utils.email_processor | 处理邮件主题筛选时出错: 'int' object has no attribute 'decode'
2025-06-24 16:22:57 | ERROR    | app.utils.email_processor | 处理邮件主题筛选时出错: 'int' object has no attribute 'decode'
2025-06-24 16:22:57 | ERROR    | app | 保存授权码失败: 'EmailConfig' object has no attribute 'description'
2025-06-24 16:22:57 | ERROR    | app | 获取授权码失败: 'EmailConfig' object has no attribute 'description'
2025-06-24 16:23:03 | ERROR    | app.utils.email_processor | 处理邮件主题筛选时出错: 'int' object has no attribute 'decode'
2025-06-24 16:23:03 | ERROR    | app.utils.email_processor | 处理邮件主题筛选时出错: 'int' object has no attribute 'decode'
2025-06-24 16:23:03 | ERROR    | app.utils.email_processor | 处理邮件主题筛选时出错: 'int' object has no attribute 'decode'
2025-06-24 16:23:03 | ERROR    | app.utils.email_processor | 处理邮件时出错: 'int' object has no attribute 'decode'
2025-06-24 16:23:03 | ERROR    | app.utils.email_processor | 处理邮件主题筛选时出错: 'int' object has no attribute 'decode'
2025-06-24 16:23:03 | ERROR    | app.utils.email_processor | 处理邮件主题筛选时出错: 'int' object has no attribute 'decode'
2025-06-24 16:23:03 | ERROR    | app.utils.email_processor | 处理邮件主题筛选时出错: 'int' object has no attribute 'decode'
2025-06-24 16:23:03 | ERROR    | app.utils.email_processor | 处理邮件主题筛选时出错: 'int' object has no attribute 'decode'
2025-06-24 16:23:03 | ERROR    | app.utils.email_processor | 处理邮件主题筛选时出错: 'int' object has no attribute 'decode'
2025-06-24 16:23:03 | ERROR    | app.utils.email_processor | 处理邮件时出错: 'int' object has no attribute 'decode'
2025-06-24 16:23:03 | ERROR    | app.utils.email_processor | 处理邮件主题筛选时出错: 'int' object has no attribute 'decode'
2025-06-24 16:23:03 | ERROR    | app.utils.email_processor | 处理邮件主题筛选时出错: 'int' object has no attribute 'decode'
2025-06-24 16:23:03 | ERROR    | app.utils.email_processor | 处理邮件主题筛选时出错: 'int' object has no attribute 'decode'
2025-06-24 16:23:03 | ERROR    | app.utils.email_processor | 处理邮件主题筛选时出错: 'int' object has no attribute 'decode'
2025-06-24 16:23:05 | ERROR    | app.utils.email_processor | 处理邮件主题筛选时出错: 'int' object has no attribute 'decode'
2025-06-24 16:23:08 | ERROR    | app.utils.email_processor | 处理邮件主题筛选时出错: 'int' object has no attribute 'decode'
2025-06-24 16:23:14 | ERROR    | app.utils.email_processor | 处理邮件主题筛选时出错: 'int' object has no attribute 'decode'
2025-06-24 16:23:14 | ERROR    | app.utils.email_processor | 处理邮件主题筛选时出错: 'int' object has no attribute 'decode'
2025-06-24 16:23:14 | ERROR    | app.utils.email_processor | 处理邮件主题筛选时出错: 'int' object has no attribute 'decode'
2025-06-24 16:23:14 | ERROR    | app.utils.email_processor | 处理邮件主题筛选时出错: 'int' object has no attribute 'decode'
2025-06-24 16:23:15 | ERROR    | app | 保存授权码失败: 'EmailConfig' object has no attribute 'description'
2025-06-24 16:23:20 | ERROR    | app.utils.email_processor | 处理邮件主题筛选时出错: 'int' object has no attribute 'decode'
2025-06-24 16:23:20 | ERROR    | app.utils.email_processor | 处理邮件主题筛选时出错: 'int' object has no attribute 'decode'
2025-06-24 16:23:23 | ERROR    | app.utils.email_processor | 处理邮件主题筛选时出错: 'int' object has no attribute 'decode'
2025-06-24 16:23:23 | ERROR    | app.utils.email_processor | 处理邮件主题筛选时出错: 'int' object has no attribute 'decode'
2025-06-24 16:23:26 | ERROR    | app.utils.email_processor | 处理邮件主题筛选时出错: 'int' object has no attribute 'decode'
2025-06-24 16:23:29 | ERROR    | app.utils.email_processor | 处理邮件主题筛选时出错: 'int' object has no attribute 'decode'
2025-06-24 16:23:29 | ERROR    | app.utils.email_processor | 处理邮件主题筛选时出错: 'int' object has no attribute 'decode'
2025-06-24 16:23:32 | ERROR    | app.utils.email_processor | 处理邮件主题筛选时出错: 'int' object has no attribute 'decode'
2025-06-24 16:23:32 | ERROR    | app.utils.email_processor | 处理邮件主题筛选时出错: 'int' object has no attribute 'decode'
2025-06-24 16:23:32 | ERROR    | app.utils.email_processor | 处理邮件主题筛选时出错: 'int' object has no attribute 'decode'
2025-06-24 16:23:32 | ERROR    | app.utils.email_processor | 处理邮件主题筛选时出错: 'int' object has no attribute 'decode'
2025-06-24 16:23:32 | ERROR    | app.utils.email_processor | 处理邮件主题筛选时出错: 'int' object has no attribute 'decode'
2025-06-24 16:23:35 | ERROR    | app.utils.email_processor | 处理邮件主题筛选时出错: 'int' object has no attribute 'decode'
2025-06-24 16:23:35 | ERROR    | app.utils.email_processor | 处理邮件主题筛选时出错: 'int' object has no attribute 'decode'
2025-06-24 16:23:35 | ERROR    | app.utils.email_processor | 处理邮件主题筛选时出错: 'int' object has no attribute 'decode'
2025-06-24 16:23:35 | ERROR    | app.utils.email_processor | 处理邮件主题筛选时出错: 'int' object has no attribute 'decode'
2025-06-24 16:23:35 | ERROR    | app.utils.email_processor | 处理邮件主题筛选时出错: 'int' object has no attribute 'decode'
2025-06-24 16:23:38 | ERROR    | app.utils.email_processor | 处理邮件主题筛选时出错: 'int' object has no attribute 'decode'
2025-06-24 16:23:38 | ERROR    | app.utils.email_processor | 处理邮件主题筛选时出错: 'int' object has no attribute 'decode'
2025-06-24 16:23:41 | ERROR    | app.utils.email_processor | 处理邮件主题筛选时出错: 'int' object has no attribute 'decode'
2025-06-24 16:23:41 | ERROR    | app.utils.email_processor | 处理邮件主题筛选时出错: 'int' object has no attribute 'decode'
2025-06-24 16:23:41 | ERROR    | app.utils.email_processor | 处理邮件主题筛选时出错: 'int' object has no attribute 'decode'
2025-06-24 16:23:42 | ERROR    | app.utils.email_processor | 处理邮件主题筛选时出错: 'int' object has no attribute 'decode'
2025-06-24 16:23:42 | ERROR    | app.utils.email_processor | 处理邮件主题筛选时出错: 'int' object has no attribute 'decode'
2025-06-24 16:23:42 | ERROR    | app.utils.email_processor | 处理邮件主题筛选时出错: 'int' object has no attribute 'decode'
2025-06-24 16:23:42 | ERROR    | app.utils.email_processor | 处理邮件主题筛选时出错: 'int' object has no attribute 'decode'
2025-06-24 16:23:42 | ERROR    | app.utils.email_processor | 处理邮件主题筛选时出错: 'int' object has no attribute 'decode'
2025-06-24 16:23:43 | ERROR    | app.utils.email_processor | 处理邮件主题筛选时出错: 'int' object has no attribute 'decode'
2025-06-24 16:23:43 | ERROR    | app.utils.email_processor | 处理邮件主题筛选时出错: 'int' object has no attribute 'decode'
2025-06-24 16:23:43 | ERROR    | app.utils.email_processor | 处理邮件主题筛选时出错: 'int' object has no attribute 'decode'
2025-06-24 16:23:47 | ERROR    | app.utils.email_processor | 处理邮件主题筛选时出错: 'int' object has no attribute 'decode'
2025-06-24 16:23:47 | ERROR    | app.utils.email_processor | 处理邮件主题筛选时出错: 'int' object has no attribute 'decode'
2025-06-24 16:23:47 | ERROR    | app.utils.email_processor | 处理邮件主题筛选时出错: 'int' object has no attribute 'decode'
2025-06-24 16:23:48 | ERROR    | app.utils.email_processor | 处理邮件时出错: 'int' object has no attribute 'decode'
2025-06-24 16:23:48 | ERROR    | app.utils.email_processor | 处理邮件主题筛选时出错: 'int' object has no attribute 'decode'
2025-06-24 16:23:48 | ERROR    | app.utils.email_processor | 处理邮件主题筛选时出错: 'int' object has no attribute 'decode'
2025-06-24 16:23:48 | ERROR    | app.utils.email_processor | 处理邮件主题筛选时出错: 'int' object has no attribute 'decode'
2025-06-24 16:23:48 | ERROR    | app.utils.email_processor | 处理邮件主题筛选时出错: 'int' object has no attribute 'decode'
2025-06-24 16:23:48 | ERROR    | app.utils.email_processor | 处理邮件主题筛选时出错: 'int' object has no attribute 'decode'
2025-06-24 16:23:48 | ERROR    | app.utils.email_processor | 处理邮件主题筛选时出错: 'int' object has no attribute 'decode'
2025-06-24 16:23:48 | ERROR    | app.utils.email_processor | 处理邮件主题筛选时出错: 'int' object has no attribute 'decode'
2025-06-24 16:23:48 | ERROR    | app.utils.email_processor | 处理邮件主题筛选时出错: 'int' object has no attribute 'decode'
2025-06-24 16:23:48 | ERROR    | app.utils.email_processor | 处理邮件主题筛选时出错: 'int' object has no attribute 'decode'
2025-06-24 16:23:50 | ERROR    | app.utils.email_processor | 处理邮件主题筛选时出错: 'int' object has no attribute 'decode'
2025-06-24 16:23:50 | ERROR    | app.utils.email_processor | 处理邮件主题筛选时出错: 'int' object has no attribute 'decode'
2025-06-24 16:23:50 | ERROR    | app.utils.email_processor | 处理邮件主题筛选时出错: 'int' object has no attribute 'decode'
2025-06-24 16:23:50 | ERROR    | app.utils.email_processor | 处理邮件主题筛选时出错: 'int' object has no attribute 'decode'
2025-06-24 16:23:50 | ERROR    | app.utils.email_processor | 处理邮件主题筛选时出错: 'int' object has no attribute 'decode'
2025-06-24 16:23:50 | ERROR    | app.utils.email_processor | 处理邮件主题筛选时出错: 'int' object has no attribute 'decode'
2025-06-24 16:23:50 | ERROR    | app.utils.email_processor | 处理邮件主题筛选时出错: 'int' object has no attribute 'decode'
2025-06-24 16:23:50 | ERROR    | app.utils.email_processor | 处理邮件主题筛选时出错: 'int' object has no attribute 'decode'
2025-06-24 16:23:50 | ERROR    | app.utils.email_processor | 处理邮件主题筛选时出错: 'int' object has no attribute 'decode'
2025-06-24 16:23:59 | ERROR    | app.utils.email_processor | 处理邮件主题筛选时出错: 'int' object has no attribute 'decode'
2025-06-24 16:24:02 | ERROR    | app.utils.email_processor | 处理邮件主题筛选时出错: 'int' object has no attribute 'decode'
2025-06-24 16:24:02 | ERROR    | app.utils.email_processor | 处理邮件主题筛选时出错: 'int' object has no attribute 'decode'
2025-06-24 16:24:02 | ERROR    | app.utils.email_processor | 处理邮件主题筛选时出错: 'int' object has no attribute 'decode'
2025-06-24 16:24:02 | ERROR    | app.utils.email_processor | 处理邮件主题筛选时出错: 'int' object has no attribute 'decode'
2025-06-24 16:24:02 | ERROR    | app.utils.email_processor | 处理邮件时出错: 'int' object has no attribute 'decode'
2025-06-24 16:24:02 | ERROR    | app.utils.email_processor | 处理邮件主题筛选时出错: 'int' object has no attribute 'decode'
2025-06-24 16:24:03 | ERROR    | app.utils.email_processor | 处理邮件时出错: 'int' object has no attribute 'decode'
2025-06-24 16:24:03 | ERROR    | app.utils.email_processor | 处理邮件主题筛选时出错: 'int' object has no attribute 'decode'
2025-06-24 16:24:03 | ERROR    | app.utils.email_processor | 处理邮件主题筛选时出错: 'int' object has no attribute 'decode'
2025-06-24 16:24:03 | ERROR    | app.utils.email_processor | 处理邮件时出错: 'int' object has no attribute 'decode'
2025-06-24 16:24:05 | ERROR    | app.utils.email_processor | 处理邮件主题筛选时出错: 'int' object has no attribute 'decode'
2025-06-24 16:24:05 | ERROR    | app.utils.email_processor | 处理邮件主题筛选时出错: 'int' object has no attribute 'decode'
2025-06-24 16:24:05 | ERROR    | app.utils.email_processor | 处理邮件时出错: 'int' object has no attribute 'decode'
2025-06-24 16:24:05 | ERROR    | app.utils.email_processor | 处理邮件主题筛选时出错: 'int' object has no attribute 'decode'
2025-06-24 16:24:05 | ERROR    | app.utils.email_processor | 处理邮件主题筛选时出错: 'int' object has no attribute 'decode'
2025-06-24 16:24:05 | ERROR    | app.utils.email_processor | 处理邮件时出错: 'int' object has no attribute 'decode'
2025-06-24 16:24:05 | ERROR    | app.utils.email_processor | 处理邮件时出错: 'int' object has no attribute 'decode'
2025-06-24 16:24:05 | ERROR    | app.utils.email_processor | 处理邮件主题筛选时出错: 'int' object has no attribute 'decode'
2025-06-24 16:24:05 | ERROR    | app.utils.email_processor | 处理邮件主题筛选时出错: 'int' object has no attribute 'decode'
2025-06-24 16:24:05 | ERROR    | app.utils.email_processor | 处理邮件主题筛选时出错: 'int' object has no attribute 'decode'
2025-06-24 16:24:05 | ERROR    | app.utils.email_processor | 处理邮件主题筛选时出错: 'int' object has no attribute 'decode'
2025-06-24 16:24:05 | ERROR    | app.utils.email_processor | 处理邮件主题筛选时出错: 'int' object has no attribute 'decode'
2025-06-24 16:24:05 | ERROR    | app.utils.email_processor | 处理邮件时出错: 'int' object has no attribute 'decode'
2025-06-24 16:24:05 | ERROR    | app.utils.email_processor | 处理邮件时出错: 'int' object has no attribute 'decode'
2025-06-24 16:24:05 | ERROR    | app.utils.email_processor | 处理邮件主题筛选时出错: 'int' object has no attribute 'decode'
2025-06-24 16:24:06 | ERROR    | app.utils.email_processor | 处理邮件主题筛选时出错: 'int' object has no attribute 'decode'
2025-06-24 16:24:06 | ERROR    | app.utils.email_processor | 处理邮件时出错: 'int' object has no attribute 'decode'
2025-06-24 16:24:06 | ERROR    | app.utils.email_processor | 处理邮件主题筛选时出错: 'int' object has no attribute 'decode'
2025-06-24 16:24:06 | ERROR    | app.utils.email_processor | 处理邮件主题筛选时出错: 'int' object has no attribute 'decode'
2025-06-24 16:24:06 | ERROR    | app.utils.email_processor | 处理邮件时出错: 'int' object has no attribute 'decode'
2025-06-24 16:24:06 | ERROR    | app.utils.email_processor | 处理邮件主题筛选时出错: 'int' object has no attribute 'decode'
2025-06-24 16:24:06 | ERROR    | app.utils.email_processor | 处理邮件时出错: 'int' object has no attribute 'decode'
2025-06-24 16:24:06 | ERROR    | app.utils.email_processor | 处理邮件主题筛选时出错: 'int' object has no attribute 'decode'
2025-06-24 16:24:06 | ERROR    | app.utils.email_processor | 处理邮件主题筛选时出错: 'int' object has no attribute 'decode'
2025-06-24 16:24:06 | ERROR    | app.utils.email_processor | 处理邮件主题筛选时出错: 'int' object has no attribute 'decode'
2025-06-24 16:24:06 | ERROR    | app.utils.email_processor | 处理邮件主题筛选时出错: 'int' object has no attribute 'decode'
2025-06-24 16:24:06 | ERROR    | app.utils.email_processor | 处理邮件主题筛选时出错: 'int' object has no attribute 'decode'
2025-06-24 16:24:06 | ERROR    | app.utils.email_processor | 处理邮件主题筛选时出错: 'int' object has no attribute 'decode'
2025-06-24 16:24:06 | ERROR    | app.utils.email_processor | 处理邮件主题筛选时出错: 'int' object has no attribute 'decode'
2025-06-24 16:24:07 | ERROR    | app.utils.email_processor | 处理邮件时出错: 'int' object has no attribute 'decode'
2025-06-24 16:24:07 | ERROR    | app.utils.email_processor | 处理邮件主题筛选时出错: 'int' object has no attribute 'decode'
2025-06-24 16:24:07 | ERROR    | app.utils.email_processor | 处理邮件时出错: 'int' object has no attribute 'decode'
2025-06-24 16:24:07 | ERROR    | app.utils.email_processor | 处理邮件主题筛选时出错: 'int' object has no attribute 'decode'
2025-06-24 16:24:07 | ERROR    | app.utils.email_processor | 处理邮件主题筛选时出错: 'int' object has no attribute 'decode'
2025-06-24 16:24:07 | ERROR    | app.utils.email_processor | 处理邮件主题筛选时出错: 'int' object has no attribute 'decode'
2025-06-24 16:24:08 | ERROR    | app.utils.email_processor | 处理邮件主题筛选时出错: 'int' object has no attribute 'decode'
2025-06-24 16:24:08 | ERROR    | app.utils.email_processor | 处理邮件主题筛选时出错: 'int' object has no attribute 'decode'
2025-06-24 16:24:08 | ERROR    | app.utils.email_processor | 处理邮件主题筛选时出错: 'int' object has no attribute 'decode'
2025-06-24 16:24:08 | ERROR    | app.utils.email_processor | 处理邮件主题筛选时出错: 'int' object has no attribute 'decode'
2025-06-24 16:24:08 | ERROR    | app.utils.email_processor | 处理邮件主题筛选时出错: 'int' object has no attribute 'decode'
2025-06-24 16:24:08 | ERROR    | app.utils.email_processor | 处理邮件主题筛选时出错: 'int' object has no attribute 'decode'
2025-06-24 16:24:08 | ERROR    | app.utils.email_processor | 处理邮件主题筛选时出错: 'int' object has no attribute 'decode'
2025-06-24 16:24:08 | ERROR    | app.utils.email_processor | 处理邮件主题筛选时出错: 'int' object has no attribute 'decode'
2025-06-24 16:24:08 | ERROR    | app.utils.email_processor | 处理邮件主题筛选时出错: 'int' object has no attribute 'decode'
2025-06-24 16:24:09 | ERROR    | app.utils.email_processor | 处理邮件主题筛选时出错: 'int' object has no attribute 'decode'
2025-06-24 16:24:09 | ERROR    | app.utils.email_processor | 处理邮件主题筛选时出错: 'int' object has no attribute 'decode'
2025-06-24 16:24:11 | ERROR    | app.utils.email_processor | 处理邮件主题筛选时出错: 'int' object has no attribute 'decode'
2025-06-24 16:24:14 | ERROR    | app.utils.email_processor | 处理邮件主题筛选时出错: 'int' object has no attribute 'decode'
2025-06-24 16:24:14 | ERROR    | app.utils.email_processor | 处理邮件主题筛选时出错: 'int' object has no attribute 'decode'
2025-06-24 16:24:14 | ERROR    | app.utils.email_processor | 处理邮件主题筛选时出错: 'int' object has no attribute 'decode'
2025-06-24 16:24:14 | ERROR    | app.utils.email_processor | 处理邮件主题筛选时出错: 'int' object has no attribute 'decode'
2025-06-24 16:24:14 | ERROR    | app.utils.email_processor | 处理邮件主题筛选时出错: 'int' object has no attribute 'decode'
2025-06-24 16:24:14 | ERROR    | app.utils.email_processor | 处理邮件主题筛选时出错: 'int' object has no attribute 'decode'
2025-06-24 16:24:14 | ERROR    | app.utils.email_processor | 处理邮件主题筛选时出错: 'int' object has no attribute 'decode'
2025-06-24 16:24:14 | ERROR    | app.utils.email_processor | 处理邮件主题筛选时出错: 'int' object has no attribute 'decode'
2025-06-24 16:24:15 | ERROR    | app.utils.email_processor | 处理邮件时出错: 'int' object has no attribute 'decode'
2025-06-24 16:24:15 | ERROR    | app.utils.email_processor | 处理邮件主题筛选时出错: 'int' object has no attribute 'decode'
2025-06-24 16:24:15 | ERROR    | app.utils.email_processor | 处理邮件主题筛选时出错: 'int' object has no attribute 'decode'
2025-06-24 16:24:15 | ERROR    | app.utils.email_processor | 处理邮件主题筛选时出错: 'int' object has no attribute 'decode'
2025-06-24 16:24:15 | ERROR    | app.utils.email_processor | 处理邮件时出错: 'int' object has no attribute 'decode'
2025-06-24 16:24:15 | ERROR    | app.utils.email_processor | 处理邮件主题筛选时出错: 'int' object has no attribute 'decode'
2025-06-24 16:24:15 | ERROR    | app.utils.email_processor | 处理邮件主题筛选时出错: 'int' object has no attribute 'decode'
2025-06-24 16:24:15 | ERROR    | app.utils.email_processor | 处理邮件主题筛选时出错: 'int' object has no attribute 'decode'
2025-06-24 16:24:15 | ERROR    | app.utils.email_processor | 处理邮件主题筛选时出错: 'int' object has no attribute 'decode'
2025-06-24 16:24:15 | ERROR    | app.utils.email_processor | 处理邮件主题筛选时出错: 'int' object has no attribute 'decode'
2025-06-24 16:24:16 | ERROR    | app.utils.email_processor | 处理邮件主题筛选时出错: 'int' object has no attribute 'decode'
2025-06-24 16:24:16 | ERROR    | app.utils.email_processor | 处理邮件主题筛选时出错: 'int' object has no attribute 'decode'
2025-06-24 16:24:16 | ERROR    | app.utils.email_processor | 处理邮件主题筛选时出错: 'int' object has no attribute 'decode'
2025-06-24 16:24:16 | ERROR    | app.utils.email_processor | 处理邮件主题筛选时出错: 'int' object has no attribute 'decode'
2025-06-24 16:24:16 | ERROR    | app.utils.email_processor | 处理邮件主题筛选时出错: 'int' object has no attribute 'decode'
2025-06-24 16:24:16 | ERROR    | app.utils.email_processor | 处理邮件主题筛选时出错: 'int' object has no attribute 'decode'
2025-06-24 16:24:17 | ERROR    | app.utils.email_processor | 处理邮件主题筛选时出错: 'int' object has no attribute 'decode'
2025-06-24 16:24:17 | ERROR    | app.utils.email_processor | 处理邮件主题筛选时出错: 'int' object has no attribute 'decode'
2025-06-24 16:24:17 | ERROR    | app.utils.email_processor | 处理邮件主题筛选时出错: 'int' object has no attribute 'decode'
2025-06-24 16:24:17 | ERROR    | app.utils.email_processor | 处理邮件主题筛选时出错: 'int' object has no attribute 'decode'
2025-06-24 16:24:18 | ERROR    | app.utils.email_processor | 处理邮件主题筛选时出错: 'int' object has no attribute 'decode'
2025-06-24 16:24:18 | ERROR    | app.utils.email_processor | 处理邮件主题筛选时出错: 'int' object has no attribute 'decode'
2025-06-24 16:24:18 | ERROR    | app.utils.email_processor | 处理邮件主题筛选时出错: 'int' object has no attribute 'decode'
2025-06-24 16:24:18 | ERROR    | app.utils.email_processor | 处理邮件主题筛选时出错: 'int' object has no attribute 'decode'
2025-06-24 16:24:18 | ERROR    | app.utils.email_processor | 处理邮件时出错: 'int' object has no attribute 'decode'
2025-06-24 16:24:18 | ERROR    | app.utils.email_processor | 处理邮件时出错: 'int' object has no attribute 'decode'
2025-06-24 16:24:18 | ERROR    | app.utils.email_processor | 处理邮件主题筛选时出错: 'int' object has no attribute 'decode'
2025-06-24 16:24:18 | ERROR    | app.utils.email_processor | 处理邮件主题筛选时出错: 'int' object has no attribute 'decode'
2025-06-24 16:24:21 | ERROR    | app.utils.email_processor | 处理邮件主题筛选时出错: 'int' object has no attribute 'decode'
2025-06-24 16:24:22 | ERROR    | app.utils.email_processor | 处理邮件主题筛选时出错: 'int' object has no attribute 'decode'
2025-06-24 16:24:23 | ERROR    | app.utils.email_processor | 处理邮件主题筛选时出错: 'int' object has no attribute 'decode'
2025-06-24 16:24:26 | ERROR    | app.utils.email_processor | 处理邮件主题筛选时出错: 'int' object has no attribute 'decode'
2025-06-24 16:24:29 | ERROR    | app.utils.email_processor | 处理邮件主题筛选时出错: 'int' object has no attribute 'decode'
2025-06-24 16:24:30 | ERROR    | app.utils.email_processor | 处理邮件主题筛选时出错: 'int' object has no attribute 'decode'
2025-06-24 16:24:30 | ERROR    | app.utils.email_processor | 处理邮件主题筛选时出错: 'int' object has no attribute 'decode'
2025-06-24 16:24:30 | ERROR    | app.utils.email_processor | 处理邮件主题筛选时出错: 'int' object has no attribute 'decode'
2025-06-24 16:24:35 | ERROR    | app.utils.email_processor | 处理邮件主题筛选时出错: 'int' object has no attribute 'decode'
2025-06-24 16:24:35 | ERROR    | app.utils.email_processor | 处理邮件主题筛选时出错: 'int' object has no attribute 'decode'
2025-06-24 16:24:35 | ERROR    | app.utils.email_processor | 处理邮件主题筛选时出错: 'int' object has no attribute 'decode'
2025-06-24 16:24:36 | ERROR    | app.utils.email_processor | 处理邮件主题筛选时出错: 'int' object has no attribute 'decode'
2025-06-24 16:24:36 | ERROR    | app.utils.email_processor | 处理邮件主题筛选时出错: 'int' object has no attribute 'decode'
2025-06-24 16:24:36 | ERROR    | app.utils.email_processor | 处理邮件主题筛选时出错: 'int' object has no attribute 'decode'
2025-06-24 16:24:36 | ERROR    | app.utils.email_processor | 处理邮件主题筛选时出错: 'int' object has no attribute 'decode'
2025-06-24 16:24:36 | ERROR    | app.utils.email_processor | 处理邮件主题筛选时出错: 'int' object has no attribute 'decode'
2025-06-24 16:24:36 | ERROR    | app.utils.email_processor | 处理邮件主题筛选时出错: 'int' object has no attribute 'decode'
2025-06-24 16:24:36 | ERROR    | app.utils.email_processor | 处理邮件主题筛选时出错: 'int' object has no attribute 'decode'
2025-06-24 16:24:38 | ERROR    | app.utils.email_processor | 处理邮件主题筛选时出错: 'int' object has no attribute 'decode'
2025-06-24 16:24:39 | ERROR    | app.utils.email_processor | 处理邮件主题筛选时出错: 'int' object has no attribute 'decode'
2025-06-24 16:24:39 | ERROR    | app.utils.email_processor | 处理邮件主题筛选时出错: 'int' object has no attribute 'decode'
2025-06-24 16:24:39 | ERROR    | app.utils.email_processor | 处理邮件主题筛选时出错: 'int' object has no attribute 'decode'
2025-06-24 16:24:41 | ERROR    | app.utils.email_processor | 处理邮件主题筛选时出错: 'int' object has no attribute 'decode'
2025-06-24 16:24:41 | ERROR    | app.utils.email_processor | 处理邮件主题筛选时出错: 'int' object has no attribute 'decode'
2025-06-24 16:24:41 | ERROR    | app.utils.email_processor | 处理邮件主题筛选时出错: 'int' object has no attribute 'decode'
2025-06-24 16:24:41 | ERROR    | app.utils.email_processor | 处理邮件主题筛选时出错: 'int' object has no attribute 'decode'
2025-06-24 16:24:44 | ERROR    | app.utils.email_processor | 处理邮件主题筛选时出错: 'int' object has no attribute 'decode'
2025-06-24 16:24:44 | ERROR    | app.utils.email_processor | 处理邮件主题筛选时出错: 'int' object has no attribute 'decode'
2025-06-24 16:24:44 | ERROR    | app.utils.email_processor | 处理邮件主题筛选时出错: 'int' object has no attribute 'decode'
2025-06-24 16:24:44 | ERROR    | app.utils.email_processor | 处理邮件主题筛选时出错: 'int' object has no attribute 'decode'
2025-06-24 16:24:44 | ERROR    | app.utils.email_processor | 处理邮件主题筛选时出错: 'int' object has no attribute 'decode'
2025-06-24 16:24:44 | ERROR    | app.utils.email_processor | 处理邮件主题筛选时出错: 'int' object has no attribute 'decode'
2025-06-24 16:24:44 | ERROR    | app.utils.email_processor | 处理邮件主题筛选时出错: 'int' object has no attribute 'decode'
2025-06-24 16:24:44 | ERROR    | app.utils.email_processor | 处理邮件主题筛选时出错: 'int' object has no attribute 'decode'
2025-06-24 16:24:44 | ERROR    | app.utils.email_processor | 处理邮件主题筛选时出错: 'int' object has no attribute 'decode'
2025-06-24 16:24:47 | ERROR    | app.utils.email_processor | 处理邮件主题筛选时出错: 'int' object has no attribute 'decode'
2025-06-24 16:24:47 | ERROR    | app.utils.email_processor | 处理邮件主题筛选时出错: 'int' object has no attribute 'decode'
2025-06-24 16:24:47 | ERROR    | app.utils.email_processor | 处理邮件主题筛选时出错: 'int' object has no attribute 'decode'
2025-06-24 16:24:53 | ERROR    | app.utils.email_processor | 处理邮件主题筛选时出错: 'int' object has no attribute 'decode'
2025-06-24 16:24:59 | ERROR    | app.utils.email_processor | 处理邮件主题筛选时出错: 'int' object has no attribute 'decode'
2025-06-24 16:24:59 | ERROR    | app.utils.email_processor | 处理邮件主题筛选时出错: 'int' object has no attribute 'decode'
2025-06-24 16:24:59 | ERROR    | app.utils.email_processor | 处理邮件主题筛选时出错: 'int' object has no attribute 'decode'
2025-06-24 16:25:05 | ERROR    | app.utils.email_processor | 处理邮件主题筛选时出错: 'int' object has no attribute 'decode'
2025-06-24 16:25:05 | ERROR    | app.utils.email_processor | 处理邮件主题筛选时出错: 'int' object has no attribute 'decode'
2025-06-24 16:25:11 | ERROR    | app.utils.email_processor | 处理邮件主题筛选时出错: 'int' object has no attribute 'decode'
2025-06-24 16:25:17 | ERROR    | app.utils.email_processor | 处理邮件主题筛选时出错: 'int' object has no attribute 'decode'
2025-06-24 16:25:20 | ERROR    | app.utils.email_processor | 处理邮件主题筛选时出错: 'int' object has no attribute 'decode'
2025-06-24 16:25:20 | ERROR    | app.utils.email_processor | 处理邮件主题筛选时出错: 'int' object has no attribute 'decode'
2025-06-24 16:25:20 | ERROR    | app.utils.email_processor | 处理邮件主题筛选时出错: 'int' object has no attribute 'decode'
2025-06-24 16:25:20 | ERROR    | app.utils.email_processor | 处理邮件主题筛选时出错: 'int' object has no attribute 'decode'
2025-06-24 16:25:23 | ERROR    | app.utils.email_processor | 处理邮件主题筛选时出错: 'int' object has no attribute 'decode'
2025-06-24 16:25:23 | ERROR    | app.utils.email_processor | 处理邮件主题筛选时出错: 'int' object has no attribute 'decode'
2025-06-24 16:25:24 | ERROR    | app.utils.email_processor | 处理邮件主题筛选时出错: 'int' object has no attribute 'decode'
2025-06-24 16:25:24 | ERROR    | app.utils.email_processor | 处理邮件主题筛选时出错: 'int' object has no attribute 'decode'
2025-06-24 16:25:24 | ERROR    | app.utils.email_processor | 处理邮件主题筛选时出错: 'int' object has no attribute 'decode'
2025-06-24 16:25:24 | ERROR    | app.utils.email_processor | 处理邮件主题筛选时出错: 'int' object has no attribute 'decode'
2025-06-24 16:25:25 | ERROR    | app.utils.email_processor | 处理邮件主题筛选时出错: 'int' object has no attribute 'decode'
2025-06-24 16:25:25 | ERROR    | app.utils.email_processor | 处理邮件主题筛选时出错: 'int' object has no attribute 'decode'
2025-06-24 16:25:26 | ERROR    | app.utils.email_processor | 处理邮件主题筛选时出错: 'int' object has no attribute 'decode'
2025-06-24 16:25:26 | ERROR    | app.utils.email_processor | 处理邮件主题筛选时出错: 'int' object has no attribute 'decode'
2025-06-24 16:25:26 | ERROR    | app.utils.email_processor | 处理邮件主题筛选时出错: 'int' object has no attribute 'decode'
2025-06-24 16:25:26 | ERROR    | app.utils.email_processor | 处理邮件主题筛选时出错: 'int' object has no attribute 'decode'
2025-06-24 16:25:27 | ERROR    | app.utils.email_processor | 处理邮件主题筛选时出错: 'int' object has no attribute 'decode'
2025-06-24 16:25:27 | ERROR    | app.utils.email_processor | 处理邮件主题筛选时出错: 'int' object has no attribute 'decode'
2025-06-24 16:25:33 | ERROR    | app.utils.email_processor | 处理邮件主题筛选时出错: 'int' object has no attribute 'decode'
2025-06-24 16:25:33 | ERROR    | app.utils.email_processor | 处理邮件主题筛选时出错: 'int' object has no attribute 'decode'
2025-06-24 16:25:34 | ERROR    | app.utils.email_processor | 处理邮件主题筛选时出错: 'int' object has no attribute 'decode'
2025-06-24 16:25:35 | ERROR    | app.utils.email_processor | 处理邮件主题筛选时出错: 'int' object has no attribute 'decode'
2025-06-24 16:25:35 | ERROR    | app.utils.email_processor | 处理邮件主题筛选时出错: 'int' object has no attribute 'decode'
2025-06-24 16:25:35 | ERROR    | app.utils.email_processor | 处理邮件主题筛选时出错: 'int' object has no attribute 'decode'
2025-06-24 16:25:35 | ERROR    | app.utils.email_processor | 处理邮件主题筛选时出错: 'int' object has no attribute 'decode'
2025-06-24 16:25:36 | ERROR    | app.utils.email_processor | 处理邮件主题筛选时出错: 'int' object has no attribute 'decode'
2025-06-24 16:25:36 | ERROR    | app.utils.email_processor | 处理邮件主题筛选时出错: 'int' object has no attribute 'decode'
2025-06-24 16:25:36 | ERROR    | app.utils.email_processor | 处理邮件主题筛选时出错: 'int' object has no attribute 'decode'
2025-06-24 16:25:36 | ERROR    | app.utils.email_processor | 处理邮件主题筛选时出错: 'int' object has no attribute 'decode'
2025-06-24 16:25:37 | ERROR    | app.utils.email_processor | 处理邮件主题筛选时出错: 'int' object has no attribute 'decode'
2025-06-24 16:25:37 | ERROR    | app.utils.email_processor | 处理邮件主题筛选时出错: 'int' object has no attribute 'decode'
2025-06-24 16:25:37 | ERROR    | app.utils.email_processor | 处理邮件主题筛选时出错: 'int' object has no attribute 'decode'
2025-06-24 16:25:37 | ERROR    | app.utils.email_processor | 处理邮件主题筛选时出错: 'int' object has no attribute 'decode'
2025-06-24 16:25:37 | ERROR    | app.utils.email_processor | 处理邮件主题筛选时出错: 'int' object has no attribute 'decode'
2025-06-24 16:25:37 | ERROR    | app.utils.email_processor | 处理邮件主题筛选时出错: 'int' object has no attribute 'decode'
2025-06-24 16:25:37 | ERROR    | app.utils.email_processor | 处理邮件主题筛选时出错: 'int' object has no attribute 'decode'
2025-06-24 16:25:37 | ERROR    | app.utils.email_processor | 处理邮件主题筛选时出错: 'int' object has no attribute 'decode'
2025-06-24 16:25:37 | ERROR    | app.utils.email_processor | 处理邮件主题筛选时出错: 'int' object has no attribute 'decode'
2025-06-24 16:25:38 | ERROR    | app.utils.email_processor | 处理邮件主题筛选时出错: 'int' object has no attribute 'decode'
2025-06-24 16:25:38 | ERROR    | app.utils.email_processor | 处理邮件主题筛选时出错: 'int' object has no attribute 'decode'
2025-06-24 16:25:38 | ERROR    | app.utils.email_processor | 处理邮件主题筛选时出错: 'int' object has no attribute 'decode'
2025-06-24 16:25:38 | ERROR    | app.utils.email_processor | 处理邮件主题筛选时出错: 'int' object has no attribute 'decode'
2025-06-24 16:25:38 | ERROR    | app.utils.email_processor | 处理邮件主题筛选时出错: 'int' object has no attribute 'decode'
2025-06-24 16:25:38 | ERROR    | app.utils.email_processor | 处理邮件主题筛选时出错: 'int' object has no attribute 'decode'
2025-06-24 16:25:39 | ERROR    | app.utils.email_processor | 处理邮件主题筛选时出错: 'int' object has no attribute 'decode'
2025-06-24 16:25:39 | ERROR    | app.utils.email_processor | 处理邮件主题筛选时出错: 'int' object has no attribute 'decode'
2025-06-24 16:25:39 | ERROR    | app.utils.email_processor | 处理邮件主题筛选时出错: 'int' object has no attribute 'decode'
2025-06-24 16:25:39 | ERROR    | app.utils.email_processor | 处理邮件主题筛选时出错: 'int' object has no attribute 'decode'
2025-06-24 16:25:39 | ERROR    | app.utils.email_processor | 处理邮件主题筛选时出错: 'int' object has no attribute 'decode'
2025-06-24 16:25:39 | ERROR    | app.utils.email_processor | 处理邮件主题筛选时出错: 'int' object has no attribute 'decode'
2025-06-24 16:25:39 | ERROR    | app.utils.email_processor | 处理邮件主题筛选时出错: 'int' object has no attribute 'decode'
2025-06-24 16:25:39 | ERROR    | app.utils.email_processor | 处理邮件主题筛选时出错: 'int' object has no attribute 'decode'
2025-06-24 16:25:40 | ERROR    | app.utils.email_processor | 处理邮件主题筛选时出错: 'int' object has no attribute 'decode'
2025-06-24 16:25:40 | ERROR    | app.utils.email_processor | 处理邮件主题筛选时出错: 'int' object has no attribute 'decode'
2025-06-24 16:25:40 | ERROR    | app.utils.email_processor | 处理邮件主题筛选时出错: 'int' object has no attribute 'decode'
2025-06-24 16:25:40 | ERROR    | app.utils.email_processor | 处理邮件主题筛选时出错: 'int' object has no attribute 'decode'
2025-06-24 16:25:40 | ERROR    | app.utils.email_processor | 处理邮件主题筛选时出错: 'int' object has no attribute 'decode'
2025-06-24 16:25:40 | ERROR    | app.utils.email_processor | 处理邮件主题筛选时出错: 'int' object has no attribute 'decode'
2025-06-24 16:25:41 | ERROR    | app.utils.email_processor | 处理邮件主题筛选时出错: 'int' object has no attribute 'decode'
2025-06-24 16:25:41 | ERROR    | app.utils.email_processor | 处理邮件主题筛选时出错: 'int' object has no attribute 'decode'
2025-06-24 16:25:42 | ERROR    | app.utils.email_processor | 处理邮件主题筛选时出错: 'int' object has no attribute 'decode'
2025-06-24 16:25:42 | ERROR    | app.utils.email_processor | 处理邮件主题筛选时出错: 'int' object has no attribute 'decode'
2025-06-24 16:25:42 | ERROR    | app.utils.email_processor | 处理邮件主题筛选时出错: 'int' object has no attribute 'decode'
2025-06-24 16:25:42 | ERROR    | app.utils.email_processor | 处理邮件主题筛选时出错: 'int' object has no attribute 'decode'
2025-06-24 16:25:42 | ERROR    | app.utils.email_processor | 处理邮件主题筛选时出错: 'int' object has no attribute 'decode'
2025-06-24 16:25:43 | ERROR    | app.utils.email_processor | 处理邮件主题筛选时出错: 'int' object has no attribute 'decode'
2025-06-24 16:25:43 | ERROR    | app.utils.email_processor | 处理邮件主题筛选时出错: 'int' object has no attribute 'decode'
2025-06-24 16:25:44 | ERROR    | app.utils.email_processor | 处理邮件主题筛选时出错: 'int' object has no attribute 'decode'
2025-06-24 16:25:44 | ERROR    | app.utils.email_processor | 处理邮件主题筛选时出错: 'int' object has no attribute 'decode'
2025-06-24 16:25:44 | ERROR    | app.utils.email_processor | 处理邮件主题筛选时出错: 'int' object has no attribute 'decode'
2025-06-24 16:25:44 | ERROR    | app.utils.email_processor | 处理邮件主题筛选时出错: 'int' object has no attribute 'decode'
2025-06-24 16:25:44 | ERROR    | app.utils.email_processor | 处理邮件主题筛选时出错: 'int' object has no attribute 'decode'
2025-06-24 16:25:44 | ERROR    | app.utils.email_processor | 处理邮件主题筛选时出错: 'int' object has no attribute 'decode'
2025-06-24 16:25:44 | ERROR    | app.utils.email_processor | 处理邮件主题筛选时出错: 'int' object has no attribute 'decode'
2025-06-24 16:25:44 | ERROR    | app.utils.email_processor | 处理邮件主题筛选时出错: 'int' object has no attribute 'decode'
2025-06-24 16:25:44 | ERROR    | app.utils.email_processor | 处理邮件主题筛选时出错: 'int' object has no attribute 'decode'
2025-06-24 16:25:44 | ERROR    | app.utils.email_processor | 处理邮件主题筛选时出错: 'int' object has no attribute 'decode'
2025-06-24 16:25:44 | ERROR    | app.utils.email_processor | 处理邮件主题筛选时出错: 'int' object has no attribute 'decode'
2025-06-24 16:25:45 | ERROR    | app.utils.email_processor | 处理邮件主题筛选时出错: 'int' object has no attribute 'decode'
2025-06-24 16:25:45 | ERROR    | app.utils.email_processor | 处理邮件主题筛选时出错: 'int' object has no attribute 'decode'
2025-06-24 16:25:46 | ERROR    | app.utils.email_processor | 处理邮件主题筛选时出错: 'int' object has no attribute 'decode'
2025-06-24 16:25:46 | ERROR    | app.utils.email_processor | 处理邮件主题筛选时出错: 'int' object has no attribute 'decode'
2025-06-24 16:25:46 | ERROR    | app.utils.email_processor | 处理邮件主题筛选时出错: 'int' object has no attribute 'decode'
2025-06-24 16:25:46 | ERROR    | app.utils.email_processor | 处理邮件主题筛选时出错: 'int' object has no attribute 'decode'
2025-06-24 16:25:46 | ERROR    | app.utils.email_processor | 处理邮件主题筛选时出错: 'int' object has no attribute 'decode'
2025-06-24 16:25:46 | ERROR    | app.utils.email_processor | 处理邮件主题筛选时出错: 'int' object has no attribute 'decode'
2025-06-24 16:25:47 | ERROR    | app.utils.email_processor | 处理邮件主题筛选时出错: 'int' object has no attribute 'decode'
2025-06-24 16:25:47 | ERROR    | app.utils.email_processor | 处理邮件主题筛选时出错: 'int' object has no attribute 'decode'
2025-06-24 16:25:48 | ERROR    | app.utils.email_processor | 处理邮件主题筛选时出错: 'int' object has no attribute 'decode'
2025-06-24 16:25:48 | ERROR    | app.utils.email_processor | 处理邮件主题筛选时出错: 'int' object has no attribute 'decode'
2025-06-24 16:25:48 | ERROR    | app.utils.email_processor | 处理邮件主题筛选时出错: 'int' object has no attribute 'decode'
2025-06-24 16:25:49 | ERROR    | app.utils.email_processor | 处理邮件主题筛选时出错: 'int' object has no attribute 'decode'
2025-06-24 16:25:49 | ERROR    | app.utils.email_processor | 处理邮件主题筛选时出错: 'int' object has no attribute 'decode'
2025-06-24 16:25:49 | ERROR    | app.utils.email_processor | 处理邮件主题筛选时出错: 'int' object has no attribute 'decode'
2025-06-24 16:25:49 | ERROR    | app.utils.email_processor | 处理邮件主题筛选时出错: 'int' object has no attribute 'decode'
2025-06-24 16:25:49 | ERROR    | app.utils.email_processor | 处理邮件主题筛选时出错: 'int' object has no attribute 'decode'
2025-06-24 16:25:49 | ERROR    | app.utils.email_processor | 处理邮件主题筛选时出错: 'int' object has no attribute 'decode'
2025-06-24 16:25:49 | ERROR    | app.utils.email_processor | 处理邮件主题筛选时出错: 'int' object has no attribute 'decode'
2025-06-24 16:25:49 | ERROR    | app.utils.email_processor | 处理邮件主题筛选时出错: 'int' object has no attribute 'decode'
2025-06-24 16:25:49 | ERROR    | app.utils.email_processor | 处理邮件主题筛选时出错: 'int' object has no attribute 'decode'
2025-06-24 16:25:49 | ERROR    | app.utils.email_processor | 处理邮件主题筛选时出错: 'int' object has no attribute 'decode'
2025-06-24 16:25:49 | ERROR    | app.utils.email_processor | 处理邮件主题筛选时出错: 'int' object has no attribute 'decode'
2025-06-24 16:25:49 | ERROR    | app.utils.email_processor | 处理邮件主题筛选时出错: 'int' object has no attribute 'decode'
2025-06-24 16:25:49 | ERROR    | app.utils.email_processor | 处理邮件主题筛选时出错: 'int' object has no attribute 'decode'
2025-06-24 16:25:50 | ERROR    | app.utils.email_processor | 处理邮件主题筛选时出错: 'int' object has no attribute 'decode'
2025-06-24 16:25:50 | ERROR    | app.utils.email_processor | 处理邮件主题筛选时出错: 'int' object has no attribute 'decode'
2025-06-24 16:25:50 | ERROR    | app.utils.email_processor | 处理邮件主题筛选时出错: 'int' object has no attribute 'decode'
2025-06-24 16:25:50 | ERROR    | app.utils.email_processor | 处理邮件主题筛选时出错: 'int' object has no attribute 'decode'
2025-06-24 16:25:50 | ERROR    | app.utils.email_processor | 处理邮件主题筛选时出错: 'int' object has no attribute 'decode'
2025-06-24 16:25:50 | ERROR    | app.utils.email_processor | 处理邮件主题筛选时出错: 'int' object has no attribute 'decode'
2025-06-24 16:25:50 | ERROR    | app.utils.email_processor | 处理邮件主题筛选时出错: 'int' object has no attribute 'decode'
2025-06-24 16:25:50 | ERROR    | app.utils.email_processor | 处理邮件主题筛选时出错: 'int' object has no attribute 'decode'
2025-06-24 16:25:50 | ERROR    | app.utils.email_processor | 处理邮件主题筛选时出错: 'int' object has no attribute 'decode'
2025-06-24 16:25:50 | ERROR    | app.utils.email_processor | 处理邮件主题筛选时出错: 'int' object has no attribute 'decode'
2025-06-24 16:25:51 | ERROR    | app.utils.email_processor | 处理邮件主题筛选时出错: 'int' object has no attribute 'decode'
2025-06-24 16:25:51 | ERROR    | app.utils.email_processor | 处理邮件主题筛选时出错: 'int' object has no attribute 'decode'
2025-06-24 16:25:51 | ERROR    | app.utils.email_processor | 处理邮件主题筛选时出错: 'int' object has no attribute 'decode'
2025-06-24 16:25:51 | ERROR    | app.utils.email_processor | 处理邮件主题筛选时出错: 'int' object has no attribute 'decode'
2025-06-24 16:25:52 | ERROR    | app.utils.email_processor | 处理邮件主题筛选时出错: 'int' object has no attribute 'decode'
2025-06-24 16:25:52 | ERROR    | app.utils.email_processor | 处理邮件主题筛选时出错: 'int' object has no attribute 'decode'
2025-06-24 16:25:53 | ERROR    | app.utils.email_processor | 处理邮件主题筛选时出错: 'int' object has no attribute 'decode'
2025-06-24 16:25:53 | ERROR    | app.utils.email_processor | 处理邮件主题筛选时出错: 'int' object has no attribute 'decode'
2025-06-24 16:25:53 | ERROR    | app.utils.email_processor | 处理邮件主题筛选时出错: 'int' object has no attribute 'decode'
2025-06-24 16:25:53 | ERROR    | app.utils.email_processor | 处理邮件主题筛选时出错: 'int' object has no attribute 'decode'
2025-06-24 16:25:53 | ERROR    | app.utils.email_processor | 处理邮件主题筛选时出错: 'int' object has no attribute 'decode'
2025-06-24 16:25:53 | ERROR    | app.utils.email_processor | 处理邮件主题筛选时出错: 'int' object has no attribute 'decode'
2025-06-24 16:25:53 | ERROR    | app.utils.email_processor | 处理邮件主题筛选时出错: 'int' object has no attribute 'decode'
2025-06-24 16:25:53 | ERROR    | app.utils.email_processor | 处理邮件主题筛选时出错: 'int' object has no attribute 'decode'
2025-06-24 16:25:53 | ERROR    | app.utils.email_processor | 处理邮件主题筛选时出错: 'int' object has no attribute 'decode'
2025-06-24 16:25:53 | ERROR    | app.utils.email_processor | 处理邮件主题筛选时出错: 'int' object has no attribute 'decode'
2025-06-24 16:25:53 | ERROR    | app.utils.email_processor | 处理邮件主题筛选时出错: 'int' object has no attribute 'decode'
2025-06-24 16:25:53 | ERROR    | app.utils.email_processor | 处理邮件主题筛选时出错: 'int' object has no attribute 'decode'
2025-06-24 16:25:54 | ERROR    | app.utils.email_processor | 处理邮件主题筛选时出错: 'int' object has no attribute 'decode'
2025-06-24 16:25:54 | ERROR    | app.utils.email_processor | 处理邮件主题筛选时出错: 'int' object has no attribute 'decode'
2025-06-24 16:25:56 | ERROR    | app.utils.email_processor | 处理邮件主题筛选时出错: 'int' object has no attribute 'decode'
2025-06-24 16:25:56 | ERROR    | app.utils.email_processor | 处理邮件主题筛选时出错: 'int' object has no attribute 'decode'
2025-06-24 16:25:57 | ERROR    | app.utils.email_processor | 处理邮件主题筛选时出错: 'int' object has no attribute 'decode'
2025-06-24 16:25:57 | ERROR    | app.utils.email_processor | 处理邮件主题筛选时出错: 'int' object has no attribute 'decode'
2025-06-24 16:25:57 | ERROR    | app.utils.email_processor | 处理邮件主题筛选时出错: 'int' object has no attribute 'decode'
2025-06-24 16:25:57 | ERROR    | app.utils.email_processor | 处理邮件主题筛选时出错: 'int' object has no attribute 'decode'
2025-06-24 16:25:58 | ERROR    | app.utils.email_processor | 处理邮件主题筛选时出错: 'int' object has no attribute 'decode'
2025-06-24 16:25:58 | ERROR    | app.utils.email_processor | 处理邮件主题筛选时出错: 'int' object has no attribute 'decode'
2025-06-24 16:25:58 | ERROR    | app.utils.email_processor | 处理邮件主题筛选时出错: 'int' object has no attribute 'decode'
2025-06-24 16:25:59 | ERROR    | app.utils.email_processor | 处理邮件主题筛选时出错: 'int' object has no attribute 'decode'
2025-06-24 16:25:59 | ERROR    | app.utils.email_processor | 处理邮件主题筛选时出错: 'int' object has no attribute 'decode'
2025-06-24 16:25:59 | ERROR    | app.utils.email_processor | 处理邮件主题筛选时出错: 'int' object has no attribute 'decode'
2025-06-24 16:25:59 | ERROR    | app.utils.email_processor | 处理邮件主题筛选时出错: 'int' object has no attribute 'decode'
2025-06-24 16:25:59 | ERROR    | app.utils.email_processor | 处理邮件主题筛选时出错: 'int' object has no attribute 'decode'
2025-06-24 16:25:59 | ERROR    | app.utils.email_processor | 处理邮件主题筛选时出错: 'int' object has no attribute 'decode'
2025-06-24 16:25:59 | ERROR    | app.utils.email_processor | 处理邮件主题筛选时出错: 'int' object has no attribute 'decode'
2025-06-24 16:25:59 | ERROR    | app.utils.email_processor | 处理邮件主题筛选时出错: 'int' object has no attribute 'decode'
2025-06-24 16:25:59 | ERROR    | app.utils.email_processor | 处理邮件主题筛选时出错: 'int' object has no attribute 'decode'
2025-06-24 16:25:59 | ERROR    | app.utils.email_processor | 处理邮件主题筛选时出错: 'int' object has no attribute 'decode'
2025-06-24 16:25:59 | ERROR    | app.utils.email_processor | 处理邮件主题筛选时出错: 'int' object has no attribute 'decode'
2025-06-24 16:25:59 | ERROR    | app.utils.email_processor | 处理邮件主题筛选时出错: 'int' object has no attribute 'decode'
2025-06-24 16:25:59 | ERROR    | app.utils.email_processor | 处理邮件主题筛选时出错: 'int' object has no attribute 'decode'
2025-06-24 16:25:59 | ERROR    | app.utils.email_processor | 处理邮件主题筛选时出错: 'int' object has no attribute 'decode'
2025-06-24 16:25:59 | ERROR    | app.utils.email_processor | 处理邮件主题筛选时出错: 'int' object has no attribute 'decode'
2025-06-24 16:26:00 | ERROR    | app.utils.email_processor | 处理邮件主题筛选时出错: 'int' object has no attribute 'decode'
2025-06-24 16:26:01 | ERROR    | app.utils.email_processor | 处理邮件主题筛选时出错: 'int' object has no attribute 'decode'
2025-06-24 16:26:01 | ERROR    | app.utils.email_processor | 处理邮件主题筛选时出错: 'int' object has no attribute 'decode'
2025-06-24 16:26:01 | ERROR    | app.utils.email_processor | 处理邮件主题筛选时出错: 'int' object has no attribute 'decode'
2025-06-24 16:26:01 | ERROR    | app.utils.email_processor | 处理邮件主题筛选时出错: 'int' object has no attribute 'decode'
2025-06-24 16:26:01 | ERROR    | app.utils.email_processor | 处理邮件主题筛选时出错: 'int' object has no attribute 'decode'
2025-06-24 16:26:05 | ERROR    | app.utils.email_processor | 处理邮件主题筛选时出错: 'int' object has no attribute 'decode'
2025-06-24 16:26:05 | ERROR    | app.utils.email_processor | 处理邮件主题筛选时出错: 'int' object has no attribute 'decode'
2025-06-24 16:26:05 | ERROR    | app.utils.email_processor | 处理邮件主题筛选时出错: 'int' object has no attribute 'decode'
2025-06-24 16:26:05 | ERROR    | app.utils.email_processor | 处理邮件主题筛选时出错: 'int' object has no attribute 'decode'
2025-06-24 16:26:06 | ERROR    | app.utils.email_processor | 处理邮件主题筛选时出错: 'int' object has no attribute 'decode'
2025-06-24 16:26:06 | ERROR    | app.utils.email_processor | 处理邮件主题筛选时出错: 'int' object has no attribute 'decode'
2025-06-24 16:26:07 | ERROR    | app.utils.email_processor | 处理邮件主题筛选时出错: 'int' object has no attribute 'decode'
2025-06-24 16:26:07 | ERROR    | app.utils.email_processor | 处理邮件主题筛选时出错: 'int' object has no attribute 'decode'
2025-06-24 16:26:07 | ERROR    | app.utils.email_processor | 处理邮件主题筛选时出错: 'int' object has no attribute 'decode'
2025-06-24 16:26:07 | ERROR    | app.utils.email_processor | 处理邮件主题筛选时出错: 'int' object has no attribute 'decode'
2025-06-24 16:26:07 | ERROR    | app.utils.email_processor | 处理邮件主题筛选时出错: 'int' object has no attribute 'decode'
2025-06-24 16:26:07 | ERROR    | app.utils.email_processor | 处理邮件主题筛选时出错: 'int' object has no attribute 'decode'
2025-06-24 16:26:07 | ERROR    | app.utils.email_processor | 处理邮件主题筛选时出错: 'int' object has no attribute 'decode'
2025-06-24 16:26:07 | ERROR    | app.utils.email_processor | 处理邮件主题筛选时出错: 'int' object has no attribute 'decode'
2025-06-24 16:26:08 | ERROR    | app.utils.email_processor | 处理邮件主题筛选时出错: 'int' object has no attribute 'decode'
2025-06-24 16:26:08 | ERROR    | app.utils.email_processor | 处理邮件主题筛选时出错: 'int' object has no attribute 'decode'
2025-06-24 16:26:08 | ERROR    | app.utils.email_processor | 处理邮件主题筛选时出错: 'int' object has no attribute 'decode'
2025-06-24 16:26:08 | ERROR    | app.utils.email_processor | 处理邮件主题筛选时出错: 'int' object has no attribute 'decode'
2025-06-24 16:26:08 | ERROR    | app.utils.email_processor | 处理邮件主题筛选时出错: 'int' object has no attribute 'decode'
2025-06-24 16:26:08 | ERROR    | app.utils.email_processor | 处理邮件主题筛选时出错: 'int' object has no attribute 'decode'
2025-06-24 16:26:08 | ERROR    | app.utils.email_processor | 处理邮件主题筛选时出错: 'int' object has no attribute 'decode'
2025-06-24 16:26:08 | ERROR    | app.utils.email_processor | 处理邮件主题筛选时出错: 'int' object has no attribute 'decode'
2025-06-24 16:26:08 | ERROR    | app.utils.email_processor | 处理邮件主题筛选时出错: 'int' object has no attribute 'decode'
2025-06-24 16:26:08 | ERROR    | app.utils.email_processor | 处理邮件主题筛选时出错: 'int' object has no attribute 'decode'
2025-06-24 16:26:08 | ERROR    | app.utils.email_processor | 处理邮件主题筛选时出错: 'int' object has no attribute 'decode'
2025-06-24 16:26:08 | ERROR    | app.utils.email_processor | 处理邮件主题筛选时出错: 'int' object has no attribute 'decode'
2025-06-24 16:26:09 | ERROR    | app.utils.email_processor | 处理邮件主题筛选时出错: 'int' object has no attribute 'decode'
2025-06-24 16:26:09 | ERROR    | app.utils.email_processor | 处理邮件主题筛选时出错: 'int' object has no attribute 'decode'
2025-06-24 16:26:09 | ERROR    | app.utils.email_processor | 处理邮件主题筛选时出错: 'int' object has no attribute 'decode'
2025-06-24 16:26:09 | ERROR    | app.utils.email_processor | 处理邮件主题筛选时出错: 'int' object has no attribute 'decode'
2025-06-24 16:26:09 | ERROR    | app.utils.email_processor | 处理邮件主题筛选时出错: 'int' object has no attribute 'decode'
2025-06-24 16:26:10 | ERROR    | app.utils.email_processor | 处理邮件主题筛选时出错: 'int' object has no attribute 'decode'
2025-06-24 16:26:10 | ERROR    | app.utils.email_processor | 处理邮件主题筛选时出错: 'int' object has no attribute 'decode'
2025-06-24 16:26:10 | ERROR    | app.utils.email_processor | 处理邮件主题筛选时出错: 'int' object has no attribute 'decode'
2025-06-24 16:26:10 | ERROR    | app.utils.email_processor | 处理邮件主题筛选时出错: 'int' object has no attribute 'decode'
2025-06-24 16:26:11 | ERROR    | app.utils.email_processor | 处理邮件主题筛选时出错: 'int' object has no attribute 'decode'
2025-06-24 16:26:11 | ERROR    | app.utils.email_processor | 处理邮件主题筛选时出错: 'int' object has no attribute 'decode'
2025-06-24 16:26:11 | ERROR    | app.utils.email_processor | 处理邮件主题筛选时出错: 'int' object has no attribute 'decode'
2025-06-24 16:26:11 | ERROR    | app.utils.email_processor | 处理邮件主题筛选时出错: 'int' object has no attribute 'decode'
2025-06-24 16:26:11 | ERROR    | app.utils.email_processor | 处理邮件主题筛选时出错: 'int' object has no attribute 'decode'
2025-06-24 16:31:35 | ERROR    | app.utils.email_processor | 处理邮件主题筛选时出错: 'int' object has no attribute 'decode'
2025-06-24 16:31:35 | ERROR    | app.utils.email_processor | 处理邮件主题筛选时出错: 'int' object has no attribute 'decode'
2025-06-24 16:31:37 | ERROR    | app.utils.email_processor | 处理邮件主题筛选时出错: 'int' object has no attribute 'decode'
2025-06-24 16:31:37 | ERROR    | app.utils.email_processor | 处理邮件主题筛选时出错: 'int' object has no attribute 'decode'
2025-06-24 16:31:37 | ERROR    | app.utils.email_processor | 处理邮件主题筛选时出错: 'int' object has no attribute 'decode'
2025-06-24 16:31:37 | ERROR    | app.utils.email_processor | 处理邮件主题筛选时出错: 'int' object has no attribute 'decode'
2025-06-24 16:31:37 | ERROR    | app.utils.email_processor | 处理邮件主题筛选时出错: 'int' object has no attribute 'decode'
2025-06-24 16:31:37 | ERROR    | app.utils.email_processor | 处理邮件主题筛选时出错: 'int' object has no attribute 'decode'
2025-06-24 16:31:37 | ERROR    | app.utils.email_processor | 处理邮件主题筛选时出错: 'int' object has no attribute 'decode'
2025-06-24 16:31:37 | ERROR    | app.utils.email_processor | 处理邮件主题筛选时出错: 'int' object has no attribute 'decode'
2025-06-24 16:31:38 | ERROR    | app.utils.email_processor | 处理邮件主题筛选时出错: 'int' object has no attribute 'decode'
2025-06-24 16:31:38 | ERROR    | app.utils.email_processor | 处理邮件主题筛选时出错: 'int' object has no attribute 'decode'
2025-06-24 16:31:38 | ERROR    | app.utils.email_processor | 处理邮件主题筛选时出错: 'int' object has no attribute 'decode'
2025-06-24 16:31:38 | ERROR    | app.utils.email_processor | 处理邮件主题筛选时出错: 'int' object has no attribute 'decode'
2025-06-24 16:31:39 | ERROR    | app.utils.email_processor | 处理邮件主题筛选时出错: 'int' object has no attribute 'decode'
2025-06-24 16:31:39 | ERROR    | app.utils.email_processor | 处理邮件主题筛选时出错: 'int' object has no attribute 'decode'
2025-06-24 16:31:40 | ERROR    | app.utils.email_processor | 处理邮件主题筛选时出错: 'int' object has no attribute 'decode'
2025-06-24 16:31:40 | ERROR    | app.utils.email_processor | 处理邮件主题筛选时出错: 'int' object has no attribute 'decode'
2025-06-24 16:31:40 | ERROR    | app.utils.email_processor | 处理邮件主题筛选时出错: 'int' object has no attribute 'decode'
2025-06-24 16:31:40 | ERROR    | app.utils.email_processor | 处理邮件主题筛选时出错: 'int' object has no attribute 'decode'
2025-06-24 16:31:41 | ERROR    | app.utils.email_processor | 处理邮件主题筛选时出错: 'int' object has no attribute 'decode'
2025-06-24 16:31:41 | ERROR    | app.utils.email_processor | 处理邮件主题筛选时出错: 'int' object has no attribute 'decode'
2025-06-24 16:31:41 | ERROR    | app.utils.email_processor | 处理邮件主题筛选时出错: 'int' object has no attribute 'decode'
2025-06-24 16:31:42 | ERROR    | app.utils.email_processor | 处理邮件主题筛选时出错: 'int' object has no attribute 'decode'
2025-06-24 16:31:42 | ERROR    | app.utils.email_processor | 处理邮件主题筛选时出错: 'int' object has no attribute 'decode'
2025-06-24 16:31:42 | ERROR    | app.utils.email_processor | 处理邮件主题筛选时出错: 'int' object has no attribute 'decode'
2025-06-24 16:31:42 | ERROR    | app.utils.email_processor | 处理邮件主题筛选时出错: 'int' object has no attribute 'decode'
2025-06-24 16:31:42 | ERROR    | app.utils.email_processor | 处理邮件主题筛选时出错: 'int' object has no attribute 'decode'
2025-06-24 16:31:42 | ERROR    | app.utils.email_processor | 处理邮件主题筛选时出错: 'int' object has no attribute 'decode'
2025-06-24 16:31:43 | ERROR    | app.utils.email_processor | 处理邮件主题筛选时出错: 'int' object has no attribute 'decode'
2025-06-24 16:31:43 | ERROR    | app.utils.email_processor | 处理邮件主题筛选时出错: 'int' object has no attribute 'decode'
2025-06-24 16:31:43 | ERROR    | app.utils.email_processor | 处理邮件主题筛选时出错: 'int' object has no attribute 'decode'
2025-06-24 16:31:43 | ERROR    | app.utils.email_processor | 处理邮件主题筛选时出错: 'int' object has no attribute 'decode'
2025-06-24 16:31:43 | ERROR    | app.utils.email_processor | 处理邮件主题筛选时出错: 'int' object has no attribute 'decode'
2025-06-24 16:31:43 | ERROR    | app.utils.email_processor | 处理邮件主题筛选时出错: 'int' object has no attribute 'decode'
2025-06-24 16:31:44 | ERROR    | app.utils.email_processor | 处理邮件主题筛选时出错: 'int' object has no attribute 'decode'
2025-06-24 16:31:44 | ERROR    | app.utils.email_processor | 处理邮件主题筛选时出错: 'int' object has no attribute 'decode'
2025-06-24 16:31:44 | ERROR    | app.utils.email_processor | 处理邮件主题筛选时出错: 'int' object has no attribute 'decode'
2025-06-24 16:31:44 | ERROR    | app.utils.email_processor | 处理邮件主题筛选时出错: 'int' object has no attribute 'decode'
2025-06-24 16:31:44 | ERROR    | app.utils.email_processor | 处理邮件主题筛选时出错: 'int' object has no attribute 'decode'
2025-06-24 16:31:44 | ERROR    | app.utils.email_processor | 处理邮件主题筛选时出错: 'int' object has no attribute 'decode'
2025-06-24 16:31:44 | ERROR    | app.utils.email_processor | 处理邮件主题筛选时出错: 'int' object has no attribute 'decode'
2025-06-24 16:31:45 | ERROR    | app.utils.email_processor | 处理邮件主题筛选时出错: 'int' object has no attribute 'decode'
2025-06-24 16:31:45 | ERROR    | app.utils.email_processor | 处理邮件主题筛选时出错: 'int' object has no attribute 'decode'
2025-06-24 16:31:45 | ERROR    | app.utils.email_processor | 处理邮件主题筛选时出错: 'int' object has no attribute 'decode'
2025-06-24 16:31:45 | ERROR    | app.utils.email_processor | 处理邮件主题筛选时出错: 'int' object has no attribute 'decode'
2025-06-24 16:31:45 | ERROR    | app.utils.email_processor | 处理邮件主题筛选时出错: 'int' object has no attribute 'decode'
2025-06-24 16:31:45 | ERROR    | app.utils.email_processor | 处理邮件主题筛选时出错: 'int' object has no attribute 'decode'
2025-06-24 16:31:45 | ERROR    | app.utils.email_processor | 处理邮件主题筛选时出错: 'int' object has no attribute 'decode'
2025-06-24 16:31:46 | ERROR    | app.utils.email_processor | 处理邮件主题筛选时出错: 'int' object has no attribute 'decode'
2025-06-24 16:31:46 | ERROR    | app.utils.email_processor | 处理邮件主题筛选时出错: 'int' object has no attribute 'decode'
2025-06-24 16:31:46 | ERROR    | app.utils.email_processor | 处理邮件主题筛选时出错: 'int' object has no attribute 'decode'
2025-06-24 16:31:46 | ERROR    | app.utils.email_processor | 处理邮件主题筛选时出错: 'int' object has no attribute 'decode'
2025-06-24 16:31:47 | ERROR    | app.utils.email_processor | 处理邮件主题筛选时出错: 'int' object has no attribute 'decode'
2025-06-24 16:31:47 | ERROR    | app.utils.email_processor | 处理邮件主题筛选时出错: 'int' object has no attribute 'decode'
2025-06-24 16:31:47 | ERROR    | app.utils.email_processor | 处理邮件主题筛选时出错: 'int' object has no attribute 'decode'
2025-06-24 16:31:48 | ERROR    | app.utils.email_processor | 处理邮件主题筛选时出错: 'int' object has no attribute 'decode'
2025-06-24 16:31:49 | ERROR    | app.utils.email_processor | 处理邮件主题筛选时出错: 'int' object has no attribute 'decode'
2025-06-24 16:31:49 | ERROR    | app.utils.email_processor | 处理邮件主题筛选时出错: 'int' object has no attribute 'decode'
2025-06-24 16:31:49 | ERROR    | app.utils.email_processor | 处理邮件主题筛选时出错: 'int' object has no attribute 'decode'
2025-06-24 16:31:49 | ERROR    | app.utils.email_processor | 处理邮件主题筛选时出错: 'int' object has no attribute 'decode'
2025-06-24 16:31:49 | ERROR    | app.utils.email_processor | 处理邮件主题筛选时出错: 'int' object has no attribute 'decode'
2025-06-24 16:31:49 | ERROR    | app.utils.email_processor | 处理邮件主题筛选时出错: 'int' object has no attribute 'decode'
2025-06-24 16:31:49 | ERROR    | app.utils.email_processor | 处理邮件主题筛选时出错: 'int' object has no attribute 'decode'
2025-06-24 16:31:49 | ERROR    | app.utils.email_processor | 处理邮件主题筛选时出错: 'int' object has no attribute 'decode'
2025-06-24 16:31:49 | ERROR    | app.utils.email_processor | 处理邮件主题筛选时出错: 'int' object has no attribute 'decode'
2025-06-24 16:31:49 | ERROR    | app.utils.email_processor | 处理邮件主题筛选时出错: 'int' object has no attribute 'decode'
2025-06-24 16:31:49 | ERROR    | app.utils.email_processor | 处理邮件主题筛选时出错: 'int' object has no attribute 'decode'
2025-06-24 16:31:49 | ERROR    | app.utils.email_processor | 处理邮件主题筛选时出错: 'int' object has no attribute 'decode'
2025-06-24 16:31:49 | ERROR    | app.utils.email_processor | 处理邮件主题筛选时出错: 'int' object has no attribute 'decode'
2025-06-24 16:31:51 | ERROR    | app.utils.email_processor | 处理邮件主题筛选时出错: 'int' object has no attribute 'decode'
2025-06-24 16:31:52 | ERROR    | app.utils.email_processor | 处理邮件主题筛选时出错: 'int' object has no attribute 'decode'
2025-06-24 16:31:53 | ERROR    | app.utils.email_processor | 处理邮件主题筛选时出错: 'int' object has no attribute 'decode'
2025-06-24 16:31:53 | ERROR    | app.utils.email_processor | 处理邮件主题筛选时出错: 'int' object has no attribute 'decode'
2025-06-24 16:31:53 | ERROR    | app.utils.email_processor | 处理邮件主题筛选时出错: 'int' object has no attribute 'decode'
2025-06-24 16:31:54 | ERROR    | app.utils.email_processor | 处理邮件主题筛选时出错: 'int' object has no attribute 'decode'
2025-06-24 16:31:55 | ERROR    | app.utils.email_processor | 处理邮件主题筛选时出错: 'int' object has no attribute 'decode'
2025-06-24 16:31:55 | ERROR    | app.utils.email_processor | 处理邮件主题筛选时出错: 'int' object has no attribute 'decode'
2025-06-24 16:31:55 | ERROR    | app.utils.email_processor | 处理邮件主题筛选时出错: 'int' object has no attribute 'decode'
2025-06-24 16:31:55 | ERROR    | app.utils.email_processor | 处理邮件主题筛选时出错: 'int' object has no attribute 'decode'
2025-06-24 16:31:55 | ERROR    | app.utils.email_processor | 处理邮件主题筛选时出错: 'int' object has no attribute 'decode'
2025-06-24 16:31:55 | ERROR    | app.utils.email_processor | 处理邮件主题筛选时出错: 'int' object has no attribute 'decode'
2025-06-24 16:31:55 | ERROR    | app.utils.email_processor | 处理邮件主题筛选时出错: 'int' object has no attribute 'decode'
2025-06-24 16:31:55 | ERROR    | app.utils.email_processor | 处理邮件主题筛选时出错: 'int' object has no attribute 'decode'
2025-06-24 16:31:55 | ERROR    | app.utils.email_processor | 处理邮件主题筛选时出错: 'int' object has no attribute 'decode'
2025-06-24 16:31:55 | ERROR    | app.utils.email_processor | 处理邮件主题筛选时出错: 'int' object has no attribute 'decode'
2025-06-24 16:31:55 | ERROR    | app.utils.email_processor | 处理邮件主题筛选时出错: 'int' object has no attribute 'decode'
2025-06-24 16:31:55 | ERROR    | app.utils.email_processor | 处理邮件主题筛选时出错: 'int' object has no attribute 'decode'
2025-06-24 16:31:55 | ERROR    | app.utils.email_processor | 处理邮件主题筛选时出错: 'int' object has no attribute 'decode'
2025-06-24 16:31:55 | ERROR    | app.utils.email_processor | 处理邮件主题筛选时出错: 'int' object has no attribute 'decode'
2025-06-24 16:31:55 | ERROR    | app.utils.email_processor | 处理邮件主题筛选时出错: 'int' object has no attribute 'decode'
2025-06-24 16:31:55 | ERROR    | app.utils.email_processor | 处理邮件主题筛选时出错: 'int' object has no attribute 'decode'
2025-06-24 16:31:55 | ERROR    | app.utils.email_processor | 处理邮件主题筛选时出错: 'int' object has no attribute 'decode'
2025-06-24 16:31:55 | ERROR    | app.utils.email_processor | 处理邮件主题筛选时出错: 'int' object has no attribute 'decode'
2025-06-24 16:31:57 | ERROR    | app.utils.email_processor | 处理邮件主题筛选时出错: 'int' object has no attribute 'decode'
2025-06-24 16:31:58 | ERROR    | app.utils.email_processor | 处理邮件主题筛选时出错: 'int' object has no attribute 'decode'
2025-06-24 16:31:58 | ERROR    | app.utils.email_processor | 处理邮件主题筛选时出错: 'int' object has no attribute 'decode'
2025-06-24 16:31:58 | ERROR    | app.utils.email_processor | 处理邮件主题筛选时出错: 'int' object has no attribute 'decode'
2025-06-24 16:31:58 | ERROR    | app.utils.email_processor | 处理邮件主题筛选时出错: 'int' object has no attribute 'decode'
2025-06-24 16:31:58 | ERROR    | app.utils.email_processor | 处理邮件主题筛选时出错: 'int' object has no attribute 'decode'
2025-06-24 16:32:02 | ERROR    | app.utils.email_processor | 处理邮件主题筛选时出错: 'int' object has no attribute 'decode'
2025-06-24 16:32:02 | ERROR    | app.utils.email_processor | 处理邮件主题筛选时出错: 'int' object has no attribute 'decode'
2025-06-24 16:32:02 | ERROR    | app.utils.email_processor | 处理邮件主题筛选时出错: 'int' object has no attribute 'decode'
2025-06-24 16:32:02 | ERROR    | app.utils.email_processor | 处理邮件主题筛选时出错: 'int' object has no attribute 'decode'
2025-06-24 16:32:03 | ERROR    | app.utils.email_processor | 处理邮件主题筛选时出错: 'int' object has no attribute 'decode'
2025-06-24 16:32:03 | ERROR    | app.utils.email_processor | 处理邮件主题筛选时出错: 'int' object has no attribute 'decode'
2025-06-24 16:32:04 | ERROR    | app.utils.email_processor | 处理邮件主题筛选时出错: 'int' object has no attribute 'decode'
2025-06-24 16:32:04 | ERROR    | app.utils.email_processor | 处理邮件主题筛选时出错: 'int' object has no attribute 'decode'
2025-06-24 16:32:04 | ERROR    | app.utils.email_processor | 处理邮件主题筛选时出错: 'int' object has no attribute 'decode'
2025-06-24 16:32:04 | ERROR    | app.utils.email_processor | 处理邮件主题筛选时出错: 'int' object has no attribute 'decode'
2025-06-24 16:32:05 | ERROR    | app.utils.email_processor | 处理邮件主题筛选时出错: 'int' object has no attribute 'decode'
2025-06-24 16:32:05 | ERROR    | app.utils.email_processor | 处理邮件主题筛选时出错: 'int' object has no attribute 'decode'
2025-06-24 16:32:05 | ERROR    | app.utils.email_processor | 处理邮件主题筛选时出错: 'int' object has no attribute 'decode'
2025-06-24 16:32:05 | ERROR    | app.utils.email_processor | 处理邮件主题筛选时出错: 'int' object has no attribute 'decode'
2025-06-24 16:32:05 | ERROR    | app.utils.email_processor | 处理邮件主题筛选时出错: 'int' object has no attribute 'decode'
2025-06-24 16:32:05 | ERROR    | app.utils.email_processor | 处理邮件主题筛选时出错: 'int' object has no attribute 'decode'
2025-06-24 16:32:05 | ERROR    | app.utils.email_processor | 处理邮件主题筛选时出错: 'int' object has no attribute 'decode'
2025-06-24 16:32:05 | ERROR    | app.utils.email_processor | 处理邮件主题筛选时出错: 'int' object has no attribute 'decode'
2025-06-24 16:32:05 | ERROR    | app.utils.email_processor | 处理邮件主题筛选时出错: 'int' object has no attribute 'decode'
2025-06-24 16:32:05 | ERROR    | app.utils.email_processor | 处理邮件主题筛选时出错: 'int' object has no attribute 'decode'
2025-06-24 16:32:05 | ERROR    | app.utils.email_processor | 处理邮件主题筛选时出错: 'int' object has no attribute 'decode'
2025-06-24 16:32:05 | ERROR    | app.utils.email_processor | 处理邮件主题筛选时出错: 'int' object has no attribute 'decode'
2025-06-24 16:32:05 | ERROR    | app.utils.email_processor | 处理邮件主题筛选时出错: 'int' object has no attribute 'decode'
2025-06-24 16:32:05 | ERROR    | app.utils.email_processor | 处理邮件主题筛选时出错: 'int' object has no attribute 'decode'
2025-06-24 16:32:06 | ERROR    | app.utils.email_processor | 处理邮件主题筛选时出错: 'int' object has no attribute 'decode'
2025-06-24 16:32:06 | ERROR    | app.utils.email_processor | 处理邮件主题筛选时出错: 'int' object has no attribute 'decode'
2025-06-24 16:32:06 | ERROR    | app.utils.email_processor | 处理邮件主题筛选时出错: 'int' object has no attribute 'decode'
2025-06-24 16:32:06 | ERROR    | app.utils.email_processor | 处理邮件主题筛选时出错: 'int' object has no attribute 'decode'
2025-06-24 16:32:07 | ERROR    | app.utils.email_processor | 处理邮件主题筛选时出错: 'int' object has no attribute 'decode'
2025-06-24 16:32:07 | ERROR    | app.utils.email_processor | 处理邮件主题筛选时出错: 'int' object has no attribute 'decode'
2025-06-24 16:32:07 | ERROR    | app.utils.email_processor | 处理邮件主题筛选时出错: 'int' object has no attribute 'decode'
2025-06-24 16:32:07 | ERROR    | app.utils.email_processor | 处理邮件主题筛选时出错: 'int' object has no attribute 'decode'
2025-06-24 16:32:08 | ERROR    | app.utils.email_processor | 处理邮件主题筛选时出错: 'int' object has no attribute 'decode'
2025-06-24 16:32:08 | ERROR    | app.utils.email_processor | 处理邮件主题筛选时出错: 'int' object has no attribute 'decode'
2025-06-24 16:32:08 | ERROR    | app.utils.email_processor | 处理邮件主题筛选时出错: 'int' object has no attribute 'decode'
2025-06-24 16:32:08 | ERROR    | app.utils.email_processor | 处理邮件主题筛选时出错: 'int' object has no attribute 'decode'
2025-06-24 16:32:09 | ERROR    | app.utils.email_processor | 处理邮件主题筛选时出错: 'int' object has no attribute 'decode'
2025-06-24 16:32:09 | ERROR    | app.utils.email_processor | 处理邮件主题筛选时出错: 'int' object has no attribute 'decode'
2025-06-24 16:32:09 | ERROR    | app.utils.email_processor | 处理邮件主题筛选时出错: 'int' object has no attribute 'decode'
2025-06-24 16:32:09 | ERROR    | app.utils.email_processor | 处理邮件主题筛选时出错: 'int' object has no attribute 'decode'
2025-06-24 16:32:09 | ERROR    | app.utils.email_processor | 处理邮件主题筛选时出错: 'int' object has no attribute 'decode'
2025-06-24 16:32:09 | ERROR    | app.utils.email_processor | 处理邮件主题筛选时出错: 'int' object has no attribute 'decode'
2025-06-24 17:13:46 | ERROR    | app.utils.email_processor | 处理邮件主题筛选时出错: 'int' object has no attribute 'decode'
2025-06-24 17:13:47 | ERROR    | app.utils.email_processor | 处理邮件主题筛选时出错: 'int' object has no attribute 'decode'
2025-06-24 17:13:49 | ERROR    | app.utils.email_processor | 处理邮件主题筛选时出错: 'int' object has no attribute 'decode'
2025-06-24 17:13:49 | ERROR    | app.utils.email_processor | 处理邮件主题筛选时出错: 'int' object has no attribute 'decode'
2025-06-24 17:13:49 | ERROR    | app.utils.email_processor | 处理邮件主题筛选时出错: 'int' object has no attribute 'decode'
2025-06-24 17:13:49 | ERROR    | app.utils.email_processor | 处理邮件主题筛选时出错: 'int' object has no attribute 'decode'
2025-06-24 17:13:49 | ERROR    | app.utils.email_processor | 处理邮件主题筛选时出错: 'int' object has no attribute 'decode'
2025-06-24 17:13:49 | ERROR    | app.utils.email_processor | 处理邮件主题筛选时出错: 'int' object has no attribute 'decode'
2025-06-24 17:13:49 | ERROR    | app.utils.email_processor | 处理邮件主题筛选时出错: 'int' object has no attribute 'decode'
2025-06-24 17:13:50 | ERROR    | app.utils.email_processor | 处理邮件主题筛选时出错: 'int' object has no attribute 'decode'
2025-06-24 17:13:50 | ERROR    | app.utils.email_processor | 处理邮件主题筛选时出错: 'int' object has no attribute 'decode'
2025-06-24 17:13:50 | ERROR    | app.utils.email_processor | 处理邮件主题筛选时出错: 'int' object has no attribute 'decode'
2025-06-24 17:13:50 | ERROR    | app.utils.email_processor | 处理邮件主题筛选时出错: 'int' object has no attribute 'decode'
2025-06-24 17:13:50 | ERROR    | app.utils.email_processor | 处理邮件主题筛选时出错: 'int' object has no attribute 'decode'
2025-06-24 17:13:51 | ERROR    | app.utils.email_processor | 处理邮件主题筛选时出错: 'int' object has no attribute 'decode'
2025-06-24 17:13:52 | ERROR    | app.utils.email_processor | 处理邮件主题筛选时出错: 'int' object has no attribute 'decode'
2025-06-24 17:13:52 | ERROR    | app.utils.email_processor | 处理邮件主题筛选时出错: 'int' object has no attribute 'decode'
2025-06-24 17:13:52 | ERROR    | app.utils.email_processor | 处理邮件主题筛选时出错: 'int' object has no attribute 'decode'
2025-06-24 17:13:52 | ERROR    | app.utils.email_processor | 处理邮件主题筛选时出错: 'int' object has no attribute 'decode'
2025-06-24 17:13:53 | ERROR    | app.utils.email_processor | 处理邮件主题筛选时出错: 'int' object has no attribute 'decode'
2025-06-24 17:13:53 | ERROR    | app.utils.email_processor | 处理邮件主题筛选时出错: 'int' object has no attribute 'decode'
2025-06-24 17:13:53 | ERROR    | app.utils.email_processor | 处理邮件主题筛选时出错: 'int' object has no attribute 'decode'
2025-06-24 17:13:54 | ERROR    | app.utils.email_processor | 处理邮件主题筛选时出错: 'int' object has no attribute 'decode'
2025-06-24 17:13:54 | ERROR    | app.utils.email_processor | 处理邮件主题筛选时出错: 'int' object has no attribute 'decode'
2025-06-24 17:13:56 | ERROR    | app.utils.email_processor | 处理邮件主题筛选时出错: 'int' object has no attribute 'decode'
2025-06-24 17:13:56 | ERROR    | app.utils.email_processor | 处理邮件主题筛选时出错: 'int' object has no attribute 'decode'
2025-06-24 17:13:56 | ERROR    | app.utils.email_processor | 处理邮件主题筛选时出错: 'int' object has no attribute 'decode'
2025-06-24 17:13:56 | ERROR    | app.utils.email_processor | 处理邮件主题筛选时出错: 'int' object has no attribute 'decode'
2025-06-24 17:13:56 | ERROR    | app.utils.email_processor | 处理邮件主题筛选时出错: 'int' object has no attribute 'decode'
2025-06-24 17:13:56 | ERROR    | app.utils.email_processor | 处理邮件主题筛选时出错: 'int' object has no attribute 'decode'
2025-06-24 17:13:56 | ERROR    | app.utils.email_processor | 处理邮件主题筛选时出错: 'int' object has no attribute 'decode'
2025-06-24 17:13:56 | ERROR    | app.utils.email_processor | 处理邮件主题筛选时出错: 'int' object has no attribute 'decode'
2025-06-24 17:13:56 | ERROR    | app.utils.email_processor | 处理邮件主题筛选时出错: 'int' object has no attribute 'decode'
2025-06-24 17:13:56 | ERROR    | app.utils.email_processor | 处理邮件主题筛选时出错: 'int' object has no attribute 'decode'
2025-06-24 17:13:56 | ERROR    | app.utils.email_processor | 处理邮件主题筛选时出错: 'int' object has no attribute 'decode'
2025-06-24 17:13:56 | ERROR    | app.utils.email_processor | 处理邮件主题筛选时出错: 'int' object has no attribute 'decode'
2025-06-24 17:13:57 | ERROR    | app.utils.email_processor | 处理邮件主题筛选时出错: 'int' object has no attribute 'decode'
2025-06-24 17:13:57 | ERROR    | app.utils.email_processor | 处理邮件主题筛选时出错: 'int' object has no attribute 'decode'
2025-06-24 17:13:57 | ERROR    | app.utils.email_processor | 处理邮件主题筛选时出错: 'int' object has no attribute 'decode'
2025-06-24 17:13:57 | ERROR    | app.utils.email_processor | 处理邮件主题筛选时出错: 'int' object has no attribute 'decode'
2025-06-24 17:13:57 | ERROR    | app.utils.email_processor | 处理邮件主题筛选时出错: 'int' object has no attribute 'decode'
2025-06-24 17:13:57 | ERROR    | app.utils.email_processor | 处理邮件主题筛选时出错: 'int' object has no attribute 'decode'
2025-06-24 17:13:57 | ERROR    | app.utils.email_processor | 处理邮件主题筛选时出错: 'int' object has no attribute 'decode'
2025-06-24 17:13:57 | ERROR    | app.utils.email_processor | 处理邮件主题筛选时出错: 'int' object has no attribute 'decode'
2025-06-24 17:13:58 | ERROR    | app.utils.email_processor | 处理邮件主题筛选时出错: 'int' object has no attribute 'decode'
2025-06-24 17:13:58 | ERROR    | app.utils.email_processor | 处理邮件主题筛选时出错: 'int' object has no attribute 'decode'
2025-06-24 17:13:58 | ERROR    | app.utils.email_processor | 处理邮件主题筛选时出错: 'int' object has no attribute 'decode'
2025-06-24 17:13:58 | ERROR    | app.utils.email_processor | 处理邮件主题筛选时出错: 'int' object has no attribute 'decode'
2025-06-24 17:13:58 | ERROR    | app.utils.email_processor | 处理邮件主题筛选时出错: 'int' object has no attribute 'decode'
2025-06-24 17:13:58 | ERROR    | app.utils.email_processor | 处理邮件主题筛选时出错: 'int' object has no attribute 'decode'
2025-06-24 17:14:00 | ERROR    | app.utils.email_processor | 处理邮件主题筛选时出错: 'int' object has no attribute 'decode'
2025-06-24 17:14:00 | ERROR    | app.utils.email_processor | 处理邮件主题筛选时出错: 'int' object has no attribute 'decode'
2025-06-24 17:14:00 | ERROR    | app.utils.email_processor | 处理邮件主题筛选时出错: 'int' object has no attribute 'decode'
2025-06-24 17:14:00 | ERROR    | app.utils.email_processor | 处理邮件主题筛选时出错: 'int' object has no attribute 'decode'
2025-06-24 17:14:01 | ERROR    | app.utils.email_processor | 处理邮件主题筛选时出错: 'int' object has no attribute 'decode'
2025-06-24 17:14:01 | ERROR    | app.utils.email_processor | 处理邮件主题筛选时出错: 'int' object has no attribute 'decode'
2025-06-24 17:14:03 | ERROR    | app.utils.email_processor | 处理邮件主题筛选时出错: 'int' object has no attribute 'decode'
2025-06-24 17:14:03 | ERROR    | app.utils.email_processor | 处理邮件主题筛选时出错: 'int' object has no attribute 'decode'
2025-06-24 17:14:03 | ERROR    | app.utils.email_processor | 处理邮件主题筛选时出错: 'int' object has no attribute 'decode'
2025-06-24 17:14:03 | ERROR    | app.utils.email_processor | 处理邮件主题筛选时出错: 'int' object has no attribute 'decode'
2025-06-24 17:14:03 | ERROR    | app.utils.email_processor | 处理邮件主题筛选时出错: 'int' object has no attribute 'decode'
2025-06-24 17:14:03 | ERROR    | app.utils.email_processor | 处理邮件主题筛选时出错: 'int' object has no attribute 'decode'
2025-06-24 17:14:03 | ERROR    | app.utils.email_processor | 处理邮件主题筛选时出错: 'int' object has no attribute 'decode'
2025-06-24 17:14:03 | ERROR    | app.utils.email_processor | 处理邮件主题筛选时出错: 'int' object has no attribute 'decode'
2025-06-24 17:14:03 | ERROR    | app.utils.email_processor | 处理邮件主题筛选时出错: 'int' object has no attribute 'decode'
2025-06-24 17:14:03 | ERROR    | app.utils.email_processor | 处理邮件主题筛选时出错: 'int' object has no attribute 'decode'
2025-06-24 17:14:03 | ERROR    | app.utils.email_processor | 处理邮件主题筛选时出错: 'int' object has no attribute 'decode'
2025-06-24 17:14:03 | ERROR    | app.utils.email_processor | 处理邮件主题筛选时出错: 'int' object has no attribute 'decode'
2025-06-24 17:14:03 | ERROR    | app.utils.email_processor | 处理邮件主题筛选时出错: 'int' object has no attribute 'decode'
2025-06-24 17:14:03 | ERROR    | app.utils.email_processor | 处理邮件主题筛选时出错: 'int' object has no attribute 'decode'
2025-06-24 17:14:06 | ERROR    | app.utils.email_processor | 处理邮件主题筛选时出错: 'int' object has no attribute 'decode'
2025-06-24 17:14:06 | ERROR    | app.utils.email_processor | 处理邮件主题筛选时出错: 'int' object has no attribute 'decode'
2025-06-24 17:14:08 | ERROR    | app.utils.email_processor | 处理邮件主题筛选时出错: 'int' object has no attribute 'decode'
2025-06-24 17:14:08 | ERROR    | app.utils.email_processor | 处理邮件主题筛选时出错: 'int' object has no attribute 'decode'
2025-06-24 17:14:08 | ERROR    | app.utils.email_processor | 处理邮件主题筛选时出错: 'int' object has no attribute 'decode'
2025-06-24 17:14:09 | ERROR    | app.utils.email_processor | 处理邮件主题筛选时出错: 'int' object has no attribute 'decode'
2025-06-24 17:14:10 | ERROR    | app.utils.email_processor | 处理邮件主题筛选时出错: 'int' object has no attribute 'decode'
2025-06-24 17:14:10 | ERROR    | app.utils.email_processor | 处理邮件主题筛选时出错: 'int' object has no attribute 'decode'
2025-06-24 17:14:10 | ERROR    | app.utils.email_processor | 处理邮件主题筛选时出错: 'int' object has no attribute 'decode'
2025-06-24 17:14:10 | ERROR    | app.utils.email_processor | 处理邮件主题筛选时出错: 'int' object has no attribute 'decode'
2025-06-24 17:14:10 | ERROR    | app.utils.email_processor | 处理邮件主题筛选时出错: 'int' object has no attribute 'decode'
2025-06-24 17:14:10 | ERROR    | app.utils.email_processor | 处理邮件主题筛选时出错: 'int' object has no attribute 'decode'
2025-06-24 17:14:10 | ERROR    | app.utils.email_processor | 处理邮件主题筛选时出错: 'int' object has no attribute 'decode'
2025-06-24 17:14:10 | ERROR    | app.utils.email_processor | 处理邮件主题筛选时出错: 'int' object has no attribute 'decode'
2025-06-24 17:14:10 | ERROR    | app.utils.email_processor | 处理邮件主题筛选时出错: 'int' object has no attribute 'decode'
2025-06-24 17:14:10 | ERROR    | app.utils.email_processor | 处理邮件主题筛选时出错: 'int' object has no attribute 'decode'
2025-06-24 17:14:10 | ERROR    | app.utils.email_processor | 处理邮件主题筛选时出错: 'int' object has no attribute 'decode'
2025-06-24 17:14:10 | ERROR    | app.utils.email_processor | 处理邮件主题筛选时出错: 'int' object has no attribute 'decode'
2025-06-24 17:14:10 | ERROR    | app.utils.email_processor | 处理邮件主题筛选时出错: 'int' object has no attribute 'decode'
2025-06-24 17:14:10 | ERROR    | app.utils.email_processor | 处理邮件主题筛选时出错: 'int' object has no attribute 'decode'
2025-06-24 17:14:10 | ERROR    | app.utils.email_processor | 处理邮件主题筛选时出错: 'int' object has no attribute 'decode'
2025-06-24 17:14:10 | ERROR    | app.utils.email_processor | 处理邮件主题筛选时出错: 'int' object has no attribute 'decode'
2025-06-24 17:14:10 | ERROR    | app.utils.email_processor | 处理邮件主题筛选时出错: 'int' object has no attribute 'decode'
2025-06-24 17:14:10 | ERROR    | app.utils.email_processor | 处理邮件主题筛选时出错: 'int' object has no attribute 'decode'
2025-06-24 17:14:13 | ERROR    | app.utils.email_processor | 处理邮件主题筛选时出错: 'int' object has no attribute 'decode'
2025-06-24 17:14:13 | ERROR    | app.utils.email_processor | 处理邮件主题筛选时出错: 'int' object has no attribute 'decode'
2025-06-24 17:14:13 | ERROR    | app.utils.email_processor | 处理邮件主题筛选时出错: 'int' object has no attribute 'decode'
2025-06-24 17:14:13 | ERROR    | app.utils.email_processor | 处理邮件主题筛选时出错: 'int' object has no attribute 'decode'
2025-06-24 17:14:13 | ERROR    | app.utils.email_processor | 处理邮件主题筛选时出错: 'int' object has no attribute 'decode'
2025-06-24 17:14:13 | ERROR    | app.utils.email_processor | 处理邮件主题筛选时出错: 'int' object has no attribute 'decode'
2025-06-24 17:14:18 | ERROR    | app.utils.email_processor | 处理邮件主题筛选时出错: 'int' object has no attribute 'decode'
2025-06-24 17:14:18 | ERROR    | app.utils.email_processor | 处理邮件主题筛选时出错: 'int' object has no attribute 'decode'
2025-06-24 17:14:18 | ERROR    | app.utils.email_processor | 处理邮件主题筛选时出错: 'int' object has no attribute 'decode'
2025-06-24 17:14:18 | ERROR    | app.utils.email_processor | 处理邮件主题筛选时出错: 'int' object has no attribute 'decode'
2025-06-24 17:14:18 | ERROR    | app.utils.email_processor | 处理邮件主题筛选时出错: 'int' object has no attribute 'decode'
2025-06-24 17:14:18 | ERROR    | app.utils.email_processor | 处理邮件主题筛选时出错: 'int' object has no attribute 'decode'
2025-06-24 17:14:20 | ERROR    | app.utils.email_processor | 处理邮件主题筛选时出错: 'int' object has no attribute 'decode'
2025-06-24 17:14:20 | ERROR    | app.utils.email_processor | 处理邮件主题筛选时出错: 'int' object has no attribute 'decode'
2025-06-24 17:14:20 | ERROR    | app.utils.email_processor | 处理邮件主题筛选时出错: 'int' object has no attribute 'decode'
2025-06-24 17:14:20 | ERROR    | app.utils.email_processor | 处理邮件主题筛选时出错: 'int' object has no attribute 'decode'
2025-06-24 17:14:20 | ERROR    | app.utils.email_processor | 处理邮件主题筛选时出错: 'int' object has no attribute 'decode'
2025-06-24 17:14:20 | ERROR    | app.utils.email_processor | 处理邮件主题筛选时出错: 'int' object has no attribute 'decode'
2025-06-24 17:14:20 | ERROR    | app.utils.email_processor | 处理邮件主题筛选时出错: 'int' object has no attribute 'decode'
2025-06-24 17:14:20 | ERROR    | app.utils.email_processor | 处理邮件主题筛选时出错: 'int' object has no attribute 'decode'
2025-06-24 17:14:20 | ERROR    | app.utils.email_processor | 处理邮件主题筛选时出错: 'int' object has no attribute 'decode'
2025-06-24 17:14:21 | ERROR    | app.utils.email_processor | 处理邮件主题筛选时出错: 'int' object has no attribute 'decode'
2025-06-24 17:14:21 | ERROR    | app.utils.email_processor | 处理邮件主题筛选时出错: 'int' object has no attribute 'decode'
2025-06-24 17:14:21 | ERROR    | app.utils.email_processor | 处理邮件主题筛选时出错: 'int' object has no attribute 'decode'
2025-06-24 17:14:21 | ERROR    | app.utils.email_processor | 处理邮件主题筛选时出错: 'int' object has no attribute 'decode'
2025-06-24 17:14:21 | ERROR    | app.utils.email_processor | 处理邮件主题筛选时出错: 'int' object has no attribute 'decode'
2025-06-24 17:14:21 | ERROR    | app.utils.email_processor | 处理邮件主题筛选时出错: 'int' object has no attribute 'decode'
2025-06-24 17:14:21 | ERROR    | app.utils.email_processor | 处理邮件主题筛选时出错: 'int' object has no attribute 'decode'
2025-06-24 17:14:21 | ERROR    | app.utils.email_processor | 处理邮件主题筛选时出错: 'int' object has no attribute 'decode'
2025-06-24 17:14:21 | ERROR    | app.utils.email_processor | 处理邮件主题筛选时出错: 'int' object has no attribute 'decode'
2025-06-24 17:14:21 | ERROR    | app.utils.email_processor | 处理邮件主题筛选时出错: 'int' object has no attribute 'decode'
2025-06-24 17:14:21 | ERROR    | app.utils.email_processor | 处理邮件主题筛选时出错: 'int' object has no attribute 'decode'
2025-06-24 17:14:22 | ERROR    | app.utils.email_processor | 处理邮件主题筛选时出错: 'int' object has no attribute 'decode'
2025-06-24 17:14:22 | ERROR    | app.utils.email_processor | 处理邮件主题筛选时出错: 'int' object has no attribute 'decode'
2025-06-24 17:14:23 | ERROR    | app.utils.email_processor | 处理邮件主题筛选时出错: 'int' object has no attribute 'decode'
2025-06-24 17:14:23 | ERROR    | app.utils.email_processor | 处理邮件主题筛选时出错: 'int' object has no attribute 'decode'
2025-06-24 17:14:23 | ERROR    | app.utils.email_processor | 处理邮件主题筛选时出错: 'int' object has no attribute 'decode'
2025-06-24 17:14:23 | ERROR    | app.utils.email_processor | 处理邮件主题筛选时出错: 'int' object has no attribute 'decode'
2025-06-24 17:14:24 | ERROR    | app.utils.email_processor | 处理邮件主题筛选时出错: 'int' object has no attribute 'decode'
2025-06-24 17:14:24 | ERROR    | app.utils.email_processor | 处理邮件主题筛选时出错: 'int' object has no attribute 'decode'
2025-06-24 17:14:24 | ERROR    | app.utils.email_processor | 处理邮件主题筛选时出错: 'int' object has no attribute 'decode'
2025-06-24 17:14:24 | ERROR    | app.utils.email_processor | 处理邮件主题筛选时出错: 'int' object has no attribute 'decode'
2025-06-24 17:14:25 | ERROR    | app.utils.email_processor | 处理邮件主题筛选时出错: 'int' object has no attribute 'decode'
2025-06-24 17:14:25 | ERROR    | app.utils.email_processor | 处理邮件主题筛选时出错: 'int' object has no attribute 'decode'
2025-06-24 17:14:25 | ERROR    | app.utils.email_processor | 处理邮件主题筛选时出错: 'int' object has no attribute 'decode'
2025-06-24 17:14:25 | ERROR    | app.utils.email_processor | 处理邮件主题筛选时出错: 'int' object has no attribute 'decode'
2025-06-24 17:14:25 | ERROR    | app.utils.email_processor | 处理邮件主题筛选时出错: 'int' object has no attribute 'decode'
2025-06-24 17:14:25 | ERROR    | app.utils.email_processor | 处理邮件主题筛选时出错: 'int' object has no attribute 'decode'
2025-06-24 17:14:26 | ERROR    | app.utils.email_processor | 处理邮件主题筛选时出错: 'int' object has no attribute 'decode'
2025-06-24 17:14:26 | ERROR    | app.utils.email_processor | 处理邮件主题筛选时出错: 'int' object has no attribute 'decode'
2025-06-24 17:34:46 | ERROR    | app.utils.email_processor | 处理邮件时出错: 'int' object has no attribute 'decode'
2025-06-24 17:34:46 | ERROR    | app.utils.email_processor | 处理邮件时出错: 'int' object has no attribute 'decode'
2025-06-24 17:35:30 | ERROR    | app.utils.email_processor | 处理邮件时出错: 'int' object has no attribute 'decode'
2025-06-24 17:35:43 | ERROR    | app.utils.email_processor | 处理邮件时出错: 'int' object has no attribute 'decode'
2025-06-24 17:35:43 | ERROR    | app.utils.email_processor | 处理邮件时出错: 'int' object has no attribute 'decode'
2025-06-24 17:35:43 | ERROR    | app.utils.email_processor | 处理邮件时出错: 'int' object has no attribute 'decode'
2025-06-24 17:35:45 | ERROR    | app.utils.email_processor | 处理邮件时出错: 'int' object has no attribute 'decode'
2025-06-24 17:35:45 | ERROR    | app.utils.email_processor | 处理邮件时出错: 'int' object has no attribute 'decode'
2025-06-24 17:35:45 | ERROR    | app.utils.email_processor | 处理邮件时出错: 'int' object has no attribute 'decode'
2025-06-24 17:35:45 | ERROR    | app.utils.email_processor | 处理邮件时出错: 'int' object has no attribute 'decode'
2025-06-24 17:35:45 | ERROR    | app.utils.email_processor | 处理邮件时出错: 'int' object has no attribute 'decode'
2025-06-24 17:35:46 | ERROR    | app.utils.email_processor | 处理邮件时出错: 'int' object has no attribute 'decode'
2025-06-24 17:35:46 | ERROR    | app.utils.email_processor | 处理邮件时出错: 'int' object has no attribute 'decode'
2025-06-24 17:35:46 | ERROR    | app.utils.email_processor | 处理邮件时出错: 'int' object has no attribute 'decode'
2025-06-24 17:35:47 | ERROR    | app.utils.email_processor | 处理邮件时出错: 'int' object has no attribute 'decode'
2025-06-24 17:35:47 | ERROR    | app.utils.email_processor | 处理邮件时出错: 'int' object has no attribute 'decode'
2025-06-24 17:35:55 | ERROR    | app.utils.email_processor | 处理邮件时出错: 'int' object has no attribute 'decode'
2025-06-24 17:35:57 | ERROR    | app.utils.email_processor | 处理邮件时出错: 'int' object has no attribute 'decode'
2025-06-24 17:35:57 | ERROR    | app.utils.email_processor | 处理邮件时出错: 'int' object has no attribute 'decode'
2025-06-24 17:35:57 | ERROR    | app.utils.email_processor | 处理邮件时出错: 'int' object has no attribute 'decode'
2025-06-24 17:36:03 | ERROR    | app.utils.email_processor | 处理邮件时出错: 'int' object has no attribute 'decode'
2025-06-24 17:36:03 | ERROR    | app.utils.email_processor | 处理邮件时出错: 'int' object has no attribute 'decode'
2025-06-24 17:37:19 | ERROR    | app.utils.email_processor | 处理邮件时出错: 'NoneType' object is not subscriptable
2025-06-24 17:37:25 | ERROR    | app.utils.email_processor | 处理邮件主题筛选时出错: 'int' object has no attribute 'decode'
2025-06-24 17:37:27 | ERROR    | app.utils.email_processor | 处理邮件主题筛选时出错: 'int' object has no attribute 'decode'
2025-06-24 17:37:27 | ERROR    | app.utils.email_processor | 处理邮件主题筛选时出错: 'int' object has no attribute 'decode'
2025-06-24 17:37:27 | ERROR    | app.utils.email_processor | 处理邮件主题筛选时出错: 'int' object has no attribute 'decode'
2025-06-24 17:37:28 | ERROR    | app.utils.email_processor | 处理邮件主题筛选时出错: 'int' object has no attribute 'decode'
2025-06-24 17:37:28 | ERROR    | app.utils.email_processor | 处理邮件主题筛选时出错: 'int' object has no attribute 'decode'
2025-06-24 17:37:28 | ERROR    | app.utils.email_processor | 处理邮件主题筛选时出错: 'int' object has no attribute 'decode'
2025-06-24 17:37:30 | ERROR    | app.utils.email_processor | 处理邮件主题筛选时出错: 'int' object has no attribute 'decode'
2025-06-24 17:37:31 | ERROR    | app.utils.email_processor | 处理邮件主题筛选时出错: 'int' object has no attribute 'decode'
2025-06-24 17:37:31 | ERROR    | app.utils.email_processor | 处理邮件主题筛选时出错: 'int' object has no attribute 'decode'
2025-06-24 17:37:32 | ERROR    | app.utils.email_processor | 处理邮件主题筛选时出错: 'int' object has no attribute 'decode'
2025-06-24 17:37:33 | ERROR    | app.utils.email_processor | 处理邮件主题筛选时出错: 'int' object has no attribute 'decode'
2025-06-24 17:37:34 | ERROR    | app.utils.email_processor | 处理邮件主题筛选时出错: 'int' object has no attribute 'decode'
2025-06-24 17:37:35 | ERROR    | app.utils.email_processor | 处理邮件主题筛选时出错: 'int' object has no attribute 'decode'
2025-06-24 17:37:35 | ERROR    | app.utils.email_processor | 处理邮件主题筛选时出错: 'int' object has no attribute 'decode'
2025-06-24 17:37:35 | ERROR    | app.utils.email_processor | 处理邮件主题筛选时出错: 'int' object has no attribute 'decode'
2025-06-24 17:37:35 | ERROR    | app.utils.email_processor | 处理邮件主题筛选时出错: 'int' object has no attribute 'decode'
2025-06-24 17:37:35 | ERROR    | app.utils.email_processor | 处理邮件主题筛选时出错: 'int' object has no attribute 'decode'
2025-06-24 17:37:36 | ERROR    | app.utils.email_processor | 处理邮件主题筛选时出错: 'int' object has no attribute 'decode'
2025-06-24 17:37:36 | ERROR    | app.utils.email_processor | 处理邮件主题筛选时出错: 'int' object has no attribute 'decode'
2025-06-24 17:37:36 | ERROR    | app.utils.email_processor | 处理邮件主题筛选时出错: 'int' object has no attribute 'decode'
2025-06-24 17:37:36 | ERROR    | app.utils.email_processor | 处理邮件主题筛选时出错: 'int' object has no attribute 'decode'
2025-06-24 17:37:37 | ERROR    | app.utils.email_processor | 处理邮件主题筛选时出错: 'int' object has no attribute 'decode'
2025-06-24 17:37:37 | ERROR    | app.utils.email_processor | 处理邮件主题筛选时出错: 'int' object has no attribute 'decode'
2025-06-24 17:37:37 | ERROR    | app.utils.email_processor | 处理邮件主题筛选时出错: 'int' object has no attribute 'decode'
2025-06-24 17:37:38 | ERROR    | app.utils.email_processor | 处理邮件主题筛选时出错: 'int' object has no attribute 'decode'
2025-06-24 17:37:39 | ERROR    | app.utils.email_processor | 处理邮件主题筛选时出错: 'int' object has no attribute 'decode'
2025-06-24 17:37:39 | ERROR    | app.utils.email_processor | 处理邮件主题筛选时出错: 'int' object has no attribute 'decode'
2025-06-24 17:37:41 | ERROR    | app.utils.email_processor | 处理邮件主题筛选时出错: 'int' object has no attribute 'decode'
2025-06-24 17:37:42 | ERROR    | app.utils.email_processor | 处理邮件主题筛选时出错: 'int' object has no attribute 'decode'
2025-06-24 17:37:42 | ERROR    | app.utils.email_processor | 处理邮件主题筛选时出错: 'int' object has no attribute 'decode'
2025-06-24 17:37:42 | ERROR    | app.utils.email_processor | 处理邮件主题筛选时出错: 'int' object has no attribute 'decode'
2025-06-24 17:37:42 | ERROR    | app.utils.email_processor | 处理邮件主题筛选时出错: 'int' object has no attribute 'decode'
2025-06-24 17:37:42 | ERROR    | app.utils.email_processor | 处理邮件主题筛选时出错: 'int' object has no attribute 'decode'
2025-06-24 17:37:42 | ERROR    | app.utils.email_processor | 处理邮件主题筛选时出错: 'int' object has no attribute 'decode'
2025-06-24 17:37:45 | ERROR    | app.utils.email_processor | 处理邮件主题筛选时出错: 'int' object has no attribute 'decode'
2025-06-24 17:37:47 | ERROR    | app.utils.email_processor | 处理邮件主题筛选时出错: 'int' object has no attribute 'decode'
2025-06-24 17:37:47 | ERROR    | app.utils.email_processor | 处理邮件主题筛选时出错: 'int' object has no attribute 'decode'
2025-06-24 17:37:49 | ERROR    | app.utils.email_processor | 处理邮件主题筛选时出错: 'int' object has no attribute 'decode'
2025-06-24 17:37:50 | ERROR    | app.utils.email_processor | 处理邮件主题筛选时出错: 'int' object has no attribute 'decode'
2025-06-24 17:37:50 | ERROR    | app.utils.email_processor | 处理邮件主题筛选时出错: 'int' object has no attribute 'decode'
2025-06-24 17:37:50 | ERROR    | app.utils.email_processor | 处理邮件主题筛选时出错: 'int' object has no attribute 'decode'
2025-06-24 17:37:50 | ERROR    | app.utils.email_processor | 处理邮件主题筛选时出错: 'int' object has no attribute 'decode'
2025-06-24 17:37:50 | ERROR    | app.utils.email_processor | 处理邮件主题筛选时出错: 'int' object has no attribute 'decode'
2025-06-24 17:37:50 | ERROR    | app.utils.email_processor | 处理邮件主题筛选时出错: 'int' object has no attribute 'decode'
2025-06-24 17:37:50 | ERROR    | app.utils.email_processor | 处理邮件主题筛选时出错: 'int' object has no attribute 'decode'
2025-06-24 17:37:50 | ERROR    | app.utils.email_processor | 处理邮件主题筛选时出错: 'int' object has no attribute 'decode'
2025-06-24 17:37:52 | ERROR    | app.utils.email_processor | 处理邮件主题筛选时出错: 'int' object has no attribute 'decode'
2025-06-24 17:37:52 | ERROR    | app.utils.email_processor | 处理邮件主题筛选时出错: 'int' object has no attribute 'decode'
2025-06-24 17:37:52 | ERROR    | app.utils.email_processor | 处理邮件主题筛选时出错: 'int' object has no attribute 'decode'
2025-06-24 17:37:58 | ERROR    | app.utils.email_processor | 处理邮件主题筛选时出错: 'int' object has no attribute 'decode'
2025-06-24 17:37:58 | ERROR    | app.utils.email_processor | 处理邮件主题筛选时出错: 'int' object has no attribute 'decode'
2025-06-24 17:37:59 | ERROR    | app.utils.email_processor | 处理邮件主题筛选时出错: 'int' object has no attribute 'decode'
2025-06-24 17:38:00 | ERROR    | app.utils.email_processor | 处理邮件主题筛选时出错: 'int' object has no attribute 'decode'
2025-06-24 17:38:00 | ERROR    | app.utils.email_processor | 处理邮件主题筛选时出错: 'int' object has no attribute 'decode'
2025-06-24 17:38:00 | ERROR    | app.utils.email_processor | 处理邮件主题筛选时出错: 'int' object has no attribute 'decode'
2025-06-24 17:38:00 | ERROR    | app.utils.email_processor | 处理邮件主题筛选时出错: 'int' object has no attribute 'decode'
2025-06-24 17:38:01 | ERROR    | app.utils.email_processor | 处理邮件主题筛选时出错: 'int' object has no attribute 'decode'
2025-06-24 17:38:01 | ERROR    | app.utils.email_processor | 处理邮件主题筛选时出错: 'int' object has no attribute 'decode'
2025-06-24 17:38:01 | ERROR    | app.utils.email_processor | 处理邮件主题筛选时出错: 'int' object has no attribute 'decode'
2025-06-24 17:38:01 | ERROR    | app.utils.email_processor | 处理邮件主题筛选时出错: 'int' object has no attribute 'decode'
2025-06-24 17:38:01 | ERROR    | app.utils.email_processor | 处理邮件主题筛选时出错: 'int' object has no attribute 'decode'
2025-06-24 17:38:02 | ERROR    | app.utils.email_processor | 处理邮件主题筛选时出错: 'int' object has no attribute 'decode'
2025-06-24 17:38:02 | ERROR    | app.utils.email_processor | 处理邮件主题筛选时出错: 'int' object has no attribute 'decode'
2025-06-24 17:38:03 | ERROR    | app.utils.email_processor | 处理邮件主题筛选时出错: 'int' object has no attribute 'decode'
2025-06-24 17:38:03 | ERROR    | app.utils.email_processor | 处理邮件主题筛选时出错: 'int' object has no attribute 'decode'
2025-06-24 17:38:04 | ERROR    | app.utils.email_processor | 处理邮件主题筛选时出错: 'int' object has no attribute 'decode'
2025-06-24 17:38:04 | ERROR    | app.utils.email_processor | 处理邮件主题筛选时出错: 'int' object has no attribute 'decode'
2025-06-24 17:38:05 | ERROR    | app.utils.email_processor | 处理邮件主题筛选时出错: 'int' object has no attribute 'decode'
2025-06-24 17:38:05 | ERROR    | app.utils.email_processor | 处理邮件主题筛选时出错: 'int' object has no attribute 'decode'
2025-06-24 17:38:05 | ERROR    | app.utils.email_processor | 处理邮件主题筛选时出错: 'int' object has no attribute 'decode'
2025-06-24 17:38:05 | ERROR    | app.utils.email_processor | 处理邮件主题筛选时出错: 'int' object has no attribute 'decode'
2025-06-24 17:38:12 | ERROR    | app.utils.email_processor | 处理邮件主题筛选时出错: 'NoneType' object is not subscriptable
2025-06-24 18:16:01 | ERROR    | app.utils.email_processor | 处理邮件主题筛选时出错: 'int' object has no attribute 'decode'
2025-06-24 18:16:03 | ERROR    | app.utils.email_processor | 处理邮件主题筛选时出错: 'int' object has no attribute 'decode'
2025-06-24 18:16:03 | ERROR    | app.utils.email_processor | 处理邮件主题筛选时出错: 'int' object has no attribute 'decode'
2025-06-24 18:16:03 | ERROR    | app.utils.email_processor | 处理邮件主题筛选时出错: 'int' object has no attribute 'decode'
2025-06-24 18:16:04 | ERROR    | app.utils.email_processor | 处理邮件主题筛选时出错: 'int' object has no attribute 'decode'
2025-06-24 18:16:04 | ERROR    | app.utils.email_processor | 处理邮件主题筛选时出错: 'int' object has no attribute 'decode'
2025-06-24 18:16:04 | ERROR    | app.utils.email_processor | 处理邮件主题筛选时出错: 'int' object has no attribute 'decode'
2025-06-24 18:16:06 | ERROR    | app.utils.email_processor | 处理邮件主题筛选时出错: 'int' object has no attribute 'decode'
2025-06-24 18:16:08 | ERROR    | app.utils.email_processor | 处理邮件主题筛选时出错: 'int' object has no attribute 'decode'
2025-06-24 18:16:08 | ERROR    | app.utils.email_processor | 处理邮件主题筛选时出错: 'int' object has no attribute 'decode'
2025-06-24 18:16:08 | ERROR    | app.utils.email_processor | 处理邮件主题筛选时出错: 'int' object has no attribute 'decode'
2025-06-24 18:16:09 | ERROR    | app.utils.email_processor | 处理邮件主题筛选时出错: 'int' object has no attribute 'decode'
2025-06-24 18:16:11 | ERROR    | app.utils.email_processor | 处理邮件主题筛选时出错: 'int' object has no attribute 'decode'
2025-06-24 18:16:11 | ERROR    | app.utils.email_processor | 处理邮件主题筛选时出错: 'int' object has no attribute 'decode'
2025-06-24 18:16:11 | ERROR    | app.utils.email_processor | 处理邮件主题筛选时出错: 'int' object has no attribute 'decode'
2025-06-24 18:16:11 | ERROR    | app.utils.email_processor | 处理邮件主题筛选时出错: 'int' object has no attribute 'decode'
2025-06-24 18:16:11 | ERROR    | app.utils.email_processor | 处理邮件主题筛选时出错: 'int' object has no attribute 'decode'
2025-06-24 18:16:11 | ERROR    | app.utils.email_processor | 处理邮件主题筛选时出错: 'int' object has no attribute 'decode'
2025-06-24 18:16:12 | ERROR    | app.utils.email_processor | 处理邮件主题筛选时出错: 'int' object has no attribute 'decode'
2025-06-24 18:16:12 | ERROR    | app.utils.email_processor | 处理邮件主题筛选时出错: 'int' object has no attribute 'decode'
2025-06-24 18:16:12 | ERROR    | app.utils.email_processor | 处理邮件主题筛选时出错: 'int' object has no attribute 'decode'
2025-06-24 18:16:12 | ERROR    | app.utils.email_processor | 处理邮件主题筛选时出错: 'int' object has no attribute 'decode'
2025-06-24 18:16:13 | ERROR    | app.utils.email_processor | 处理邮件主题筛选时出错: 'int' object has no attribute 'decode'
2025-06-24 18:16:13 | ERROR    | app.utils.email_processor | 处理邮件主题筛选时出错: 'int' object has no attribute 'decode'
2025-06-24 18:16:13 | ERROR    | app.utils.email_processor | 处理邮件主题筛选时出错: 'int' object has no attribute 'decode'
2025-06-24 18:16:15 | ERROR    | app.utils.email_processor | 处理邮件主题筛选时出错: 'int' object has no attribute 'decode'
2025-06-24 18:16:15 | ERROR    | app.utils.email_processor | 处理邮件主题筛选时出错: 'int' object has no attribute 'decode'
2025-06-24 18:16:16 | ERROR    | app.utils.email_processor | 处理邮件主题筛选时出错: 'int' object has no attribute 'decode'
2025-06-24 18:16:17 | ERROR    | app.utils.email_processor | 处理邮件主题筛选时出错: 'int' object has no attribute 'decode'
2025-06-24 18:16:18 | ERROR    | app.utils.email_processor | 处理邮件主题筛选时出错: 'int' object has no attribute 'decode'
2025-06-24 18:16:18 | ERROR    | app.utils.email_processor | 处理邮件主题筛选时出错: 'int' object has no attribute 'decode'
2025-06-24 18:16:18 | ERROR    | app.utils.email_processor | 处理邮件主题筛选时出错: 'int' object has no attribute 'decode'
2025-06-24 18:16:18 | ERROR    | app.utils.email_processor | 处理邮件主题筛选时出错: 'int' object has no attribute 'decode'
2025-06-24 18:16:18 | ERROR    | app.utils.email_processor | 处理邮件主题筛选时出错: 'int' object has no attribute 'decode'
2025-06-24 18:16:18 | ERROR    | app.utils.email_processor | 处理邮件主题筛选时出错: 'int' object has no attribute 'decode'
2025-06-24 18:16:21 | ERROR    | app.utils.email_processor | 处理邮件主题筛选时出错: 'int' object has no attribute 'decode'
2025-06-24 18:16:22 | ERROR    | app.utils.email_processor | 处理邮件主题筛选时出错: 'int' object has no attribute 'decode'
2025-06-24 18:16:23 | ERROR    | app.utils.email_processor | 处理邮件主题筛选时出错: 'int' object has no attribute 'decode'
2025-06-24 18:16:25 | ERROR    | app.utils.email_processor | 处理邮件主题筛选时出错: 'int' object has no attribute 'decode'
2025-06-24 18:16:25 | ERROR    | app.utils.email_processor | 处理邮件主题筛选时出错: 'int' object has no attribute 'decode'
2025-06-24 18:16:25 | ERROR    | app.utils.email_processor | 处理邮件主题筛选时出错: 'int' object has no attribute 'decode'
2025-06-24 18:16:25 | ERROR    | app.utils.email_processor | 处理邮件主题筛选时出错: 'int' object has no attribute 'decode'
2025-06-24 18:16:26 | ERROR    | app.utils.email_processor | 处理邮件主题筛选时出错: 'int' object has no attribute 'decode'
2025-06-24 18:16:26 | ERROR    | app.utils.email_processor | 处理邮件主题筛选时出错: 'int' object has no attribute 'decode'
2025-06-24 18:16:26 | ERROR    | app.utils.email_processor | 处理邮件主题筛选时出错: 'int' object has no attribute 'decode'
2025-06-24 18:16:26 | ERROR    | app.utils.email_processor | 处理邮件主题筛选时出错: 'int' object has no attribute 'decode'
2025-06-24 18:16:26 | ERROR    | app.utils.email_processor | 处理邮件主题筛选时出错: 'int' object has no attribute 'decode'
2025-06-24 18:16:28 | ERROR    | app.utils.email_processor | 处理邮件主题筛选时出错: 'int' object has no attribute 'decode'
2025-06-24 18:16:29 | ERROR    | app.utils.email_processor | 处理邮件主题筛选时出错: 'int' object has no attribute 'decode'
2025-06-24 18:16:29 | ERROR    | app.utils.email_processor | 处理邮件主题筛选时出错: 'int' object has no attribute 'decode'
2025-06-24 18:16:33 | ERROR    | app.utils.email_processor | 处理邮件主题筛选时出错: 'int' object has no attribute 'decode'
2025-06-24 18:16:34 | ERROR    | app.utils.email_processor | 处理邮件主题筛选时出错: 'int' object has no attribute 'decode'
2025-06-24 18:16:34 | ERROR    | app.utils.email_processor | 处理邮件主题筛选时出错: 'int' object has no attribute 'decode'
2025-06-24 18:16:35 | ERROR    | app.utils.email_processor | 处理邮件主题筛选时出错: 'int' object has no attribute 'decode'
2025-06-24 18:16:35 | ERROR    | app.utils.email_processor | 处理邮件主题筛选时出错: 'int' object has no attribute 'decode'
2025-06-24 18:16:36 | ERROR    | app.utils.email_processor | 处理邮件主题筛选时出错: 'int' object has no attribute 'decode'
2025-06-24 18:16:36 | ERROR    | app.utils.email_processor | 处理邮件主题筛选时出错: 'int' object has no attribute 'decode'
2025-06-24 18:16:36 | ERROR    | app.utils.email_processor | 处理邮件主题筛选时出错: 'int' object has no attribute 'decode'
2025-06-24 18:16:36 | ERROR    | app.utils.email_processor | 处理邮件主题筛选时出错: 'int' object has no attribute 'decode'
2025-06-24 18:16:36 | ERROR    | app.utils.email_processor | 处理邮件主题筛选时出错: 'int' object has no attribute 'decode'
2025-06-24 18:16:36 | ERROR    | app.utils.email_processor | 处理邮件主题筛选时出错: 'int' object has no attribute 'decode'
2025-06-24 18:16:36 | ERROR    | app.utils.email_processor | 处理邮件主题筛选时出错: 'int' object has no attribute 'decode'
2025-06-24 18:16:37 | ERROR    | app.utils.email_processor | 处理邮件主题筛选时出错: 'int' object has no attribute 'decode'
2025-06-24 18:16:37 | ERROR    | app.utils.email_processor | 处理邮件主题筛选时出错: 'int' object has no attribute 'decode'
2025-06-24 18:16:38 | ERROR    | app.utils.email_processor | 处理邮件主题筛选时出错: 'int' object has no attribute 'decode'
2025-06-24 18:16:38 | ERROR    | app.utils.email_processor | 处理邮件主题筛选时出错: 'int' object has no attribute 'decode'
2025-06-24 18:16:39 | ERROR    | app.utils.email_processor | 处理邮件主题筛选时出错: 'int' object has no attribute 'decode'
2025-06-24 18:16:40 | ERROR    | app.utils.email_processor | 处理邮件主题筛选时出错: 'int' object has no attribute 'decode'
2025-06-24 18:16:40 | ERROR    | app.utils.email_processor | 处理邮件主题筛选时出错: 'int' object has no attribute 'decode'
2025-06-24 18:16:40 | ERROR    | app.utils.email_processor | 处理邮件主题筛选时出错: 'int' object has no attribute 'decode'
2025-06-24 18:16:40 | ERROR    | app.utils.email_processor | 处理邮件主题筛选时出错: 'int' object has no attribute 'decode'
2025-06-24 18:16:41 | ERROR    | app.utils.email_processor | 处理邮件主题筛选时出错: 'int' object has no attribute 'decode'
2025-06-24 18:41:08 | ERROR    | app | 扫描邮箱附件失败: 'HighPerformanceEmailProcessor' object has no attribute 'preview_attachments'
2025-06-24 18:44:44 | ERROR    | app | 扫描邮箱附件失败: 'HighPerformanceEmailProcessor' object has no attribute 'preview_attachments'
2025-06-25 20:05:31 | ERROR    | app.services.data_source_manager | MySQL获取已排产批次数据失败: (1054, "Unknown column 'scheduled_time' in 'order clause'")
2025-06-25 20:38:50 | ERROR    | app.services.data_source_manager | MySQL获取已排产批次数据失败: (1054, "Unknown column 'scheduled_time' in 'order clause'")
2025-06-25 20:40:33 | ERROR    | app.services.data_source_manager | MySQL获取已排产批次数据失败: (1054, "Unknown column 'scheduled_time' in 'order clause'")
2025-06-25 20:48:39 | ERROR    | app.services.data_source_manager | MySQL获取已排产批次数据失败: (1054, "Unknown column 'scheduled_time' in 'order clause'")
2025-06-25 20:58:34 | ERROR    | app.services.data_source_manager | 更新eqp_status记录失败: (1054, "Unknown column 'EQP_ID' in 'field list'")
2025-06-25 20:58:45 | ERROR    | app.services.data_source_manager | 更新eqp_status记录失败: (1054, "Unknown column 'EQP_ID' in 'field list'")
2025-06-25 20:59:48 | ERROR    | app.services.data_source_manager | 更新eqp_status记录失败: (1054, "Unknown column 'EQP_ID' in 'field list'")
2025-06-25 21:03:21 | ERROR    | app.services.data_source_manager | MySQL获取已排产批次数据失败: (1054, "Unknown column 'scheduled_time' in 'order clause'")
2025-06-25 21:08:41 | ERROR    | app.services.data_source_manager | MySQL获取已排产批次数据失败: (1054, "Unknown column 'scheduled_time' in 'order clause'")
2025-06-25 21:14:50 | ERROR    | app.services.data_source_manager | MySQL获取已排产批次数据失败: (1054, "Unknown column 'scheduled_time' in 'order clause'")
2025-06-25 21:14:51 | ERROR    | app.services.data_source_manager | ❌ 所有数据源都不可用
2025-06-25 21:14:54 | ERROR    | app.services.data_source_manager | MySQL获取已排产批次数据失败: (1054, "Unknown column 'scheduled_time' in 'order clause'")
2025-06-25 21:15:02 | ERROR    | app.services.data_source_manager | ❌ 所有数据源都不可用
2025-06-25 21:15:09 | ERROR    | app.services.data_source_manager | ❌ 所有数据源都不可用
2025-06-25 21:27:07 | ERROR    | app.api.routes | 获取导入进度失败: Expecting value: line 1 column 1 (char 0)
2025-06-25 21:47:46 | ERROR    | app.services.enhanced_data_source_manager | ❌ 获取表数据失败 - eqp_status: too many values to unpack (expected 2)
2025-06-25 21:49:49 | ERROR    | app.services.enhanced_data_source_manager | ❌ 获取表数据失败 - eqp_status: too many values to unpack (expected 2)
2025-06-25 22:26:45 | ERROR    | app.services.data_source_manager | MySQL获取已排产批次数据失败: (1054, "Unknown column 'scheduled_time' in 'order clause'")
2025-06-25 22:31:38 | ERROR    | app.api.routes_v3 | ❌ API v3: 通用资源页面访问失败 - eqp_status: errors/404.html
2025-06-25 22:31:38 | ERROR    | app | Exception on /api/v3/page/eqp_status [GET]
Traceback (most recent call last):
  File "D:\Users\pc\Documents\HBuilderProjects\APS-2025.6.23.4\app\api\routes_v3.py", line 282, in universal_resource_page
    return render_template('errors/404.html',
          ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\Users\pc\AppData\Local\Programs\Python\Python311\Lib\site-packages\flask\templating.py", line 150, in render_template
    template = app.jinja_env.get_or_select_template(template_name_or_list)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\Users\pc\AppData\Local\Programs\Python\Python311\Lib\site-packages\jinja2\environment.py", line 1081, in get_or_select_template
    return self.get_template(template_name_or_list, parent, globals)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\Users\pc\AppData\Local\Programs\Python\Python311\Lib\site-packages\jinja2\environment.py", line 1010, in get_template
    return self._load_template(name, globals)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\Users\pc\AppData\Local\Programs\Python\Python311\Lib\site-packages\jinja2\environment.py", line 969, in _load_template
    template = self.loader.load(self, name, self.make_globals(globals))
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\Users\pc\AppData\Local\Programs\Python\Python311\Lib\site-packages\jinja2\loaders.py", line 126, in load
    source, filename, uptodate = self.get_source(environment, name)
                                 ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\Users\pc\AppData\Local\Programs\Python\Python311\Lib\site-packages\flask\templating.py", line 64, in get_source
    return self._get_source_fast(environment, template)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\Users\pc\AppData\Local\Programs\Python\Python311\Lib\site-packages\flask\templating.py", line 98, in _get_source_fast
    raise TemplateNotFound(template)
jinja2.exceptions.TemplateNotFound: errors/404.html

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "D:\Users\pc\AppData\Local\Programs\Python\Python311\Lib\site-packages\flask\app.py", line 2190, in wsgi_app
    response = self.full_dispatch_request()
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\Users\pc\AppData\Local\Programs\Python\Python311\Lib\site-packages\flask\app.py", line 1486, in full_dispatch_request
    rv = self.handle_user_exception(e)
         ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\Users\pc\AppData\Local\Programs\Python\Python311\Lib\site-packages\flask_cors\extension.py", line 176, in wrapped_function
    return cors_after_request(app.make_response(f(*args, **kwargs)))
                                                ^^^^^^^^^^^^^^^^^^
  File "D:\Users\pc\AppData\Local\Programs\Python\Python311\Lib\site-packages\flask\app.py", line 1484, in full_dispatch_request
    rv = self.dispatch_request()
         ^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\Users\pc\AppData\Local\Programs\Python\Python311\Lib\site-packages\flask\app.py", line 1469, in dispatch_request
    return self.ensure_sync(self.view_functions[rule.endpoint])(**view_args)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\Users\pc\Documents\HBuilderProjects\APS-2025.6.23.4\app\api\routes_v3.py", line 302, in universal_resource_page
    return render_template('errors/500.html',
          ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\Users\pc\AppData\Local\Programs\Python\Python311\Lib\site-packages\flask\templating.py", line 150, in render_template
    template = app.jinja_env.get_or_select_template(template_name_or_list)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\Users\pc\AppData\Local\Programs\Python\Python311\Lib\site-packages\jinja2\environment.py", line 1081, in get_or_select_template
    return self.get_template(template_name_or_list, parent, globals)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\Users\pc\AppData\Local\Programs\Python\Python311\Lib\site-packages\jinja2\environment.py", line 1010, in get_template
    return self._load_template(name, globals)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\Users\pc\AppData\Local\Programs\Python\Python311\Lib\site-packages\jinja2\environment.py", line 969, in _load_template
    template = self.loader.load(self, name, self.make_globals(globals))
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\Users\pc\AppData\Local\Programs\Python\Python311\Lib\site-packages\jinja2\loaders.py", line 126, in load
    source, filename, uptodate = self.get_source(environment, name)
                                 ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\Users\pc\AppData\Local\Programs\Python\Python311\Lib\site-packages\flask\templating.py", line 64, in get_source
    return self._get_source_fast(environment, template)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\Users\pc\AppData\Local\Programs\Python\Python311\Lib\site-packages\flask\templating.py", line 98, in _get_source_fast
    raise TemplateNotFound(template)
jinja2.exceptions.TemplateNotFound: errors/500.html
2025-06-25 22:33:46 | ERROR    | app.api.routes_v3 | ❌ API v3: 通用资源页面访问失败 - eqp_status: errors/404.html
2025-06-25 22:33:46 | ERROR    | app | Exception on /api/v3/page/eqp_status [GET]
Traceback (most recent call last):
  File "D:\Users\pc\Documents\HBuilderProjects\APS-2025.6.23.4\app\api\routes_v3.py", line 282, in universal_resource_page
    return render_template('errors/404.html',
          ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\Users\pc\AppData\Local\Programs\Python\Python311\Lib\site-packages\flask\templating.py", line 150, in render_template
    template = app.jinja_env.get_or_select_template(template_name_or_list)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\Users\pc\AppData\Local\Programs\Python\Python311\Lib\site-packages\jinja2\environment.py", line 1081, in get_or_select_template
    return self.get_template(template_name_or_list, parent, globals)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\Users\pc\AppData\Local\Programs\Python\Python311\Lib\site-packages\jinja2\environment.py", line 1010, in get_template
    return self._load_template(name, globals)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\Users\pc\AppData\Local\Programs\Python\Python311\Lib\site-packages\jinja2\environment.py", line 969, in _load_template
    template = self.loader.load(self, name, self.make_globals(globals))
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\Users\pc\AppData\Local\Programs\Python\Python311\Lib\site-packages\jinja2\loaders.py", line 126, in load
    source, filename, uptodate = self.get_source(environment, name)
                                 ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\Users\pc\AppData\Local\Programs\Python\Python311\Lib\site-packages\flask\templating.py", line 64, in get_source
    return self._get_source_fast(environment, template)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\Users\pc\AppData\Local\Programs\Python\Python311\Lib\site-packages\flask\templating.py", line 98, in _get_source_fast
    raise TemplateNotFound(template)
jinja2.exceptions.TemplateNotFound: errors/404.html

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "D:\Users\pc\AppData\Local\Programs\Python\Python311\Lib\site-packages\flask\app.py", line 2190, in wsgi_app
    response = self.full_dispatch_request()
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\Users\pc\AppData\Local\Programs\Python\Python311\Lib\site-packages\flask\app.py", line 1486, in full_dispatch_request
    rv = self.handle_user_exception(e)
         ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\Users\pc\AppData\Local\Programs\Python\Python311\Lib\site-packages\flask_cors\extension.py", line 176, in wrapped_function
    return cors_after_request(app.make_response(f(*args, **kwargs)))
                                                ^^^^^^^^^^^^^^^^^^
  File "D:\Users\pc\AppData\Local\Programs\Python\Python311\Lib\site-packages\flask\app.py", line 1484, in full_dispatch_request
    rv = self.dispatch_request()
         ^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\Users\pc\AppData\Local\Programs\Python\Python311\Lib\site-packages\flask\app.py", line 1469, in dispatch_request
    return self.ensure_sync(self.view_functions[rule.endpoint])(**view_args)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\Users\pc\Documents\HBuilderProjects\APS-2025.6.23.4\app\api\routes_v3.py", line 302, in universal_resource_page
    return render_template('errors/500.html',
          ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\Users\pc\AppData\Local\Programs\Python\Python311\Lib\site-packages\flask\templating.py", line 150, in render_template
    template = app.jinja_env.get_or_select_template(template_name_or_list)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\Users\pc\AppData\Local\Programs\Python\Python311\Lib\site-packages\jinja2\environment.py", line 1081, in get_or_select_template
    return self.get_template(template_name_or_list, parent, globals)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\Users\pc\AppData\Local\Programs\Python\Python311\Lib\site-packages\jinja2\environment.py", line 1010, in get_template
    return self._load_template(name, globals)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\Users\pc\AppData\Local\Programs\Python\Python311\Lib\site-packages\jinja2\environment.py", line 969, in _load_template
    template = self.loader.load(self, name, self.make_globals(globals))
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\Users\pc\AppData\Local\Programs\Python\Python311\Lib\site-packages\jinja2\loaders.py", line 126, in load
    source, filename, uptodate = self.get_source(environment, name)
                                 ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\Users\pc\AppData\Local\Programs\Python\Python311\Lib\site-packages\flask\templating.py", line 64, in get_source
    return self._get_source_fast(environment, template)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\Users\pc\AppData\Local\Programs\Python\Python311\Lib\site-packages\flask\templating.py", line 98, in _get_source_fast
    raise TemplateNotFound(template)
jinja2.exceptions.TemplateNotFound: errors/500.html
2025-06-25 22:35:12 | ERROR    | app.api.routes_v3 | ❌ API v3: 通用资源页面访问失败 - ct: errors/404.html
2025-06-25 22:35:12 | ERROR    | app | Exception on /api/v3/page/ct [GET]
Traceback (most recent call last):
  File "D:\Users\pc\Documents\HBuilderProjects\APS-2025.6.23.4\app\api\routes_v3.py", line 282, in universal_resource_page
    return render_template('errors/404.html',
          ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\Users\pc\AppData\Local\Programs\Python\Python311\Lib\site-packages\flask\templating.py", line 150, in render_template
    template = app.jinja_env.get_or_select_template(template_name_or_list)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\Users\pc\AppData\Local\Programs\Python\Python311\Lib\site-packages\jinja2\environment.py", line 1081, in get_or_select_template
    return self.get_template(template_name_or_list, parent, globals)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\Users\pc\AppData\Local\Programs\Python\Python311\Lib\site-packages\jinja2\environment.py", line 1010, in get_template
    return self._load_template(name, globals)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\Users\pc\AppData\Local\Programs\Python\Python311\Lib\site-packages\jinja2\environment.py", line 969, in _load_template
    template = self.loader.load(self, name, self.make_globals(globals))
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\Users\pc\AppData\Local\Programs\Python\Python311\Lib\site-packages\jinja2\loaders.py", line 126, in load
    source, filename, uptodate = self.get_source(environment, name)
                                 ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\Users\pc\AppData\Local\Programs\Python\Python311\Lib\site-packages\flask\templating.py", line 64, in get_source
    return self._get_source_fast(environment, template)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\Users\pc\AppData\Local\Programs\Python\Python311\Lib\site-packages\flask\templating.py", line 98, in _get_source_fast
    raise TemplateNotFound(template)
jinja2.exceptions.TemplateNotFound: errors/404.html

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "D:\Users\pc\AppData\Local\Programs\Python\Python311\Lib\site-packages\flask\app.py", line 2190, in wsgi_app
    response = self.full_dispatch_request()
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\Users\pc\AppData\Local\Programs\Python\Python311\Lib\site-packages\flask\app.py", line 1486, in full_dispatch_request
    rv = self.handle_user_exception(e)
         ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\Users\pc\AppData\Local\Programs\Python\Python311\Lib\site-packages\flask_cors\extension.py", line 176, in wrapped_function
    return cors_after_request(app.make_response(f(*args, **kwargs)))
                                                ^^^^^^^^^^^^^^^^^^
  File "D:\Users\pc\AppData\Local\Programs\Python\Python311\Lib\site-packages\flask\app.py", line 1484, in full_dispatch_request
    rv = self.dispatch_request()
         ^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\Users\pc\AppData\Local\Programs\Python\Python311\Lib\site-packages\flask\app.py", line 1469, in dispatch_request
    return self.ensure_sync(self.view_functions[rule.endpoint])(**view_args)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\Users\pc\Documents\HBuilderProjects\APS-2025.6.23.4\app\api\routes_v3.py", line 302, in universal_resource_page
    return render_template('errors/500.html',
          ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\Users\pc\AppData\Local\Programs\Python\Python311\Lib\site-packages\flask\templating.py", line 150, in render_template
    template = app.jinja_env.get_or_select_template(template_name_or_list)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\Users\pc\AppData\Local\Programs\Python\Python311\Lib\site-packages\jinja2\environment.py", line 1081, in get_or_select_template
    return self.get_template(template_name_or_list, parent, globals)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\Users\pc\AppData\Local\Programs\Python\Python311\Lib\site-packages\jinja2\environment.py", line 1010, in get_template
    return self._load_template(name, globals)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\Users\pc\AppData\Local\Programs\Python\Python311\Lib\site-packages\jinja2\environment.py", line 969, in _load_template
    template = self.loader.load(self, name, self.make_globals(globals))
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\Users\pc\AppData\Local\Programs\Python\Python311\Lib\site-packages\jinja2\loaders.py", line 126, in load
    source, filename, uptodate = self.get_source(environment, name)
                                 ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\Users\pc\AppData\Local\Programs\Python\Python311\Lib\site-packages\flask\templating.py", line 64, in get_source
    return self._get_source_fast(environment, template)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\Users\pc\AppData\Local\Programs\Python\Python311\Lib\site-packages\flask\templating.py", line 98, in _get_source_fast
    raise TemplateNotFound(template)
jinja2.exceptions.TemplateNotFound: errors/500.html
2025-06-25 22:54:05 | ERROR    | app.api.routes_v3 | ❌ API v3: 通用资源页面访问失败 - eqp_status: 'flask_login.mixins.AnonymousUserMixin object' has no attribute 'has_permission'
2025-06-25 22:55:11 | ERROR    | app.api.routes_v3 | ❌ API v3: 通用资源页面访问失败 - eqp_status: 'flask_login.mixins.AnonymousUserMixin object' has no attribute 'has_permission'
2025-06-25 22:55:13 | ERROR    | app.api.routes_v3 | ❌ API v3: 通用资源页面访问失败 - et_uph_eqp: 'flask_login.mixins.AnonymousUserMixin object' has no attribute 'has_permission'
2025-06-25 22:55:15 | ERROR    | app.api.routes_v3 | ❌ API v3: 通用资源页面访问失败 - et_ft_test_spec: 'flask_login.mixins.AnonymousUserMixin object' has no attribute 'has_permission'
2025-06-25 22:55:17 | ERROR    | app.api.routes_v3 | ❌ API v3: 通用资源页面访问失败 - ct: 'flask_login.mixins.AnonymousUserMixin object' has no attribute 'has_permission'
2025-06-25 22:55:57 | ERROR    | app.api.routes_v3 | ❌ API v3: 通用资源页面访问失败 - eqp_status: 'flask_login.mixins.AnonymousUserMixin object' has no attribute 'has_permission'
2025-06-25 23:00:00 | ERROR    | app.api.routes_v3 | ❌ API v3: 通用资源页面访问失败 - eqp_status: 'flask_login.mixins.AnonymousUserMixin object' has no attribute 'has_permission'
2025-06-25 23:00:02 | ERROR    | app.api.routes_v3 | ❌ API v3: 通用资源页面访问失败 - et_uph_eqp: 'flask_login.mixins.AnonymousUserMixin object' has no attribute 'has_permission'
2025-06-25 23:00:04 | ERROR    | app.api.routes_v3 | ❌ API v3: 通用资源页面访问失败 - et_ft_test_spec: 'flask_login.mixins.AnonymousUserMixin object' has no attribute 'has_permission'
2025-06-25 23:00:07 | ERROR    | app.api.routes_v3 | ❌ API v3: 通用资源页面访问失败 - ct: 'flask_login.mixins.AnonymousUserMixin object' has no attribute 'has_permission'
2025-06-25 23:00:35 | ERROR    | app.api.routes_v3 | ❌ API v3: 通用资源页面访问失败 - eqp_status: 'flask_login.mixins.AnonymousUserMixin object' has no attribute 'has_permission'
2025-06-25 23:02:46 | ERROR    | app.api.routes_v3 | ❌ API v3: 通用资源页面访问失败 - eqp_status: 'flask_login.mixins.AnonymousUserMixin object' has no attribute 'has_permission'
2025-06-25 23:07:40 | ERROR    | app.api.routes_v3 | ❌ API v3: 通用资源页面访问失败 - eqp_status: 'flask_login.mixins.AnonymousUserMixin object' has no attribute 'has_permission'
2025-06-25 23:11:31 | ERROR    | app.api.routes_v3 | ❌ API v3: 通用资源页面访问失败 - eqp_status: 'flask_login.mixins.AnonymousUserMixin object' has no attribute 'has_permission'
2025-06-25 23:11:52 | ERROR    | app.api.routes_v3 | ❌ API v3: 通用资源页面访问失败 - eqp_status: 'flask_login.mixins.AnonymousUserMixin object' has no attribute 'has_permission'
2025-06-25 23:12:13 | ERROR    | app.api.routes_v3 | ❌ API v3: 通用资源页面访问失败 - eqp_status: 'flask_login.mixins.AnonymousUserMixin object' has no attribute 'has_permission'
2025-06-26 01:51:35 | ERROR    | app.api.routes_v3 | ❌ API v3: 获取表结构失败 - eqp_status: 'list' object has no attribute 'items'
2025-06-26 01:53:12 | ERROR    | app.api.routes_v3 | ❌ API v3: 获取表结构失败 - eqp_status: 'list' object has no attribute 'items'
