<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>测试规格管理 v3 - APS平台</title>
    <link href="{{ url_for('static', filename='vendor/bootstrap/bootstrap.min.css') }}" rel="stylesheet">
    <link href="{{ url_for('static', filename='vendor/fontawesome/css/all.min.css') }}" rel="stylesheet">
    <style>
        .page-header {
            background: linear-gradient(135deg, #6f42c1 0%, #e83e8c 100%);
            color: white;
            padding: 2rem 0;
            margin-bottom: 2rem;
        }
        .feature-card {
            border: 1px solid #ddd;
            border-radius: 8px;
            padding: 20px;
            margin-bottom: 20px;
            background: white;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        .search-toolbar {
            background: #f8f9fa;
            border-radius: 8px;
            padding: 15px;
            margin-bottom: 20px;
        }
        .table-container {
            background: white;
            border-radius: 8px;
            overflow: hidden;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        .data-table {
            margin: 0;
        }
        .approval-badge {
            padding: 4px 8px;
            border-radius: 4px;
            font-size: 12px;
            font-weight: bold;
        }
        .approval-pending { background-color: #ffc107; color: #000; }
        .approval-approved { background-color: #28a745; color: #fff; }
        .approval-rejected { background-color: #dc3545; color: #fff; }
        .active-badge {
            padding: 4px 8px;
            border-radius: 4px;
            font-size: 12px;
            font-weight: bold;
        }
        .active-yes { background-color: #28a745; color: #fff; }
        .active-no { background-color: #6c757d; color: #fff; }
        .pagination-container {
            background: white;
            padding: 15px;
            border-top: 1px solid #dee2e6;
        }
        .stats-cards {
            margin-bottom: 20px;
        }
        .stats-card {
            background: white;
            border-radius: 8px;
            padding: 20px;
            text-align: center;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        .stats-number {
            font-size: 2rem;
            font-weight: bold;
            margin-bottom: 5px;
        }
        .stats-label {
            color: #6c757d;
            font-size: 14px;
        }
        .loading-overlay {
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: rgba(255,255,255,0.8);
            display: flex;
            align-items: center;
            justify-content: center;
            z-index: 1000;
        }
        .sort-header {
            cursor: pointer;
            user-select: none;
        }
        .sort-header:hover {
            background-color: #f8f9fa;
        }
        .sort-indicator {
            margin-left: 5px;
            opacity: 0.5;
        }
        .sort-active {
            opacity: 1;
        }
        .spec-id-cell {
            font-family: 'Monaco', 'Consolas', monospace;
            font-weight: bold;
            color: #6f42c1;
        }
        .version-cell {
            font-weight: bold;
            color: #e83e8c;
        }
        .temperature-cell {
            text-align: right;
            font-weight: bold;
        }
    </style>
</head>
<body>
    <!-- 页面头部 -->
    <div class="page-header">
        <div class="container-fluid">
            <div class="row align-items-center">
                <div class="col-md-8">
                    <h1 class="mb-1">
                        <i class="fas fa-cogs me-2"></i>测试规格管理 v3
                    </h1>
                    <p class="mb-0 opacity-75">基于API v3的现代化测试规格管理系统</p>
                </div>
                <div class="col-md-4 text-end">
                    <span class="badge bg-success fs-6">API v3</span>
                    <span class="badge bg-info fs-6 ms-2">实时数据</span>
                </div>
            </div>
        </div>
    </div>

    <div class="container-fluid">
        <!-- 统计卡片 -->
        <div class="row stats-cards">
            <div class="col-md-3">
                <div class="stats-card">
                    <div class="stats-number text-success" id="totalSpecs">0</div>
                    <div class="stats-label">规格总数</div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="stats-card">
                    <div class="stats-number text-primary" id="approvedSpecs">0</div>
                    <div class="stats-label">已批准规格</div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="stats-card">
                    <div class="stats-number text-warning" id="activeSpecs">0</div>
                    <div class="stats-label">激活规格</div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="stats-card">
                    <div class="stats-number text-info" id="totalDevices">0</div>
                    <div class="stats-label">产品数量</div>
                </div>
            </div>
        </div>

        <!-- 操作工具栏 -->
        <div class="search-toolbar">
            <div class="row align-items-center">
                <div class="col-md-4">
                    <div class="input-group">
                        <span class="input-group-text">
                            <i class="fas fa-search"></i>
                        </span>
                        <input type="text" class="form-control" id="globalSearch" 
                               placeholder="搜索规格ID、产品名称、工序...">
                        <button class="btn btn-primary" onclick="performSearch()">搜索</button>
                    </div>
                </div>
                <div class="col-md-2">
                    <select class="form-select" id="approvalFilter">
                        <option value="">所有状态</option>
                        <option value="PENDING">待审批</option>
                        <option value="APPROVED">已批准</option>
                        <option value="REJECTED">已驳回</option>
                    </select>
                </div>
                <div class="col-md-2">
                    <select class="form-select" id="activeFilter">
                        <option value="">所有激活状态</option>
                        <option value="Y">已激活</option>
                        <option value="N">未激活</option>
                    </select>
                </div>
                <div class="col-md-1">
                    <select class="form-select" id="perPage">
                        <option value="25">25条/页</option>
                        <option value="50" selected>50条/页</option>
                        <option value="100">100条/页</option>
                    </select>
                </div>
                <div class="col-md-3">
                    <div class="btn-group" role="group">
                        <button class="btn btn-success" onclick="refreshData()">
                            <i class="fas fa-sync me-1"></i>刷新
                        </button>
                        <button class="btn btn-info" onclick="exportData()">
                            <i class="fas fa-download me-1"></i>导出
                        </button>
                        <button class="btn btn-warning" onclick="addNewRecord()">
                            <i class="fas fa-plus me-1"></i>新增
                        </button>
                        <button class="btn btn-primary" onclick="showSpecAnalysis()">
                            <i class="fas fa-chart-line me-1"></i>规格分析
                        </button>
                    </div>
                </div>
            </div>
        </div>

        <!-- 数据表格 -->
        <div class="table-container position-relative">
            <div id="loadingOverlay" class="loading-overlay" style="display: none;">
                <div class="text-center">
                    <div class="spinner-border text-primary" role="status">
                        <span class="visually-hidden">加载中...</span>
                    </div>
                    <div class="mt-2">正在加载数据...</div>
                </div>
            </div>
            
            <div class="table-responsive">
                <table class="table table-hover data-table" id="dataTable">
                    <thead class="table-light">
                        <tr>
                            <th class="sort-header" onclick="sortBy('id')">
                                ID <i class="fas fa-sort sort-indicator"></i>
                            </th>
                            <th class="sort-header" onclick="sortBy('TEST_SPEC_ID')">
                                规格ID <i class="fas fa-sort sort-indicator"></i>
                            </th>
                            <th class="sort-header" onclick="sortBy('TEST_SPEC_NAME')">
                                规格名称 <i class="fas fa-sort sort-indicator"></i>
                            </th>
                            <th class="sort-header" onclick="sortBy('TEST_SPEC_VER')">
                                版本 <i class="fas fa-sort sort-indicator"></i>
                            </th>
                            <th class="sort-header" onclick="sortBy('DEVICE')">
                                产品名称 <i class="fas fa-sort sort-indicator"></i>
                            </th>
                            <th class="sort-header" onclick="sortBy('STAGE')">
                                工序 <i class="fas fa-sort sort-indicator"></i>
                            </th>
                            <th class="sort-header" onclick="sortBy('APPROVAL_STATE')">
                                审批状态 <i class="fas fa-sort sort-indicator"></i>
                            </th>
                            <th class="sort-header" onclick="sortBy('ACTV_YN')">
                                激活状态 <i class="fas fa-sort sort-indicator"></i>
                            </th>
                            <th class="sort-header" onclick="sortBy('TEMPERATURE')">
                                温度 <i class="fas fa-sort sort-indicator"></i>
                            </th>
                            <th class="sort-header" onclick="sortBy('updated_at')">
                                更新时间 <i class="fas fa-sort sort-indicator"></i>
                            </th>
                            <th>操作</th>
                        </tr>
                    </thead>
                    <tbody id="tableBody">
                        <tr>
                            <td colspan="11" class="text-center py-4">
                                <i class="fas fa-spinner fa-spin me-2"></i>正在加载数据...
                            </td>
                        </tr>
                    </tbody>
                </table>
            </div>
            
            <!-- 分页导航 -->
            <div class="pagination-container">
                <div class="row align-items-center">
                    <div class="col-md-6">
                        <div id="dataInfo" class="text-muted">
                            显示第 1-50 条，共 0 条记录
                        </div>
                    </div>
                    <div class="col-md-6">
                        <nav>
                            <ul class="pagination justify-content-end mb-0" id="pagination">
                                <!-- 分页按钮将通过JavaScript生成 -->
                            </ul>
                        </nav>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- 编辑模态框 -->
    <div class="modal fade" id="editModal" tabindex="-1">
        <div class="modal-dialog modal-lg">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">编辑测试规格</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body">
                    <form id="editForm">
                        <input type="hidden" id="editId">
                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label class="form-label">规格ID</label>
                                    <input type="text" class="form-control" id="editSpecId" required>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label class="form-label">规格名称</label>
                                    <input type="text" class="form-control" id="editSpecName" required>
                                </div>
                            </div>
                        </div>
                        <div class="row">
                            <div class="col-md-4">
                                <div class="mb-3">
                                    <label class="form-label">版本</label>
                                    <input type="text" class="form-control" id="editSpecVer" placeholder="1.0.0">
                                </div>
                            </div>
                            <div class="col-md-4">
                                <div class="mb-3">
                                    <label class="form-label">工序</label>
                                    <select class="form-select" id="editStage" required>
                                        <option value="">请选择工序</option>
                                        <option value="FT">FT</option>
                                        <option value="CP">CP</option>
                                        <option value="SORT">SORT</option>
                                    </select>
                                </div>
                            </div>
                            <div class="col-md-4">
                                <div class="mb-3">
                                    <label class="form-label">测试机</label>
                                    <input type="text" class="form-control" id="editTester">
                                </div>
                            </div>
                        </div>
                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label class="form-label">产品名称</label>
                                    <input type="text" class="form-control" id="editDevice" required>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label class="form-label">封装型号</label>
                                    <input type="text" class="form-control" id="editPkgPn">
                                </div>
                            </div>
                        </div>
                        <div class="row">
                            <div class="col-md-4">
                                <div class="mb-3">
                                    <label class="form-label">审批状态</label>
                                    <select class="form-select" id="editApprovalState">
                                        <option value="PENDING">待审批</option>
                                        <option value="APPROVED">已批准</option>
                                        <option value="REJECTED">已驳回</option>
                                    </select>
                                </div>
                            </div>
                            <div class="col-md-4">
                                <div class="mb-3">
                                    <label class="form-label">激活状态</label>
                                    <select class="form-select" id="editActvYn">
                                        <option value="Y">激活</option>
                                        <option value="N">未激活</option>
                                    </select>
                                </div>
                            </div>
                            <div class="col-md-4">
                                <div class="mb-3">
                                    <label class="form-label">温度 (°C)</label>
                                    <input type="number" class="form-control" id="editTemperature" step="0.1">
                                </div>
                            </div>
                        </div>
                    </form>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
                    <button type="button" class="btn btn-primary" onclick="saveEdit()">保存</button>
                </div>
            </div>
        </div>
    </div>

    <!-- 规格分析模态框 -->
    <div class="modal fade" id="specAnalysisModal" tabindex="-1">
        <div class="modal-dialog modal-xl">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">测试规格分析报告</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body">
                    <div id="specAnalysisContent">
                        <div class="text-center">
                            <div class="spinner-border text-primary" role="status">
                                <span class="visually-hidden">分析中...</span>
                            </div>
                            <div class="mt-2">正在分析测试规格数据...</div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="{{ url_for('static', filename='vendor/bootstrap/bootstrap.bundle.min.js') }}"></script>
    <script>
        // 全局变量
        const API_BASE = '/api/v3';
        const TABLE_NAME = 'et_ft_test_spec';
        let currentData = [];
        let currentPage = 1;
        let totalPages = 1;
        let currentSort = { field: '', order: 'asc' };
        let currentFilters = [];

        // 页面加载时初始化
        document.addEventListener('DOMContentLoaded', function() {
            console.log('🚀 测试规格管理v3页面加载完成');
            loadData();

            // 绑定事件
            document.getElementById('approvalFilter').addEventListener('change', applyFilters);
            document.getElementById('activeFilter').addEventListener('change', applyFilters);
            document.getElementById('perPage').addEventListener('change', function() {
                currentPage = 1;
                loadData();
            });

            // 搜索框回车事件
            document.getElementById('globalSearch').addEventListener('keypress', function(e) {
                if (e.key === 'Enter') {
                    performSearch();
                }
            });
        });

        // 加载数据
        async function loadData() {
            showLoading(true);

            try {
                const params = new URLSearchParams({
                    page: currentPage,
                    per_page: document.getElementById('perPage').value
                });

                // 添加搜索条件
                const searchTerm = document.getElementById('globalSearch').value;
                if (searchTerm) {
                    params.append('search', searchTerm);
                }

                // 添加排序条件
                if (currentSort.field) {
                    params.append('sort_by', currentSort.field);
                    params.append('sort_order', currentSort.order);
                }

                // 添加筛选条件
                if (currentFilters.length > 0) {
                    params.append('filters', JSON.stringify(currentFilters));
                }

                const response = await fetch(`${API_BASE}/tables/${TABLE_NAME}/data?${params}`);
                const result = await response.json();

                if (result.success) {
                    currentData = result.data;
                    totalPages = result.pages;
                    renderTable(result.data);
                    renderPagination(result);
                    updateStats(result.data);
                    updateDataInfo(result);
                } else {
                    showError('数据加载失败: ' + result.error);
                }

            } catch (error) {
                showError('请求失败: ' + error.message);
            } finally {
                showLoading(false);
            }
        }

        // 渲染表格
        function renderTable(data) {
            const tbody = document.getElementById('tableBody');

            if (!data || data.length === 0) {
                tbody.innerHTML = `
                    <tr>
                        <td colspan="11" class="text-center py-4">
                            <i class="fas fa-inbox fa-2x text-muted mb-2"></i>
                            <div>暂无数据</div>
                        </td>
                    </tr>
                `;
                return;
            }

            tbody.innerHTML = data.map(row => `
                <tr>
                    <td>${row.id || ''}</td>
                    <td class="spec-id-cell">${row.TEST_SPEC_ID || ''}</td>
                    <td>${row.TEST_SPEC_NAME || ''}</td>
                    <td class="version-cell">${row.TEST_SPEC_VER || ''}</td>
                    <td><strong>${row.DEVICE || ''}</strong></td>
                    <td>
                        <span class="badge bg-info">${row.STAGE || ''}</span>
                    </td>
                    <td>
                        <span class="approval-badge approval-${(row.APPROVAL_STATE || 'pending').toLowerCase()}">
                            ${getApprovalText(row.APPROVAL_STATE)}
                        </span>
                    </td>
                    <td>
                        <span class="active-badge active-${row.ACTV_YN === 'Y' ? 'yes' : 'no'}">
                            ${row.ACTV_YN === 'Y' ? '激活' : '未激活'}
                        </span>
                    </td>
                    <td class="temperature-cell">
                        ${row.TEMPERATURE ? row.TEMPERATURE + '°C' : ''}
                    </td>
                    <td>${formatDateTime(row.updated_at)}</td>
                    <td>
                        <div class="btn-group btn-group-sm">
                            <button class="btn btn-outline-primary" onclick="editRecord(${row.id})" title="编辑">
                                <i class="fas fa-edit"></i>
                            </button>
                            <button class="btn btn-outline-info" onclick="viewDetails(${row.id})" title="详情">
                                <i class="fas fa-eye"></i>
                            </button>
                            <button class="btn btn-outline-danger" onclick="deleteRecord(${row.id})" title="删除">
                                <i class="fas fa-trash"></i>
                            </button>
                        </div>
                    </td>
                </tr>
            `).join('');
        }

        // 渲染分页
        function renderPagination(result) {
            const pagination = document.getElementById('pagination');
            const totalPages = result.pages;
            const currentPageNum = currentPage;

            let html = '';

            // 上一页
            html += `
                <li class="page-item ${currentPageNum <= 1 ? 'disabled' : ''}">
                    <a class="page-link" href="#" onclick="goToPage(${currentPageNum - 1})">
                        <i class="fas fa-chevron-left"></i>
                    </a>
                </li>
            `;

            // 页码
            const startPage = Math.max(1, currentPageNum - 2);
            const endPage = Math.min(totalPages, currentPageNum + 2);

            if (startPage > 1) {
                html += `<li class="page-item"><a class="page-link" href="#" onclick="goToPage(1)">1</a></li>`;
                if (startPage > 2) {
                    html += `<li class="page-item disabled"><span class="page-link">...</span></li>`;
                }
            }

            for (let i = startPage; i <= endPage; i++) {
                html += `
                    <li class="page-item ${i === currentPageNum ? 'active' : ''}">
                        <a class="page-link" href="#" onclick="goToPage(${i})">${i}</a>
                    </li>
                `;
            }

            if (endPage < totalPages) {
                if (endPage < totalPages - 1) {
                    html += `<li class="page-item disabled"><span class="page-link">...</span></li>`;
                }
                html += `<li class="page-item"><a class="page-link" href="#" onclick="goToPage(${totalPages})">${totalPages}</a></li>`;
            }

            // 下一页
            html += `
                <li class="page-item ${currentPageNum >= totalPages ? 'disabled' : ''}">
                    <a class="page-link" href="#" onclick="goToPage(${currentPageNum + 1})">
                        <i class="fas fa-chevron-right"></i>
                    </a>
                </li>
            `;

            pagination.innerHTML = html;
        }

        // 更新统计信息
        function updateStats(data) {
            const totalSpecs = data.length;
            const approvedSpecs = data.filter(row => row.APPROVAL_STATE === 'APPROVED').length;
            const activeSpecs = data.filter(row => row.ACTV_YN === 'Y').length;
            const totalDevices = new Set(data.map(row => row.DEVICE)).size;

            document.getElementById('totalSpecs').textContent = totalSpecs;
            document.getElementById('approvedSpecs').textContent = approvedSpecs;
            document.getElementById('activeSpecs').textContent = activeSpecs;
            document.getElementById('totalDevices').textContent = totalDevices;
        }

        // 更新数据信息
        function updateDataInfo(result) {
            const start = (currentPage - 1) * parseInt(document.getElementById('perPage').value) + 1;
            const end = Math.min(start + result.data.length - 1, result.total);

            document.getElementById('dataInfo').textContent =
                `显示第 ${start}-${end} 条，共 ${result.total} 条记录`;
        }

        // 执行搜索
        function performSearch() {
            currentPage = 1;
            loadData();
        }

        // 应用筛选
        function applyFilters() {
            const approvalFilter = document.getElementById('approvalFilter').value;
            const activeFilter = document.getElementById('activeFilter').value;

            currentFilters = [];

            if (approvalFilter) {
                currentFilters.push({
                    field: 'APPROVAL_STATE',
                    operator: 'equals',
                    value: approvalFilter
                });
            }

            if (activeFilter) {
                currentFilters.push({
                    field: 'ACTV_YN',
                    operator: 'equals',
                    value: activeFilter
                });
            }

            currentPage = 1;
            loadData();
        }

        // 排序
        function sortBy(field) {
            if (currentSort.field === field) {
                currentSort.order = currentSort.order === 'asc' ? 'desc' : 'asc';
            } else {
                currentSort.field = field;
                currentSort.order = 'asc';
            }

            // 更新排序指示器
            updateSortIndicators();

            currentPage = 1;
            loadData();
        }

        // 更新排序指示器
        function updateSortIndicators() {
            document.querySelectorAll('.sort-indicator').forEach(indicator => {
                indicator.className = 'fas fa-sort sort-indicator';
            });

            if (currentSort.field) {
                const header = document.querySelector(`th[onclick="sortBy('${currentSort.field}')"] .sort-indicator`);
                if (header) {
                    header.className = `fas fa-sort-${currentSort.order === 'asc' ? 'up' : 'down'} sort-indicator sort-active`;
                }
            }
        }

        // 跳转页面
        function goToPage(page) {
            if (page >= 1 && page <= totalPages && page !== currentPage) {
                currentPage = page;
                loadData();
            }
        }

        // 刷新数据
        function refreshData() {
            loadData();
        }

        // 导出数据
        function exportData() {
            const params = new URLSearchParams({
                format: 'excel'
            });

            const searchTerm = document.getElementById('globalSearch').value;
            if (searchTerm) {
                params.append('search', searchTerm);
            }

            if (currentSort.field) {
                params.append('sort_by', currentSort.field);
                params.append('sort_order', currentSort.order);
            }

            if (currentFilters.length > 0) {
                params.append('filters', JSON.stringify(currentFilters));
            }

            const exportUrl = `${API_BASE}/tables/${TABLE_NAME}/export?${params}`;

            const link = document.createElement('a');
            link.href = exportUrl;
            link.style.display = 'none';
            document.body.appendChild(link);
            link.click();
            document.body.removeChild(link);
        }

        // 新增记录
        function addNewRecord() {
            // 清空表单
            document.getElementById('editForm').reset();
            document.getElementById('editId').value = '';
            document.querySelector('#editModal .modal-title').textContent = '新增测试规格';

            // 显示模态框
            new bootstrap.Modal(document.getElementById('editModal')).show();
        }

        // 编辑记录
        function editRecord(id) {
            const record = currentData.find(item => item.id == id);
            if (!record) return;

            // 填充表单
            document.getElementById('editId').value = record.id;
            document.getElementById('editSpecId').value = record.TEST_SPEC_ID || '';
            document.getElementById('editSpecName').value = record.TEST_SPEC_NAME || '';
            document.getElementById('editSpecVer').value = record.TEST_SPEC_VER || '';
            document.getElementById('editStage').value = record.STAGE || '';
            document.getElementById('editTester').value = record.TESTER || '';
            document.getElementById('editDevice').value = record.DEVICE || '';
            document.getElementById('editPkgPn').value = record.PKG_PN || '';
            document.getElementById('editApprovalState').value = record.APPROVAL_STATE || 'PENDING';
            document.getElementById('editActvYn').value = record.ACTV_YN || 'Y';
            document.getElementById('editTemperature').value = record.TEMPERATURE || '';

            document.querySelector('#editModal .modal-title').textContent = '编辑测试规格';

            // 显示模态框
            new bootstrap.Modal(document.getElementById('editModal')).show();
        }

        // 查看详情
        function viewDetails(id) {
            const record = currentData.find(item => item.id == id);
            if (!record) return;

            // 创建详情显示内容
            const detailsHtml = `
                <div class="row">
                    <div class="col-md-6">
                        <h6>基本信息</h6>
                        <table class="table table-sm">
                            <tr><td>规格ID:</td><td>${record.TEST_SPEC_ID || ''}</td></tr>
                            <tr><td>规格名称:</td><td>${record.TEST_SPEC_NAME || ''}</td></tr>
                            <tr><td>版本:</td><td>${record.TEST_SPEC_VER || ''}</td></tr>
                            <tr><td>工序:</td><td>${record.STAGE || ''}</td></tr>
                            <tr><td>测试机:</td><td>${record.TESTER || ''}</td></tr>
                        </table>
                    </div>
                    <div class="col-md-6">
                        <h6>产品信息</h6>
                        <table class="table table-sm">
                            <tr><td>产品名称:</td><td>${record.DEVICE || ''}</td></tr>
                            <tr><td>封装型号:</td><td>${record.PKG_PN || ''}</td></tr>
                            <tr><td>芯片ID:</td><td>${record.CHIP_ID || ''}</td></tr>
                            <tr><td>分选机:</td><td>${record.HANDLER || ''}</td></tr>
                            <tr><td>温度:</td><td>${record.TEMPERATURE ? record.TEMPERATURE + '°C' : ''}</td></tr>
                        </table>
                    </div>
                </div>
                <div class="row">
                    <div class="col-12">
                        <h6>状态信息</h6>
                        <table class="table table-sm">
                            <tr><td>审批状态:</td><td><span class="approval-badge approval-${(record.APPROVAL_STATE || 'pending').toLowerCase()}">${getApprovalText(record.APPROVAL_STATE)}</span></td></tr>
                            <tr><td>激活状态:</td><td><span class="active-badge active-${record.ACTV_YN === 'Y' ? 'yes' : 'no'}">${record.ACTV_YN === 'Y' ? '激活' : '未激活'}</span></td></tr>
                            <tr><td>创建时间:</td><td>${formatDateTime(record.created_at)}</td></tr>
                            <tr><td>更新时间:</td><td>${formatDateTime(record.updated_at)}</td></tr>
                        </table>
                    </div>
                </div>
            `;

            // 显示详情模态框
            const modal = document.createElement('div');
            modal.className = 'modal fade';
            modal.innerHTML = `
                <div class="modal-dialog modal-lg">
                    <div class="modal-content">
                        <div class="modal-header">
                            <h5 class="modal-title">测试规格详情</h5>
                            <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                        </div>
                        <div class="modal-body">
                            ${detailsHtml}
                        </div>
                        <div class="modal-footer">
                            <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">关闭</button>
                        </div>
                    </div>
                </div>
            `;

            document.body.appendChild(modal);
            const bsModal = new bootstrap.Modal(modal);
            bsModal.show();

            // 模态框关闭后移除DOM元素
            modal.addEventListener('hidden.bs.modal', function() {
                document.body.removeChild(modal);
            });
        }

        // 保存编辑
        async function saveEdit() {
            const id = document.getElementById('editId').value;
            const data = {
                TEST_SPEC_ID: document.getElementById('editSpecId').value,
                TEST_SPEC_NAME: document.getElementById('editSpecName').value,
                TEST_SPEC_VER: document.getElementById('editSpecVer').value,
                STAGE: document.getElementById('editStage').value,
                TESTER: document.getElementById('editTester').value,
                DEVICE: document.getElementById('editDevice').value,
                PKG_PN: document.getElementById('editPkgPn').value,
                APPROVAL_STATE: document.getElementById('editApprovalState').value,
                ACTV_YN: document.getElementById('editActvYn').value,
                TEMPERATURE: document.getElementById('editTemperature').value
            };

            try {
                let response;
                if (id) {
                    // 更新
                    response = await fetch(`${API_BASE}/tables/${TABLE_NAME}/data/${id}`, {
                        method: 'PUT',
                        headers: { 'Content-Type': 'application/json' },
                        body: JSON.stringify(data)
                    });
                } else {
                    // 新增
                    response = await fetch(`${API_BASE}/tables/${TABLE_NAME}/data`, {
                        method: 'POST',
                        headers: { 'Content-Type': 'application/json' },
                        body: JSON.stringify(data)
                    });
                }

                const result = await response.json();

                if (result.success) {
                    bootstrap.Modal.getInstance(document.getElementById('editModal')).hide();
                    showSuccess(id ? '更新成功' : '新增成功');
                    loadData();
                } else {
                    showError('保存失败: ' + result.error);
                }

            } catch (error) {
                showError('请求失败: ' + error.message);
            }
        }

        // 删除记录
        async function deleteRecord(id) {
            if (!confirm('确定要删除这条记录吗？')) return;

            try {
                const response = await fetch(`${API_BASE}/tables/${TABLE_NAME}/data/${id}`, {
                    method: 'DELETE'
                });

                const result = await response.json();

                if (result.success) {
                    showSuccess('删除成功');
                    loadData();
                } else {
                    showError('删除失败: ' + result.error);
                }

            } catch (error) {
                showError('请求失败: ' + error.message);
            }
        }

        // 显示规格分析
        function showSpecAnalysis() {
            const modal = new bootstrap.Modal(document.getElementById('specAnalysisModal'));
            modal.show();

            // 生成分析报告
            generateSpecAnalysis();
        }

        // 生成规格分析报告
        function generateSpecAnalysis() {
            const content = document.getElementById('specAnalysisContent');

            setTimeout(() => {
                const stages = [...new Set(currentData.map(row => row.STAGE))];
                const devices = [...new Set(currentData.map(row => row.DEVICE))];
                const approvalStats = {
                    PENDING: currentData.filter(row => row.APPROVAL_STATE === 'PENDING').length,
                    APPROVED: currentData.filter(row => row.APPROVAL_STATE === 'APPROVED').length,
                    REJECTED: currentData.filter(row => row.APPROVAL_STATE === 'REJECTED').length
                };

                const stageStats = stages.map(stage => {
                    const stageData = currentData.filter(row => row.STAGE === stage);
                    const approved = stageData.filter(row => row.APPROVAL_STATE === 'APPROVED').length;
                    const active = stageData.filter(row => row.ACTV_YN === 'Y').length;
                    return { stage, total: stageData.length, approved, active };
                });

                content.innerHTML = `
                    <div class="row">
                        <div class="col-md-6">
                            <h6>工序统计</h6>
                            <div class="table-responsive">
                                <table class="table table-sm">
                                    <thead>
                                        <tr>
                                            <th>工序</th>
                                            <th>总数</th>
                                            <th>已批准</th>
                                            <th>已激活</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        ${stageStats.map(stat => `
                                            <tr>
                                                <td><span class="badge bg-info">${stat.stage}</span></td>
                                                <td>${stat.total}</td>
                                                <td>${stat.approved}</td>
                                                <td>${stat.active}</td>
                                            </tr>
                                        `).join('')}
                                    </tbody>
                                </table>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <h6>审批状态分布</h6>
                            <div class="mb-2">
                                <small class="text-muted">待审批:</small>
                                <strong class="text-warning">${approvalStats.PENDING}</strong>
                            </div>
                            <div class="mb-2">
                                <small class="text-muted">已批准:</small>
                                <strong class="text-success">${approvalStats.APPROVED}</strong>
                            </div>
                            <div class="mb-2">
                                <small class="text-muted">已驳回:</small>
                                <strong class="text-danger">${approvalStats.REJECTED}</strong>
                            </div>
                            <div class="mb-2">
                                <small class="text-muted">总产品数:</small>
                                <strong>${devices.length}</strong>
                            </div>
                            <div class="mb-2">
                                <small class="text-muted">总规格数:</small>
                                <strong>${currentData.length}</strong>
                            </div>
                        </div>
                    </div>
                `;
            }, 1000);
        }

        // 工具函数
        function getApprovalText(state) {
            const stateMap = {
                'PENDING': '待审批',
                'APPROVED': '已批准',
                'REJECTED': '已驳回'
            };
            return stateMap[state] || state || '待审批';
        }

        function formatDateTime(dateStr) {
            if (!dateStr) return '';
            try {
                return new Date(dateStr).toLocaleString('zh-CN');
            } catch {
                return dateStr;
            }
        }

        function showLoading(show) {
            document.getElementById('loadingOverlay').style.display = show ? 'flex' : 'none';
        }

        function showSuccess(message) {
            alert('✅ ' + message);
        }

        function showError(message) {
            alert('❌ ' + message);
        }
    </script>
</body>
</html>
