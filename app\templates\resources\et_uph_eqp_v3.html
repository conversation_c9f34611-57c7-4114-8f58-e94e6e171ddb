<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>UPH设备管理 v3 - APS平台</title>
    <link href="{{ url_for('static', filename='vendor/bootstrap/bootstrap.min.css') }}" rel="stylesheet">
    <link href="{{ url_for('static', filename='vendor/fontawesome/css/all.min.css') }}" rel="stylesheet">
    <style>
        .page-header {
            background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
            color: white;
            padding: 2rem 0;
            margin-bottom: 2rem;
        }
        .feature-card {
            border: 1px solid #ddd;
            border-radius: 8px;
            padding: 20px;
            margin-bottom: 20px;
            background: white;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        .search-toolbar {
            background: #f8f9fa;
            border-radius: 8px;
            padding: 15px;
            margin-bottom: 20px;
        }
        .table-container {
            background: white;
            border-radius: 8px;
            overflow: hidden;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        .data-table {
            margin: 0;
        }
        .uph-badge {
            padding: 4px 8px;
            border-radius: 4px;
            font-size: 12px;
            font-weight: bold;
        }
        .uph-high { background-color: #28a745; color: #fff; }
        .uph-medium { background-color: #ffc107; color: #000; }
        .uph-low { background-color: #dc3545; color: #fff; }
        .pagination-container {
            background: white;
            padding: 15px;
            border-top: 1px solid #dee2e6;
        }
        .stats-cards {
            margin-bottom: 20px;
        }
        .stats-card {
            background: white;
            border-radius: 8px;
            padding: 20px;
            text-align: center;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        .stats-number {
            font-size: 2rem;
            font-weight: bold;
            margin-bottom: 5px;
        }
        .stats-label {
            color: #6c757d;
            font-size: 14px;
        }
        .loading-overlay {
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: rgba(255,255,255,0.8);
            display: flex;
            align-items: center;
            justify-content: center;
            z-index: 1000;
        }
        .sort-header {
            cursor: pointer;
            user-select: none;
        }
        .sort-header:hover {
            background-color: #f8f9fa;
        }
        .sort-indicator {
            margin-left: 5px;
            opacity: 0.5;
        }
        .sort-active {
            opacity: 1;
        }
        .device-cell {
            font-family: 'Monaco', 'Consolas', monospace;
            font-weight: bold;
            color: #0066cc;
        }
        .uph-cell {
            font-weight: bold;
            text-align: right;
        }
    </style>
</head>
<body>
    <!-- 页面头部 -->
    <div class="page-header">
        <div class="container-fluid">
            <div class="row align-items-center">
                <div class="col-md-8">
                    <h1 class="mb-1">
                        <i class="fas fa-tachometer-alt me-2"></i>UPH设备管理 v3
                    </h1>
                    <p class="mb-0 opacity-75">基于API v3的现代化UPH（每小时产出）管理系统</p>
                </div>
                <div class="col-md-4 text-end">
                    <span class="badge bg-success fs-6">API v3</span>
                    <span class="badge bg-info fs-6 ms-2">实时数据</span>
                </div>
            </div>
        </div>
    </div>

    <div class="container-fluid">
        <!-- 统计卡片 -->
        <div class="row stats-cards">
            <div class="col-md-3">
                <div class="stats-card">
                    <div class="stats-number text-success" id="totalDevices">0</div>
                    <div class="stats-label">设备总数</div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="stats-card">
                    <div class="stats-number text-primary" id="avgUph">0</div>
                    <div class="stats-label">平均UPH</div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="stats-card">
                    <div class="stats-number text-warning" id="totalStages">0</div>
                    <div class="stats-label">工序数量</div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="stats-card">
                    <div class="stats-number text-info" id="totalHandlers">0</div>
                    <div class="stats-label">分选机数量</div>
                </div>
            </div>
        </div>

        <!-- 操作工具栏 -->
        <div class="search-toolbar">
            <div class="row align-items-center">
                <div class="col-md-4">
                    <div class="input-group">
                        <span class="input-group-text">
                            <i class="fas fa-search"></i>
                        </span>
                        <input type="text" class="form-control" id="globalSearch" 
                               placeholder="搜索产品型号、工序、分选机...">
                        <button class="btn btn-success" onclick="performSearch()">搜索</button>
                    </div>
                </div>
                <div class="col-md-2">
                    <select class="form-select" id="stageFilter">
                        <option value="">所有工序</option>
                        <option value="FT">FT</option>
                        <option value="CP">CP</option>
                        <option value="SORT">SORT</option>
                    </select>
                </div>
                <div class="col-md-2">
                    <select class="form-select" id="perPage">
                        <option value="25">25条/页</option>
                        <option value="50" selected>50条/页</option>
                        <option value="100">100条/页</option>
                    </select>
                </div>
                <div class="col-md-4">
                    <div class="btn-group" role="group">
                        <button class="btn btn-success" onclick="refreshData()">
                            <i class="fas fa-sync me-1"></i>刷新
                        </button>
                        <button class="btn btn-info" onclick="exportData()">
                            <i class="fas fa-download me-1"></i>导出
                        </button>
                        <button class="btn btn-warning" onclick="addNewRecord()">
                            <i class="fas fa-plus me-1"></i>新增
                        </button>
                        <button class="btn btn-primary" onclick="showUphAnalysis()">
                            <i class="fas fa-chart-bar me-1"></i>UPH分析
                        </button>
                    </div>
                </div>
            </div>
        </div>

        <!-- 数据表格 -->
        <div class="table-container position-relative">
            <div id="loadingOverlay" class="loading-overlay" style="display: none;">
                <div class="text-center">
                    <div class="spinner-border text-success" role="status">
                        <span class="visually-hidden">加载中...</span>
                    </div>
                    <div class="mt-2">正在加载数据...</div>
                </div>
            </div>
            
            <div class="table-responsive">
                <table class="table table-hover data-table" id="dataTable">
                    <thead class="table-light">
                        <tr>
                            <th class="sort-header" onclick="sortBy('id')">
                                ID <i class="fas fa-sort sort-indicator"></i>
                            </th>
                            <th class="sort-header" onclick="sortBy('DEVICE')">
                                产品型号 <i class="fas fa-sort sort-indicator"></i>
                            </th>
                            <th class="sort-header" onclick="sortBy('PKG_PN')">
                                封装型号 <i class="fas fa-sort sort-indicator"></i>
                            </th>
                            <th class="sort-header" onclick="sortBy('STAGE')">
                                工序 <i class="fas fa-sort sort-indicator"></i>
                            </th>
                            <th class="sort-header" onclick="sortBy('UPH')">
                                UPH <i class="fas fa-sort sort-indicator"></i>
                            </th>
                            <th class="sort-header" onclick="sortBy('HANDLER')">
                                分选机 <i class="fas fa-sort sort-indicator"></i>
                            </th>
                            <th class="sort-header" onclick="sortBy('FAC_ID')">
                                工厂ID <i class="fas fa-sort sort-indicator"></i>
                            </th>
                            <th class="sort-header" onclick="sortBy('updated_at')">
                                更新时间 <i class="fas fa-sort sort-indicator"></i>
                            </th>
                            <th>操作</th>
                        </tr>
                    </thead>
                    <tbody id="tableBody">
                        <tr>
                            <td colspan="9" class="text-center py-4">
                                <i class="fas fa-spinner fa-spin me-2"></i>正在加载数据...
                            </td>
                        </tr>
                    </tbody>
                </table>
            </div>
            
            <!-- 分页导航 -->
            <div class="pagination-container">
                <div class="row align-items-center">
                    <div class="col-md-6">
                        <div id="dataInfo" class="text-muted">
                            显示第 1-50 条，共 0 条记录
                        </div>
                    </div>
                    <div class="col-md-6">
                        <nav>
                            <ul class="pagination justify-content-end mb-0" id="pagination">
                                <!-- 分页按钮将通过JavaScript生成 -->
                            </ul>
                        </nav>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- 编辑模态框 -->
    <div class="modal fade" id="editModal" tabindex="-1">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">编辑UPH配置</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body">
                    <form id="editForm">
                        <input type="hidden" id="editId">
                        <div class="mb-3">
                            <label class="form-label">产品型号</label>
                            <input type="text" class="form-control" id="editDevice" required>
                        </div>
                        <div class="mb-3">
                            <label class="form-label">封装型号</label>
                            <input type="text" class="form-control" id="editPkgPn">
                        </div>
                        <div class="mb-3">
                            <label class="form-label">工序</label>
                            <select class="form-select" id="editStage" required>
                                <option value="">请选择工序</option>
                                <option value="FT">FT</option>
                                <option value="CP">CP</option>
                                <option value="SORT">SORT</option>
                            </select>
                        </div>
                        <div class="mb-3">
                            <label class="form-label">UPH（每小时产出）</label>
                            <input type="number" class="form-control" id="editUph" min="1" required>
                        </div>
                        <div class="mb-3">
                            <label class="form-label">分选机</label>
                            <input type="text" class="form-control" id="editHandler">
                        </div>
                        <div class="mb-3">
                            <label class="form-label">工厂ID</label>
                            <input type="text" class="form-control" id="editFacId" maxlength="4">
                        </div>
                    </form>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
                    <button type="button" class="btn btn-success" onclick="saveEdit()">保存</button>
                </div>
            </div>
        </div>
    </div>

    <!-- UPH分析模态框 -->
    <div class="modal fade" id="uphAnalysisModal" tabindex="-1">
        <div class="modal-dialog modal-lg">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">UPH分析报告</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body">
                    <div id="uphAnalysisContent">
                        <div class="text-center">
                            <div class="spinner-border text-success" role="status">
                                <span class="visually-hidden">分析中...</span>
                            </div>
                            <div class="mt-2">正在分析UPH数据...</div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="{{ url_for('static', filename='vendor/bootstrap/bootstrap.bundle.min.js') }}"></script>
    <script>
        // 全局变量
        const API_BASE = '/api/v3';
        const TABLE_NAME = 'et_uph_eqp';
        let currentData = [];
        let currentPage = 1;
        let totalPages = 1;
        let currentSort = { field: '', order: 'asc' };
        let currentFilters = [];

        // 页面加载时初始化
        document.addEventListener('DOMContentLoaded', function() {
            console.log('🚀 UPH设备管理v3页面加载完成');
            loadData();

            // 绑定事件
            document.getElementById('stageFilter').addEventListener('change', applyFilters);
            document.getElementById('perPage').addEventListener('change', function() {
                currentPage = 1;
                loadData();
            });

            // 搜索框回车事件
            document.getElementById('globalSearch').addEventListener('keypress', function(e) {
                if (e.key === 'Enter') {
                    performSearch();
                }
            });
        });

        // 加载数据
        async function loadData() {
            showLoading(true);

            try {
                const params = new URLSearchParams({
                    page: currentPage,
                    per_page: document.getElementById('perPage').value
                });

                // 添加搜索条件
                const searchTerm = document.getElementById('globalSearch').value;
                if (searchTerm) {
                    params.append('search', searchTerm);
                }

                // 添加排序条件
                if (currentSort.field) {
                    params.append('sort_by', currentSort.field);
                    params.append('sort_order', currentSort.order);
                }

                // 添加筛选条件
                if (currentFilters.length > 0) {
                    params.append('filters', JSON.stringify(currentFilters));
                }

                const response = await fetch(`${API_BASE}/tables/${TABLE_NAME}/data?${params}`);
                const result = await response.json();

                if (result.success) {
                    currentData = result.data;
                    totalPages = result.pages;
                    renderTable(result.data);
                    renderPagination(result);
                    updateStats(result.data);
                    updateDataInfo(result);
                } else {
                    showError('数据加载失败: ' + result.error);
                }

            } catch (error) {
                showError('请求失败: ' + error.message);
            } finally {
                showLoading(false);
            }
        }

        // 渲染表格
        function renderTable(data) {
            const tbody = document.getElementById('tableBody');

            if (!data || data.length === 0) {
                tbody.innerHTML = `
                    <tr>
                        <td colspan="9" class="text-center py-4">
                            <i class="fas fa-inbox fa-2x text-muted mb-2"></i>
                            <div>暂无数据</div>
                        </td>
                    </tr>
                `;
                return;
            }

            tbody.innerHTML = data.map(row => `
                <tr>
                    <td>${row.id || ''}</td>
                    <td class="device-cell">${row.DEVICE || ''}</td>
                    <td>${row.PKG_PN || ''}</td>
                    <td>
                        <span class="badge bg-primary">${row.STAGE || ''}</span>
                    </td>
                    <td class="uph-cell">
                        <span class="uph-badge ${getUphClass(row.UPH)}">
                            ${formatUph(row.UPH)}
                        </span>
                    </td>
                    <td>${row.HANDLER || ''}</td>
                    <td>${row.FAC_ID || ''}</td>
                    <td>${formatDateTime(row.updated_at)}</td>
                    <td>
                        <div class="btn-group btn-group-sm">
                            <button class="btn btn-outline-primary" onclick="editRecord(${row.id})" title="编辑">
                                <i class="fas fa-edit"></i>
                            </button>
                            <button class="btn btn-outline-danger" onclick="deleteRecord(${row.id})" title="删除">
                                <i class="fas fa-trash"></i>
                            </button>
                        </div>
                    </td>
                </tr>
            `).join('');
        }

        // 渲染分页
        function renderPagination(result) {
            const pagination = document.getElementById('pagination');
            const totalPages = result.pages;
            const currentPageNum = currentPage;

            let html = '';

            // 上一页
            html += `
                <li class="page-item ${currentPageNum <= 1 ? 'disabled' : ''}">
                    <a class="page-link" href="#" onclick="goToPage(${currentPageNum - 1})">
                        <i class="fas fa-chevron-left"></i>
                    </a>
                </li>
            `;

            // 页码
            const startPage = Math.max(1, currentPageNum - 2);
            const endPage = Math.min(totalPages, currentPageNum + 2);

            if (startPage > 1) {
                html += `<li class="page-item"><a class="page-link" href="#" onclick="goToPage(1)">1</a></li>`;
                if (startPage > 2) {
                    html += `<li class="page-item disabled"><span class="page-link">...</span></li>`;
                }
            }

            for (let i = startPage; i <= endPage; i++) {
                html += `
                    <li class="page-item ${i === currentPageNum ? 'active' : ''}">
                        <a class="page-link" href="#" onclick="goToPage(${i})">${i}</a>
                    </li>
                `;
            }

            if (endPage < totalPages) {
                if (endPage < totalPages - 1) {
                    html += `<li class="page-item disabled"><span class="page-link">...</span></li>`;
                }
                html += `<li class="page-item"><a class="page-link" href="#" onclick="goToPage(${totalPages})">${totalPages}</a></li>`;
            }

            // 下一页
            html += `
                <li class="page-item ${currentPageNum >= totalPages ? 'disabled' : ''}">
                    <a class="page-link" href="#" onclick="goToPage(${currentPageNum + 1})">
                        <i class="fas fa-chevron-right"></i>
                    </a>
                </li>
            `;

            pagination.innerHTML = html;
        }

        // 更新统计信息
        function updateStats(data) {
            const totalDevices = new Set(data.map(row => row.DEVICE)).size;
            const totalStages = new Set(data.map(row => row.STAGE)).size;
            const totalHandlers = new Set(data.map(row => row.HANDLER)).size;

            const uphValues = data.map(row => parseInt(row.UPH) || 0).filter(uph => uph > 0);
            const avgUph = uphValues.length > 0 ? Math.round(uphValues.reduce((a, b) => a + b, 0) / uphValues.length) : 0;

            document.getElementById('totalDevices').textContent = totalDevices;
            document.getElementById('avgUph').textContent = avgUph.toLocaleString();
            document.getElementById('totalStages').textContent = totalStages;
            document.getElementById('totalHandlers').textContent = totalHandlers;
        }

        // 更新数据信息
        function updateDataInfo(result) {
            const start = (currentPage - 1) * parseInt(document.getElementById('perPage').value) + 1;
            const end = Math.min(start + result.data.length - 1, result.total);

            document.getElementById('dataInfo').textContent =
                `显示第 ${start}-${end} 条，共 ${result.total} 条记录`;
        }

        // 执行搜索
        function performSearch() {
            currentPage = 1;
            loadData();
        }

        // 应用筛选
        function applyFilters() {
            const stageFilter = document.getElementById('stageFilter').value;

            currentFilters = [];
            if (stageFilter) {
                currentFilters.push({
                    field: 'STAGE',
                    operator: 'equals',
                    value: stageFilter
                });
            }

            currentPage = 1;
            loadData();
        }

        // 排序
        function sortBy(field) {
            if (currentSort.field === field) {
                currentSort.order = currentSort.order === 'asc' ? 'desc' : 'asc';
            } else {
                currentSort.field = field;
                currentSort.order = 'asc';
            }

            // 更新排序指示器
            updateSortIndicators();

            currentPage = 1;
            loadData();
        }

        // 更新排序指示器
        function updateSortIndicators() {
            document.querySelectorAll('.sort-indicator').forEach(indicator => {
                indicator.className = 'fas fa-sort sort-indicator';
            });

            if (currentSort.field) {
                const header = document.querySelector(`th[onclick="sortBy('${currentSort.field}')"] .sort-indicator`);
                if (header) {
                    header.className = `fas fa-sort-${currentSort.order === 'asc' ? 'up' : 'down'} sort-indicator sort-active`;
                }
            }
        }

        // 跳转页面
        function goToPage(page) {
            if (page >= 1 && page <= totalPages && page !== currentPage) {
                currentPage = page;
                loadData();
            }
        }

        // 刷新数据
        function refreshData() {
            loadData();
        }

        // 导出数据
        function exportData() {
            const params = new URLSearchParams({
                format: 'excel'
            });

            const searchTerm = document.getElementById('globalSearch').value;
            if (searchTerm) {
                params.append('search', searchTerm);
            }

            if (currentSort.field) {
                params.append('sort_by', currentSort.field);
                params.append('sort_order', currentSort.order);
            }

            if (currentFilters.length > 0) {
                params.append('filters', JSON.stringify(currentFilters));
            }

            const exportUrl = `${API_BASE}/tables/${TABLE_NAME}/export?${params}`;

            const link = document.createElement('a');
            link.href = exportUrl;
            link.style.display = 'none';
            document.body.appendChild(link);
            link.click();
            document.body.removeChild(link);
        }

        // 新增记录
        function addNewRecord() {
            // 清空表单
            document.getElementById('editForm').reset();
            document.getElementById('editId').value = '';
            document.querySelector('#editModal .modal-title').textContent = '新增UPH配置';

            // 显示模态框
            new bootstrap.Modal(document.getElementById('editModal')).show();
        }

        // 编辑记录
        function editRecord(id) {
            const record = currentData.find(item => item.id == id);
            if (!record) return;

            // 填充表单
            document.getElementById('editId').value = record.id;
            document.getElementById('editDevice').value = record.DEVICE || '';
            document.getElementById('editPkgPn').value = record.PKG_PN || '';
            document.getElementById('editStage').value = record.STAGE || '';
            document.getElementById('editUph').value = record.UPH || '';
            document.getElementById('editHandler').value = record.HANDLER || '';
            document.getElementById('editFacId').value = record.FAC_ID || '';

            document.querySelector('#editModal .modal-title').textContent = '编辑UPH配置';

            // 显示模态框
            new bootstrap.Modal(document.getElementById('editModal')).show();
        }

        // 保存编辑
        async function saveEdit() {
            const id = document.getElementById('editId').value;
            const data = {
                DEVICE: document.getElementById('editDevice').value,
                PKG_PN: document.getElementById('editPkgPn').value,
                STAGE: document.getElementById('editStage').value,
                UPH: document.getElementById('editUph').value,
                HANDLER: document.getElementById('editHandler').value,
                FAC_ID: document.getElementById('editFacId').value
            };

            try {
                let response;
                if (id) {
                    // 更新
                    response = await fetch(`${API_BASE}/tables/${TABLE_NAME}/data/${id}`, {
                        method: 'PUT',
                        headers: { 'Content-Type': 'application/json' },
                        body: JSON.stringify(data)
                    });
                } else {
                    // 新增
                    response = await fetch(`${API_BASE}/tables/${TABLE_NAME}/data`, {
                        method: 'POST',
                        headers: { 'Content-Type': 'application/json' },
                        body: JSON.stringify(data)
                    });
                }

                const result = await response.json();

                if (result.success) {
                    bootstrap.Modal.getInstance(document.getElementById('editModal')).hide();
                    showSuccess(id ? '更新成功' : '新增成功');
                    loadData();
                } else {
                    showError('保存失败: ' + result.error);
                }

            } catch (error) {
                showError('请求失败: ' + error.message);
            }
        }

        // 删除记录
        async function deleteRecord(id) {
            if (!confirm('确定要删除这条记录吗？')) return;

            try {
                const response = await fetch(`${API_BASE}/tables/${TABLE_NAME}/data/${id}`, {
                    method: 'DELETE'
                });

                const result = await response.json();

                if (result.success) {
                    showSuccess('删除成功');
                    loadData();
                } else {
                    showError('删除失败: ' + result.error);
                }

            } catch (error) {
                showError('请求失败: ' + error.message);
            }
        }

        // 显示UPH分析
        function showUphAnalysis() {
            const modal = new bootstrap.Modal(document.getElementById('uphAnalysisModal'));
            modal.show();

            // 生成分析报告
            generateUphAnalysis();
        }

        // 生成UPH分析报告
        function generateUphAnalysis() {
            const content = document.getElementById('uphAnalysisContent');

            setTimeout(() => {
                const uphValues = currentData.map(row => parseInt(row.UPH) || 0).filter(uph => uph > 0);
                const stages = [...new Set(currentData.map(row => row.STAGE))];
                const devices = [...new Set(currentData.map(row => row.DEVICE))];

                const stageStats = stages.map(stage => {
                    const stageData = currentData.filter(row => row.STAGE === stage);
                    const stageUph = stageData.map(row => parseInt(row.UPH) || 0).filter(uph => uph > 0);
                    const avgUph = stageUph.length > 0 ? Math.round(stageUph.reduce((a, b) => a + b, 0) / stageUph.length) : 0;
                    return { stage, count: stageData.length, avgUph };
                });

                content.innerHTML = `
                    <div class="row">
                        <div class="col-md-6">
                            <h6>工序统计</h6>
                            <div class="table-responsive">
                                <table class="table table-sm">
                                    <thead>
                                        <tr>
                                            <th>工序</th>
                                            <th>数量</th>
                                            <th>平均UPH</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        ${stageStats.map(stat => `
                                            <tr>
                                                <td><span class="badge bg-primary">${stat.stage}</span></td>
                                                <td>${stat.count}</td>
                                                <td>${stat.avgUph.toLocaleString()}</td>
                                            </tr>
                                        `).join('')}
                                    </tbody>
                                </table>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <h6>UPH分布</h6>
                            <div class="mb-2">
                                <small class="text-muted">最高UPH:</small>
                                <strong class="text-success">${Math.max(...uphValues).toLocaleString()}</strong>
                            </div>
                            <div class="mb-2">
                                <small class="text-muted">最低UPH:</small>
                                <strong class="text-danger">${Math.min(...uphValues).toLocaleString()}</strong>
                            </div>
                            <div class="mb-2">
                                <small class="text-muted">平均UPH:</small>
                                <strong class="text-primary">${Math.round(uphValues.reduce((a, b) => a + b, 0) / uphValues.length).toLocaleString()}</strong>
                            </div>
                            <div class="mb-2">
                                <small class="text-muted">总设备数:</small>
                                <strong>${devices.length}</strong>
                            </div>
                        </div>
                    </div>
                `;
            }, 1000);
        }

        // 工具函数
        function getUphClass(uph) {
            const uphValue = parseInt(uph) || 0;
            if (uphValue >= 2000) return 'uph-high';
            if (uphValue >= 1000) return 'uph-medium';
            return 'uph-low';
        }

        function formatUph(uph) {
            const uphValue = parseInt(uph) || 0;
            return uphValue.toLocaleString();
        }

        function formatDateTime(dateStr) {
            if (!dateStr) return '';
            try {
                return new Date(dateStr).toLocaleString('zh-CN');
            } catch {
                return dateStr;
            }
        }

        function showLoading(show) {
            document.getElementById('loadingOverlay').style.display = show ? 'flex' : 'none';
        }

        function showSuccess(message) {
            alert('✅ ' + message);
        }

        function showError(message) {
            alert('❌ ' + message);
        }
    </script>
</body>
</html>
